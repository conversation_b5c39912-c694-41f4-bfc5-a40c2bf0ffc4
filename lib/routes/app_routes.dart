// lib/routes/app_routes.dart
import 'package:get/get.dart';
import 'package:fila_app/bindings/confirmacao_saida_binding.dart';
import 'package:fila_app/bindings/fila_paciente_binding.dart';
import 'package:fila_app/bindings/paciente_binding.dart';
import 'package:fila_app/bindings/solicitacoes_binding.dart';
import 'package:fila_app/bindings/fila_atendimento_binding.dart';
import 'package:fila_app/bindings/hospitais_habilitados_binding.dart';
import 'package:fila_app/bindings/secretaria_binding.dart';
import 'package:fila_app/modules/admin/bidings/admin_binding.dart';
import 'package:fila_app/bindings/cadastro_secretaria_binding.dart';
import 'package:fila_app/controllers/login_controller.dart';
import 'package:fila_app/views/tela_inicial.dart';
import 'package:fila_app/views/tela_de_ajudas.dart';
import 'package:fila_app/views/tela_login.dart';
import 'package:fila_app/views/tela_primeiro_acesso.dart';
import 'package:fila_app/views/tela_inicial_paciente.dart';
import 'package:fila_app/views/tela_carregando_paciente.dart';
import 'package:fila_app/views/tela_esqueceu_senha.dart';
import 'package:fila_app/views/tela_fila_paciente.dart';
import 'package:fila_app/views/tela_localizacao_paciente.dart';
import 'package:fila_app/views/tela_confirmacao_sair_fila_paciente.dart';
import 'package:fila_app/views/tela_home_medico.dart';
import 'package:fila_app/views/tela_home_hospital.dart';
import 'package:fila_app/views/tela_cadastro.dart';
import 'package:fila_app/views/tela_cadastro_medico.dart';
import 'package:fila_app/views/tela_cadastro_hospital.dart';
import 'package:fila_app/views/tela_hospitais_habilitados.dart';
import 'package:fila_app/views/tela_dados_medico.dart';
import 'package:fila_app/views/tela_detalhe_ajuda.dart';
import 'package:fila_app/views/tela_dados_administrador.dart';
import 'package:fila_app/views/tela_fila_atendimento.dart';
import 'package:fila_app/views/tela_solicitacoes.dart';
import 'package:fila_app/views/tela_inserir_paciente_fila.dart';
import 'package:fila_app/views/tela_secretaria_filas.dart';
import 'package:fila_app/modules/admin/screens/admin_screen.dart';
import 'package:fila_app/views/tela_cadastro_secretaria.dart';
import 'package:fila_app/views/tela_chatbot.dart';
import 'package:fila_app/views/tela_historico_chat.dart';
import 'package:fila_app/views/tela_home_secretaria.dart';
import 'package:fila_app/views/tela_aceitar_pacientes.dart';
import 'package:fila_app/views/tela_cadastrar_medicos.dart';
import 'package:fila_app/views/tela_paciente_removido.dart';
import 'package:fila_app/views/tela_paciente_atendido.dart';

class AppRoutes {
  static List<GetPage> getPages(
      {required String initialRoute, Map<String, dynamic>? initialFilaState}) {
    return [
      GetPage(
        name: '/',
        page: () {
          // Se há um forceInitial nos argumentos, sempre mostra a tela inicial
          if (Get.arguments != null && Get.arguments['forceInitial'] == true) {
            return const TelaInicial();
          }
          // Caso contrário, usa a lógica normal do initialFilaState
          return initialFilaState != null
              ? const FilaPacienteScreen()
              : const TelaInicial();
        },
        binding: initialFilaState != null
            ? FilaPacienteBinding(initialFilaState: initialFilaState)
            : null,
      ),
      GetPage(name: '/tela_inicial', page: () => const TelaInicial()),
      GetPage(name: '/ajuda', page: () => TelaDeAjudas()),
      GetPage(
        name: '/login',
        page: () => TelaLogin(),
        binding: BindingsBuilder(() {
          Get.lazyPut<LoginController>(() => LoginController());
        }),
      ),
      GetPage(name: '/primeiroAcesso', page: () => PrimeiroAcessoScreen()),
      GetPage(
        name: '/paciente',
        page: () => TelaInicialPaciente(),
        binding: PacienteBinding(),
      ),
      GetPage(name: '/esqueceu_senha', page: () => EsqueceuSenha()),
      GetPage(
        name: '/carregando',
        page: () => TelaCarregandoPaciente(
          solicitacaoId: Get.arguments['solicitacaoId'] ?? '',
          medicoId: Get.arguments['medicoId'] ?? '',
        ),
      ),
      GetPage(
        name: '/fila_paciente',
        page: () => const FilaPacienteScreen(),
        binding: FilaPacienteBinding(),
      ),
      GetPage(name: '/localizacao', page: () => LocalizacaoPacienteScreen()),
      GetPage(name: '/medico', page: () => const TelaMedicoScreen()),
      GetPage(name: '/hospital', page: () => const TelaHospitalScreen()),
      GetPage(name: '/tipo_de_cadastro', page: () => const TelaDeCadastro()),
      GetPage(name: '/cadastroMedico', page: () => CadastroMedicoScreen()),
      GetPage(name: '/cadastroHospital', page: () => CadastroHospitalScreen()),
      GetPage(
        name: '/hospitais_habilitados',
        page: () => const TelaHospitaisHabilitados(),
        binding: HospitaisHabilitadosBinding(),
      ),
      GetPage(name: '/perfilMedico', page: () => TelaPerfilMedico()),
      GetPage(
        name: '/detalhe_ajuda',
        page: () =>
            TelaDetalheAjuda(title: Get.arguments['title'] ?? 'Detalhes'),
      ),
      GetPage(
          name: '/tela_dados_administrador',
          page: () => TelaDadosAdministrador()),
      GetPage(
        name: '/fila_atendimento',
        page: () => TelaFilaAtendimento(),
        binding: FilaAtendimentoBinding(),
      ),
      GetPage(
        name: '/solicitacoes',
        page: () => const TelaSolicitacoes(),
        binding: SolicitacoesBinding(),
      ),
      GetPage(
        name: '/inserir_paciente_fila',
        page: () => InserirPacienteFila(
          nomeMedico: Get.arguments['nomeMedico'] ?? '',
          especialidade: Get.arguments['especialidade'] ?? '',
          id: Get.arguments['id'] ?? '',
          solicitacaoId: Get.arguments['solicitacaoId'] ?? '',
          idMedico: Get.arguments['idMedico'] ?? '',
        ),
      ),
      GetPage(
        name: '/confirmacao_sair_fila',
        page: () => const ConfirmacaoSairFilaPacienteScreen(),
        binding: ConfirmacaoSaidaBinding(),
      ),
      GetPage(
        name: '/secretaria',
        page: () => const TelaSecretariaFilas(),
        binding: SecretariaBinding(),
      ),
      GetPage(
        name: '/home_secretaria',
        page: () => const TelaHomeSecretaria(),
        binding: SecretariaBinding(),
      ),
      GetPage(
        name: '/aceitar_pacientes',
        page: () => const TelaAceitarPacientes(),
        binding: SecretariaBinding(),
      ),
      GetPage(
        name: '/cadastrar_medicos',
        page: () => const TelaCadastrarMedicos(),
        binding: SecretariaBinding(),
      ),
      GetPage(
        name: '/admin_hospitais',
        page: () => const TelaAdminHospitais(),
        binding: AdminBinding(),
      ),
      GetPage(
        name: '/cadastro_secretaria',
        page: () => const TelaCadastroSecretaria(),
        binding: CadastroSecretariaBinding(),
      ),
      GetPage(
        name: '/paciente_removido',
        page: () => const TelaPacienteRemovido(),
      ),
      GetPage(
        name: '/paciente_atendido',
        page: () => const TelaPacienteAtendido(),
      ),
      // ✅ ROTAS CORRETAS PARA REDIRECIONAMENTO
      GetPage(
        name: '/tela_paciente_removido',
        page: () => const TelaPacienteRemovido(),
      ),
      GetPage(
        name: '/tela_paciente_atendido',
        page: () => const TelaPacienteAtendido(),
      ),
      GetPage(
        name: '/chatbot',
        page: () => TelaChatbot(),
      ),
      GetPage(
        name: '/chat-history',
        page: () => TelaHistoricoChat(),
      ),
    ];
  }
}
