import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../controllers/user_data_controller.dart';
import '../controllers/fila_paciente_controller.dart';
import '../firebase_options.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  print('Mensagem em background recebida: ${message.notification?.title}');
  print('Dados: ${message.data}');
}

class PushNotificationService extends GetxService {
  static const String tag = '[PUSH_NOTIFICATIONS]';
  final FirebaseMessaging _fcm = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  // Canal Android para notificações
  static const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'saude_sem_espera_channel', // ID
    'Notificações Saúde Sem Espera', // Nome
    description: 'Canal para notificações do Saúde Sem Espera',
    importance: Importance.high,
    playSound: true,
    enableVibration: true,
  );

  // Chave para armazenar a data do último registro
  static const String _lastRegisterTimeKey = 'last_push_register_time';

  // Chave para armazenar a data da última verificação de token
  static const String _lastTokenVerificationKey =
      'last_token_verification_time';

  // Intervalo mínimo entre registros (7 dias em milissegundos) - reduzir requisições
  static const int _registerIntervalMs = 7 * 24 * 60 * 60 * 1000; // 7 dias

  // Intervalo para verificação de token (3 dias em milissegundos) - otimizado para 1000 usuários
  static const int _tokenVerificationIntervalMs =
      3 * 24 * 60 * 60 * 1000; // 3 dias

  // Status de registro e token atual
  final RxBool isRegistering = false.obs;
  final RxBool isRegistered = false.obs;
  final RxString currentToken = ''.obs;

  // Controle para verificação única
  final bool _verificacaoUnicaRealizada = false;

  // Timer para registro periódico (apenas uma vez por dia)
  Timer? _registrationTimer;
  Timer? _tokenVerificationTimer; // Timer para verificação periódica do token

  // Flag para controlar logs excessivos
  bool _logSilentMode = false;

  // Flag para controlar permissões (evitar solicitação dupla)
  bool _permissionsRequested = false;

  // Cache local para reduzir requisições ao servidor
  final Map<String, dynamic> _localCache = {};
  DateTime? _lastCacheUpdate;

  // Método público para verificar e solicitar permissões de notificação
  Future<NotificationSettings> checkAndRequestPermissions() async {
    print('🔒 Verificando e solicitando permissões de notificação...');

    // Verificar permissões atuais primeiro
    NotificationSettings settings = await _fcm.getNotificationSettings();

    print('📱 Status atual de permissão: ${settings.authorizationStatus}');

    // Se não tiver autorização completa E ainda não foi solicitado, solicitar
    if (settings.authorizationStatus != AuthorizationStatus.authorized &&
        !_permissionsRequested) {
      settings = await _requestPermissionsOnce();
    }

    return settings;
  }

  Future<void> init() async {
    print('🔔 [DEBUG] Inicializando serviço de notificações...');

    try {
      // Garantir que o Firebase está inicializado
      if (Firebase.apps.isEmpty) {
        print('🔥 [DEBUG] Inicializando Firebase...');
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
        print('✅ [DEBUG] Firebase inicializado!');
      } else {
        print('✅ [DEBUG] Firebase já estava inicializado!');
      }

      // Registrar handler para mensagens em background
      FirebaseMessaging.onBackgroundMessage(
          _firebaseMessagingBackgroundHandler);
      print('✅ [DEBUG] Background handler registrado');

      // Configurar notificações locais
      print('📱 [DEBUG] Configurando notificações locais...');
      await _setupLocalNotifications();
      print('✅ [DEBUG] Notificações locais configuradas');

      // Solicitar permissões UMA ÚNICA VEZ
      print('🔒 [DEBUG] Solicitando permissões...');
      final permissions = await _requestPermissionsOnce();
      print(
          '✅ [DEBUG] Permissões solicitadas. Status: ${permissions.authorizationStatus}');

      // Configurar handlers de mensagens
      print('📥 [DEBUG] Configurando handlers de mensagens...');
      _setupMessageHandlers();
      print('✅ [DEBUG] Handlers configurados');

      // Obter o token FCM
      print('🔑 [DEBUG] Obtendo token FCM...');
      await _getToken();
      if (currentToken.value.isNotEmpty) {
        print(
            '✅ [DEBUG] Token FCM obtido: ${currentToken.value.substring(0, 50)}...');
      } else {
        print('❌ [DEBUG] Falha ao obter token FCM!');
      }

      // Iniciar timer de verificação de token apenas (sem verificação de login periódica)
      // O registro será feito principalmente após login bem-sucedido
      _startRegistrationTimer();
      _startTokenVerificationTimer();

      // Habilitar cache local para otimização
      await enableLocalCache();

      print('✅ [DEBUG] Serviço de notificações inicializado com sucesso');

      // Test notification setup removed - using real queue notifications now
    } catch (e) {
      print('❌ [DEBUG] Erro ao inicializar serviço de notificações: $e');
      print('❌ [DEBUG] Stack trace: ${StackTrace.current}');
    }
  }

  Future<void> initialize() async => init();

  void _startRegistrationTimer() {
    _registrationTimer?.cancel();
    // Aumentar intervalo para 24 horas (era 1 hora) para reduzir significativamente as verificações
    _registrationTimer = Timer.periodic(const Duration(hours: 24), (_) {
      // Usar verificação inteligente que só registra quando necessário
      _logSilentMode = true;
      _forceRegisterIfNeeded();
      _logSilentMode = false;
    });
  }

  // Remover completamente o timer de verificação de login
  // A verificação agora acontecerá apenas quando o usuário fizer login

  // Nova função para iniciar o timer de verificação de token
  void _startTokenVerificationTimer() {
    _tokenVerificationTimer?.cancel();
    // Aumentar intervalo para 24 horas (era 6 horas) para economizar recursos
    _tokenVerificationTimer = Timer.periodic(const Duration(hours: 24), (_) {
      _logSilentMode = true;
      _verifyTokenConsistency();
      _logSilentMode = false;
    });
  }

  Future<void> _setupLocalNotifications() async {
    print('Configurando notificações locais...');

    // Configurações para Android
    const AndroidInitializationSettings androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // Configurações para iOS (sem solicitar permissões aqui para evitar duplicação)
    const DarwinInitializationSettings iosSettings =
        DarwinInitializationSettings(
      requestSoundPermission: false,
      requestBadgePermission: false,
      requestAlertPermission: false,
    );

    // Configurações gerais
    const InitializationSettings initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    // Inicializar plugin
    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onLocalNotificationTap,
    );

    // Criar o canal de notificações no Android
    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    print('✅ Canal de notificações configurado: ${channel.id}');
  }

  // Nova função para solicitar permissões apenas uma vez
  Future<NotificationSettings> _requestPermissionsOnce() async {
    if (_permissionsRequested) {
      print('🔒 Permissões já foram solicitadas, retornando status atual...');
      return await _fcm.getNotificationSettings();
    }

    _permissionsRequested = true;
    return await _requestPermissions();
  }

  Future<NotificationSettings> _requestPermissions() async {
    print('Solicitando permissões de notificação...');

    NotificationSettings settings = await _fcm.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      announcement: false,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
    );

    print('Status da permissão: ${settings.authorizationStatus}');

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      print('✅ Permissões concedidas');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      print('⚠️ Permissões provisórias concedidas');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.notDetermined) {
      print(
          '🔄 Permissões ainda não determinadas, aguardando resposta do usuário...');
      // Não mostrar notificação de erro quando não determinado
    } else {
      print('❌ Permissões negadas. Notificações não funcionarão!');
      // Mostrar notificação local apenas quando explicitamente negadas
      _showLocalNotification(
        id: 999,
        title: 'Permissões de notificação negadas',
        body:
            'Por favor, habilite as notificações nas configurações do aplicativo para receber atualizações importantes.',
      );
    }

    return settings;
  }

  void _setupMessageHandlers() {
    // 1. Mensagens com o app em foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('📥 Mensagem em foreground: ${message.notification?.title}');
      // Melhorar logs para facilitar depuração
      print('📥 FOREGROUND NOTIFICATION DETAILS:');
      print('   - Title: ${message.notification?.title}');
      print('   - Body: ${message.notification?.body}');
      print('   - Data: ${message.data}');
      print('   - MessageId: ${message.messageId}');
      print('   - Timestamp: ${DateTime.now()}');

      _processIncomingMessage(message);
    });

    // 2. Mensagens com o app aberto a partir de uma notificação
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print(
          '🔔 App aberto a partir da notificação: ${message.notification?.title}');
      // Melhorar logs para facilitar depuração
      print('🔔 NOTIFICATION OPENED APP DETAILS:');
      print('   - Title: ${message.notification?.title}');
      print('   - Body: ${message.notification?.body}');
      print('   - Data: ${message.data}');
      print('   - MessageId: ${message.messageId}');
      print('   - Timestamp: ${DateTime.now()}');

      _handleOpenedFromNotification(message);
    });

    // 3. Mensagens iniciais (quando o app é aberto por uma notificação)
    _fcm.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        print(
            '🚀 Notificação inicial processada: ${message.notification?.title}');
        // Melhorar logs para facilitar depuração
        print('🚀 INITIAL NOTIFICATION DETAILS:');
        print('   - Title: ${message.notification?.title}');
        print('   - Body: ${message.notification?.body}');
        print('   - Data: ${message.data}');
        print('   - MessageId: ${message.messageId}');

        _handleOpenedFromNotification(message);
      }
    });

    print('✅ Handlers de mensagens configurados');
  }

  Future<void> _getToken() async {
    try {
      print('🔑 Obtendo token FCM...');
      String? token = await _fcm.getToken();

      if (token != null) {
        currentToken.value = token;
        print('✅ Token FCM obtido: ${token.substring(0, 15)}...');

        // Configurar listener para atualizações de token
        _fcm.onTokenRefresh.listen((newToken) {
          print('🔄 Token FCM atualizado automaticamente!');
          currentToken.value = newToken;
          // Se o usuário estiver logado, registrar o novo token imediatamente
          _checkUserAndRegisterOnTokenRefresh(newToken);
        });
      } else {
        print('❌ Não foi possível obter o token FCM');
      }
    } catch (e) {
      print('❌ Erro ao obter token FCM: $e');
    }
  }

  // Nova função para registrar token quando ele for automaticamente atualizado pelo FCM
  Future<void> _checkUserAndRegisterOnTokenRefresh(String newToken) async {
    try {
      // Verificar se o usuário está logado
      UserDataController? userDataController;
      try {
        userDataController = Get.find<UserDataController>();
        if (userDataController.userDataExists.value) {
          print(
              '✅ Token atualizado pelo FCM, registrando novo token no servidor...');
          await _registerTokenWithServer(newToken);
          await _saveLastRegisterTime();
        }
      } catch (e) {
        print('⚠️ Usuário não logado, token será registrado no próximo login');
      }
    } catch (e) {
      print('❌ Erro ao registrar token atualizado: $e');
    }
  }

  // Verificação sob demanda, não mais periódica
  Future<void> _checkUserAndRegister() async {
    // Se já está registrando, não iniciar outro registro
    if (isRegistering.value) {
      if (!_logSilentMode) {
        print('⚠️ Já existe um registro em andamento, ignorando...');
      }
      return;
    }

    try {
      // Verificar se o usuário está logado sem depender de intervalos
      // Verificar se temos um UserDataController com dados
      UserDataController? userDataController;
      try {
        userDataController = Get.find<UserDataController>();
        if (userDataController.userDataExists.value) {
          if (!_logSilentMode) {
            print(
                '✅ Usuário logado detectado, registrando para notificações...');
          }
          if (currentToken.value.isNotEmpty) {
            await _registerTokenWithServer(currentToken.value);
            // Registrar a data/hora atual como último registro
            await _saveLastRegisterTime();
            print('✅ Registro de notificações realizado com sucesso.');
          }
        } else {
          if (!_logSilentMode) {
            print(
                '⚠️ UserDataController encontrado, mas usuário não está logado ou cadastrado');
          }
        }
      } catch (e) {
        if (!_logSilentMode) {
          print('⚠️ UserDataController não disponível: $e');
        }
      }
    } catch (e) {
      if (!_logSilentMode) {
        print('❌ Erro ao verificar usuário: $e');
      }
    }
  }

  Future<void> _registerTokenWithServer(String token) async {
    if (isRegistering.value) return;
    isRegistering.value = true;

    print('📤 Registrando token no Parse Server...');

    try {
      // Garantir que temos um UserDataController acessível
      UserDataController? userDataController;
      try {
        // Tentar obter o controller já registrado
        userDataController = Get.find<UserDataController>();
        if (!_logSilentMode) {
          print('✅ UserDataController encontrado no GetX');
        }
      } catch (e) {
        if (!_logSilentMode) {
          print(
              '⚠️ UserDataController não encontrado via GetX, verificando se temos dados no dispositivo...');
        }

        // Se não encontrarmos pelo GetX, criar uma instância temporária para verificar os dados
        userDataController = UserDataController();
        await userDataController.checkUserData();
      }

      // Obter dados do usuário
      final userData = await userDataController.getUserData();

      if (userData == null || !userData.containsKey('userId')) {
        print(
            '❌ Dados do usuário indisponíveis, não é possível registrar o token');
        isRegistered.value = false;

        // Notificação de erro ainda é útil, mantemos apenas esta
        if (!isRegistering.value && !_logSilentMode) {
          _showLocalNotification(
            id: 1000,
            title: 'Erro ao configurar notificações',
            body:
                'Por favor, faça login para receber notificações sobre sua posição na fila.',
          );
        }
        return;
      }

      // Preparar dados para registro
      final String deviceId = userData['userId'];
      const String userType =
          'patient'; // Usar patient sempre para usuários do app

      // Lista de canais para garantir que o dispositivo receba todas as notificações relevantes
      final List<String> channels = [
        'patients', // Canal global para todos os pacientes
        'patient_$deviceId', // Canal específico para este paciente
        'global', // Canal para anúncios globais
      ];

      print('🔍 Registrando dispositivo - ID: $deviceId, Tipo: $userType');
      print(
          '📡 Token FCM: ${token.substring(0, 15)}... (${token.length} caracteres)');
      print('📢 Canais de inscrição: $channels');

      // Parâmetros para o Cloud Function
      final params = {
        'deviceId': deviceId,
        'userId': deviceId,
        'userType': userType,
        'token': token,
        'deviceType': GetPlatform.isIOS ? 'ios' : 'android',
        'channels': channels, // Enviando a lista de canais explicitamente
      };

      print('📡 Chamando função registerDeviceForPush...');

      // Chamar Cloud Function do Parse
      final ParseCloudFunction function =
          ParseCloudFunction('registerDeviceForPush');
      final ParseResponse response = await function.execute(parameters: params);

      if (response.success) {
        print('✅ Dispositivo registrado com sucesso!');
        print('📋 Resposta: ${response.result}');
        isRegistered.value = true;

        // Verificar registro
        final canais = response.result?['channels'];
        if (canais != null) {
          print('📢 Canais registrados: $canais');

          // Verificar se todos os canais necessários estão presentes
          bool todosCanaisRegistrados = true;
          for (final canal in channels) {
            if (!canais.contains(canal)) {
              todosCanaisRegistrados = false;
              print('⚠️ Canal não registrado: $canal');
            }
          }

          if (!todosCanaisRegistrados) {
            print(
                '⚠️ Alguns canais não foram registrados. Tentando corrigir...');
            await _adicionarCanaisFaltantes(deviceId, channels);
          } else {
            print(
                '✅ Todos os canais necessários foram registrados corretamente');
          }
        }

        // Verificar diretamente na tabela Installation
        await _verifyInstallation(deviceId, token);
      } else {
        print('❌ Erro ao registrar dispositivo: ${response.error?.message}');
        isRegistered.value = false;

        // Tentar registro direto em caso de erro
        if (response.error?.code == 141 || response.error?.code == 119) {
          print('⚠️ Erro de permissão, tentando registro direto...');
          await _createInstallationDirectly(
              token, deviceId, userType, channels);
        }
      }
    } catch (e) {
      print('❌ Exceção ao registrar token: $e');
      isRegistered.value = false;
    } finally {
      isRegistering.value = false;
    }
  }

  // Implementação melhorada para criar instalação diretamente
  Future<void> _createInstallationDirectly(String token, String deviceId,
      String userType, List<String> channels) async {
    print('🛠️ Criando instalação diretamente...');
    print('📢 Canais para registro direto: $channels');

    try {
      final installation = ParseObject('_Installation')
        ..set('deviceToken', token)
        ..set('deviceType', GetPlatform.isIOS ? 'ios' : 'android')
        ..set('installationId', deviceId)
        ..set('userId', deviceId)
        ..set('userType', userType)
        ..set('channels', channels);

      final response = await installation.save();

      if (response.success) {
        print('✅ Instalação criada diretamente com sucesso!');
        print('📋 Objeto criado: ${response.results?.first.objectId}');
        isRegistered.value = true;

        // Verificar se a instalação foi realmente criada com os canais corretos
        await _verifyInstallation(deviceId, token);
      } else {
        print('❌ Falha ao criar instalação: ${response.error?.message}');
        isRegistered.value = false;

        // Se o erro for que já existe uma instalação, tentar atualizá-la
        if (response.error?.code == 137) {
          // Código de erro para objeto já existente
          print('⚠️ Instalação já existe, tentando atualizar...');
          await _updateExistingInstallation(deviceId, token, channels);
        }
      }
    } catch (e) {
      print('❌ Erro ao criar instalação: $e');
      isRegistered.value = false;
    }
  }

  // Adicionar função para atualizar uma instalação existente
  Future<void> _updateExistingInstallation(
      String deviceId, String token, List<String> channels) async {
    try {
      print('🔄 Tentando atualizar instalação existente...');

      final installation = await _buscarInstalacaoAtual(deviceId);
      if (installation != null) {
        print('🔍 Instalação encontrada: ${installation.objectId}');

        // Atualizar o token e os canais
        installation.set('deviceToken', token);
        installation.set('channels', channels);

        final response = await installation.save();

        if (response.success) {
          print('✅ Instalação atualizada com sucesso!');
          isRegistered.value = true;
        } else {
          print('❌ Falha ao atualizar instalação: ${response.error?.message}');
          isRegistered.value = false;
        }
      } else {
        print('⚠️ Não foi possível encontrar a instalação para atualizar');
      }
    } catch (e) {
      print('❌ Erro ao atualizar instalação: $e');
    }
  }

  // Função para adicionar todos os canais faltantes
  Future<void> _adicionarCanaisFaltantes(
      String userId, List<String> canaisNecessarios) async {
    try {
      print('🔧 Tentando adicionar canais faltantes para o usuário: $userId');

      final installation = await _buscarInstalacaoAtual(userId);
      if (installation != null) {
        final List<dynamic> canaisAtuais =
            installation.get<List<dynamic>>('channels') ?? [];
        List<String> canaisFaltantes = [];

        // Verificar quais canais estão faltando
        for (final canal in canaisNecessarios) {
          if (!canaisAtuais.contains(canal)) {
            canaisFaltantes.add(canal);
          }
        }

        if (canaisFaltantes.isNotEmpty) {
          print('➕ Adicionando canais faltantes: $canaisFaltantes');

          // Adicionar os canais faltantes
          for (final canal in canaisFaltantes) {
            canaisAtuais.add(canal);
          }

          installation.set('channels', canaisAtuais);
          final response = await installation.save();

          if (response.success) {
            print('✅ Canais adicionados com sucesso: $canaisFaltantes');
          } else {
            print('❌ Falha ao adicionar canais: ${response.error?.message}');
          }
        } else {
          print('ℹ️ Todos os canais necessários já estão registrados');
        }
      } else {
        print('⚠️ Nenhuma instalação encontrada para o usuário: $userId');
      }
    } catch (e) {
      print('❌ Erro ao adicionar canais faltantes: $e');
    }
  }

  Future<void> _verifyInstallation(String deviceId, String token) async {
    print('🔍 Verificando instalação no Parse Server...');

    try {
      // Usar Cloud Function para verificar a instalação em vez de consulta direta
      // Isso evita o erro de permissão, já que a Cloud Function usa masterKey
      final ParseCloudFunction function =
          ParseCloudFunction('diagnosticarNotificacoes');
      final ParseResponse response = await function.execute(parameters: {
        'deviceId': deviceId,
        'token': token // Adicionar token para melhor diagnóstico
      });

      if (response.success && response.result != null) {
        final result = response.result;
        print('📋 Resultado do diagnóstico: $result');

        // Verificação corrigida: usar found para verificar se a instalação existe
        // e installations para acessar os detalhes da instalação
        if (result['found'] == true &&
            result['installations'] != null &&
            result['installations'].isNotEmpty) {
          final installation = result['installations'][0];
          final channels = installation['channels'];
          final deviceToken = installation['deviceToken'];
          print('✅ Instalação encontrada:');
          print('  - ID: ${installation['objectId']}');
          print('  - Token: ${deviceToken?.substring(0, 15)}...');
          print('  - Canais: $channels');

          // Verificar se o token está atualizado
          if (deviceToken != token) {
            print('⚠️ Token na instalação não corresponde ao token atual!');
            print(
                '  - Token na instalação: ${deviceToken?.substring(0, 15)}...');
            print('  - Token atual: ${token.substring(0, 15)}...');

            // Tentar atualizar o token
            await _updateTokenInInstallation(deviceId, token);
          }

          isRegistered.value = true;

          // Instalação já existe, não precisamos criar uma nova
          return;
        } else {
          print('⚠️ Instalação não encontrada no Parse Server');
          isRegistered.value = false;

          // Se não existe instalação, criar uma nova
          print('⚠️ Tentando criar uma nova instalação...');
          const String userType = 'patient';
          final List<String> channels = [
            'patients',
            'patient_$deviceId',
            'global'
          ];
          await _createInstallationDirectly(
              token, deviceId, userType, channels);
        }
      } else {
        print(
            '⚠️ Não foi possível verificar a instalação: ${response.error?.message}');
        isRegistered.value =
            true; // Assumir que está registrado, já que o register foi bem-sucedido
      }
    } catch (e) {
      print('❌ Erro ao verificar instalação: $e');
      // Não mostramos erro nem tentamos novamente, já que o registro inicial foi bem-sucedido
      isRegistered.value = true;
    }
  }

  // Função para atualizar o token em uma instalação existente
  Future<void> _updateTokenInInstallation(String deviceId, String token) async {
    try {
      print('🔄 Atualizando token na instalação...');

      final installation = await _buscarInstalacaoAtual(deviceId);
      if (installation != null) {
        print('🔍 Instalação encontrada: ${installation.objectId}');

        // Atualizar apenas o token
        installation.set('deviceToken', token);

        final response = await installation.save();

        if (response.success) {
          print('✅ Token atualizado com sucesso!');
        } else {
          print('❌ Falha ao atualizar token: ${response.error?.message}');
        }
      } else {
        print(
            '⚠️ Não foi possível encontrar a instalação para atualizar o token');
      }
    } catch (e) {
      print('❌ Erro ao atualizar token: $e');
    }
  }

  // Método para forçar a verificação e correção dos canais de inscrição
  Future<void> verificarECorrigirCanais() async {
    print('🔍 Verificando e corrigendo canais de notificação...');

    try {
      UserDataController? userDataController = Get.find<UserDataController>();
      final userData = await userDataController.getUserData();

      if (userData != null && userData.containsKey('userId')) {
        final String deviceId = userData['userId'];
        print('👤 Verificando canais para o usuário: $deviceId');

        // Usar a nova função de gerenciamento de canais
        final success = await manageChannelSubscriptions(deviceId);

        if (success) {
          print('✅ Verificação e correção de canais concluída com sucesso');
        } else {
          print('⚠️ Verificação de canais concluída com possíveis problemas');
        }
      } else {
        print(
            '❌ Dados do usuário indisponíveis, não é possível verificar canais');
      }
    } catch (e) {
      print('❌ Erro ao verificar e corrigir canais: $e');
    }
  }

  /// Salvar a data/hora do último registro no SharedPreferences
  Future<void> _saveLastRegisterTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;
      await prefs.setInt(_lastRegisterTimeKey, now);
      print(
          '✅ Data de registro salva: ${DateTime.fromMillisecondsSinceEpoch(now)}');
    } catch (e) {
      print('❌ Erro ao salvar data de registro: $e');
    }
  }

  /// Salvar a data/hora da última verificação de token no SharedPreferences
  Future<void> _saveLastTokenVerificationTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;
      await prefs.setInt(_lastTokenVerificationKey, now);
      if (!_logSilentMode) {
        print(
            '✅ Data de verificação de token salva: ${DateTime.fromMillisecondsSinceEpoch(now)}');
      }
    } catch (e) {
      if (!_logSilentMode) {
        print('❌ Erro ao salvar data de verificação de token: $e');
      }
    }
  }

  /// Gerencia a inscrição dos canais de notificação para o usuário
  /// Esta função centraliza toda a lógica de gerenciamento de canais
  /// @param userId ID do usuário atual
  /// @param forceUpdate Se verdadeiro, força a atualização mesmo que os canais já estejam corretos
  /// @return Verdadeiro se a inscrição foi bem sucedida
  Future<bool> manageChannelSubscriptions(String userId,
      {bool forceUpdate = false}) async {
    print('📢 Gerenciando inscrição em canais para usuário: $userId');

    try {
      // 1. Obter a lista de canais necessários para este usuário
      // Cada usuário deve estar inscrito em canais específicos baseados em seu perfil
      final List<String> requiredChannels =
          await _getRequiredChannelsForUser(userId);
      print('📋 Canais necessários para o usuário: $requiredChannels');

      // 2. Verificar se a instalação existe e quais canais já estão registrados
      final installation = await _buscarInstalacaoAtual(userId);

      // 3. Se a instalação não existe, não podemos gerenciar canais
      if (installation == null) {
        print(
            '⚠️ Não foi possível encontrar instalação para o usuário. Os canais serão configurados no próximo registro.');
        return false;
      }

      // 4. Obter canais atuais da instalação
      final List<dynamic> currentChannels =
          installation.get<List<dynamic>>('channels') ?? [];
      print('📋 Canais atuais: $currentChannels');

      // 5. Verificar se já está inscrito em todos os canais necessários
      bool allChannelsPresent = requiredChannels
          .every((channel) => currentChannels.contains(channel));

      // 6. Se todos os canais já estão presentes e não estamos forçando atualização, não é necessário fazer nada
      if (allChannelsPresent && !forceUpdate) {
        print('✅ Usuário já está inscrito em todos os canais necessários');
        return true;
      }

      // 7. Calcular quais canais precisam ser adicionados
      final missingChannels = requiredChannels
          .where((channel) => !currentChannels.contains(channel))
          .toList();

      // 8. Adicionar canais faltantes à lista atual
      if (missingChannels.isNotEmpty) {
        print('➕ Adicionando canais faltantes: $missingChannels');

        // Adicionar todos os canais faltantes à lista atual
        for (final channel in missingChannels) {
          currentChannels.add(channel);
        }

        // 9. Atualizar a instalação com a nova lista de canais
        installation.set('channels', currentChannels);
        final response = await installation.save();

        // 10. Verificar se a atualização foi bem sucedida
        if (response.success) {
          print('✅ Canais adicionados com sucesso: $missingChannels');

          // 11. Verifica se realmente foi salvo corretamente
          await _verifyChannelSubscriptions(userId, requiredChannels);
          return true;
        } else {
          print('❌ Falha ao adicionar canais: ${response.error?.message}');
          return false;
        }
      } else if (forceUpdate) {
        // 12. Se estamos forçando atualização mas não há canais faltantes,
        // garantimos que não há canais extras desnecessários
        print('🔄 Forçando atualização dos canais para o padrão');

        installation.set('channels', requiredChannels);
        final response = await installation.save();

        if (response.success) {
          print('✅ Canais padronizados com sucesso');
          return true;
        } else {
          print('❌ Falha ao padronizar canais: ${response.error?.message}');
          return false;
        }
      }

      return true;
    } catch (e) {
      print('❌ Erro ao gerenciar inscrição em canais: $e');
      return false;
    }
  }

  /// Verifica se os canais foram realmente inscritos após a operação
  /// Esta é uma verificação dupla para garantir que o servidor registrou corretamente
  /// @param userId ID do usuário
  /// @param expectedChannels Lista de canais que deveriam estar registrados
  Future<bool> _verifyChannelSubscriptions(
      String userId, List<String> expectedChannels) async {
    print('🔍 Verificando se os canais foram realmente inscritos...');

    try {
      // Usar a função de diagnóstico para verificar no servidor
      final ParseCloudFunction function =
          ParseCloudFunction('diagnosticarNotificacoes');
      final ParseResponse response =
          await function.execute(parameters: {'userId': userId});

      if (!response.success) {
        print('⚠️ Não foi possível verificar a inscrição nos canais');
        return false;
      }

      final result = response.result;
      if (result == null ||
          result['instalacoes'] == null ||
          result['instalacoes'].isEmpty) {
        print('⚠️ Nenhuma instalação encontrada para verificar canais');
        return false;
      }

      // Pegar a primeira instalação encontrada
      final installation = result['instalacoes'][0];
      final serverChannels = installation['channels'] ?? [];

      print('📋 Canais verificados no servidor: $serverChannels');

      // Verificar se todos os canais esperados estão presentes
      bool allChannelsPresent =
          expectedChannels.every((channel) => serverChannels.contains(channel));

      if (allChannelsPresent) {
        print('✅ Todos os canais foram inscritos corretamente');
        return true;
      } else {
        // Encontrar quais canais estão faltando
        final missingChannels = expectedChannels
            .where((channel) => !serverChannels.contains(channel))
            .toList();
        print(
            '⚠️ Alguns canais não foram inscritos corretamente: $missingChannels');

        // Tentar corrigir novamente, mas sem entrar em loop infinito
        print('🔄 Tentando corrigir canais faltantes diretamente...');
        await _adicionarCanaisFaltantes(userId, missingChannels);

        return false;
      }
    } catch (e) {
      print('❌ Erro ao verificar inscrição nos canais: $e');
      return false;
    }
  }

  /// Determina quais canais são necessários para um determinado usuário
  /// @param userId ID do usuário
  /// @return Lista de canais necessários
  Future<List<String>> _getRequiredChannelsForUser(String userId) async {
    // Define os canais básicos necessários para todos os pacientes
    final List<String> channels = [
      'patients', // Canal para todos os pacientes
      'patient_$userId', // Canal específico para este paciente
      'global', // Canal para notificações globais do app
    ];

    try {
      // Tentar obter o UserDataController com segurança
      UserDataController? userDataController;
      try {
        userDataController = Get.find<UserDataController>();
      } catch (e) {
        print('⚠️ Não foi possível obter UserDataController: $e');
        return channels; // Retorna canais básicos em caso de erro
      }

      // Obter dados do usuário com segurança
      final userData = await userDataController.getUserData();
      if (userData == null) {
        print('⚠️ Dados do usuário não disponíveis');
        return channels; // Retorna canais básicos se não há dados
      }

      // Processar unidades favoritas com segurança
      try {
        if (userData.containsKey('favoriteUnits')) {
          final dynamic favUnitsData = userData['favoriteUnits'];
          if (favUnitsData != null) {
            // Converter para lista com segurança
            List<dynamic> favUnits = [];

            if (favUnitsData is List) {
              favUnits = favUnitsData;
            } else if (favUnitsData is String) {
              try {
                final decoded = json.decode(favUnitsData);
                favUnits = decoded is List ? decoded : [];
              } catch (_) {
                favUnits = [];
              }
            }

            // Processar cada unidade
            for (final unit in favUnits) {
              String? unitId;

              if (unit is Map) {
                unitId = unit['objectId']?.toString() ?? unit['id']?.toString();
              } else if (unit is String) {
                unitId = unit;
              }

              if (unitId != null && unitId.isNotEmpty) {
                final channelName = 'unit_$unitId';
                if (!channels.contains(channelName)) {
                  channels.add(channelName);
                  print('📢 Canal adicionado: $channelName');
                }
              }
            }
          }
        }

        // Processar especialidades com segurança (preparando para uso futuro)
        if (userData.containsKey('specialties')) {
          final dynamic specialtiesData = userData['specialties'];
          if (specialtiesData is List && specialtiesData.isNotEmpty) {
            for (final specialty in specialtiesData) {
              String? specialtyId;

              if (specialty is Map) {
                specialtyId = specialty['objectId']?.toString() ??
                    specialty['id']?.toString();
              } else if (specialty is String) {
                specialtyId = specialty;
              }

              if (specialtyId != null && specialtyId.isNotEmpty) {
                final channelName = 'specialty_$specialtyId';
                if (!channels.contains(channelName)) {
                  channels.add(channelName);
                  print('📢 Canal de especialidade adicionado: $channelName');
                }
              }
            }
          }
        }
      } catch (e) {
        print('⚠️ Erro ao processar canais adicionais: $e');
        // Continuar com os canais que já foram adicionados
      }
    } catch (e) {
      print('⚠️ Erro geral ao obter canais adicionais: $e');
    }

    print('📋 Canais finais para o usuário $userId: $channels');
    return channels;
  }

  /// Inscreve o usuário em um canal específico
  /// @param userId ID do usuário
  /// @param channelName Nome do canal para inscrição
  /// @return Verdadeiro se a inscrição foi bem sucedida
  Future<bool> subscribeToChannel(String userId, String channelName) async {
    print('📢 Tentando inscrever usuário no canal: $channelName');

    try {
      final installation = await _buscarInstalacaoAtual(userId);
      if (installation == null) {
        print(
            '⚠️ Não foi possível encontrar instalação para inscrever no canal');
        return false;
      }

      // Obter lista atual de canais
      final List<dynamic> channels =
          installation.get<List<dynamic>>('channels') ?? [];

      // Verificar se já está inscrito
      if (channels.contains(channelName)) {
        print('ℹ️ Usuário já está inscrito no canal: $channelName');
        return true;
      }

      // Adicionar o novo canal
      channels.add(channelName);
      installation.set('channels', channels);

      // Salvar a instalação atualizada
      final response = await installation.save();

      if (response.success) {
        print('✅ Usuário inscrito com sucesso no canal: $channelName');
        return true;
      } else {
        print('❌ Falha ao inscrever no canal: ${response.error?.message}');
        return false;
      }
    } catch (e) {
      print('❌ Erro ao inscrever no canal: $e');
      return false;
    }
  }

  /// Cancela a inscrição do usuário em um canal específico
  /// @param userId ID do usuário
  /// @param channelName Nome do canal para cancelar inscrição
  /// @return Verdadeiro se o cancelamento foi bem sucedido
  Future<bool> unsubscribeFromChannel(String userId, String channelName) async {
    print('📢 Tentando cancelar inscrição do usuário no canal: $channelName');

    // Não permitir cancelar inscrição em canais essenciais
    if (channelName == 'patients' ||
        channelName == 'patient_$userId' ||
        channelName == 'global') {
      print('⚠️ Não é permitido cancelar inscrição em canais essenciais');
      return false;
    }

    try {
      final installation = await _buscarInstalacaoAtual(userId);
      if (installation == null) {
        print(
            '⚠️ Não foi possível encontrar instalação para cancelar inscrição');
        return false;
      }

      // Obter lista atual de canais
      final List<dynamic> channels =
          installation.get<List<dynamic>>('channels') ?? [];

      // Verificar se está inscrito
      if (!channels.contains(channelName)) {
        print('ℹ️ Usuário não está inscrito no canal: $channelName');
        return true;
      }

      // Remover o canal
      channels.remove(channelName);
      installation.set('channels', channels);

      // Salvar a instalação atualizada
      final response = await installation.save();

      if (response.success) {
        print('✅ Inscrição cancelada com sucesso no canal: $channelName');
        return true;
      } else {
        print('❌ Falha ao cancelar inscrição: ${response.error?.message}');
        return false;
      }
    } catch (e) {
      print('❌ Erro ao cancelar inscrição: $e');
      return false;
    }
  }

  /// Método público para forçar o registro quando o usuário fizer login
  Future<void> registerAfterLogin() async {
    print('🔄 Registrando token após login...');

    // Evitar registro múltiplo se já estiver registrando
    if (isRegistering.value) {
      print('⚠️ Registro já em andamento. Aguardando conclusão...');
      return;
    }

    // Verificar explicitamente se o usuário está logado antes de prosseguir
    try {
      final userDataController = Get.find<UserDataController>();
      final userData = await userDataController.getUserData();

      if (userData == null || !userData.containsKey('userId')) {
        print('⚠️ Dados do usuário indisponíveis, pulando registro após login');
        isRegistered.value = false;
        return;
      }

      // Verificar se já está registrado para evitar múltiplos registros
      if (isRegistered.value) {
        print('✅ Dispositivo já registrado. Verificando detalhes...');

        // Verificação adicional sem registro
        await manageChannelSubscriptions(userData['userId'],
            forceUpdate: false);
        return;
      }

      print('👤 Registrando notificações para usuário: ${userData['userId']}');

      if (currentToken.value.isEmpty) {
        // Se não temos token, obter um novo
        await _getToken();
      }

      if (currentToken.value.isNotEmpty) {
        // Proteger contra múltiplas tentativas simultâneas
        isRegistering.value = true;

        try {
          // Força o registro independentemente do tempo decorrido desde o último registro
          await _registerTokenWithServer(currentToken.value);

          // Garantir que os canais estejam corretos
          await manageChannelSubscriptions(userData['userId'],
              forceUpdate: true);

          // Atualizar a hora do último registro
          await _saveLastRegisterTime();

          // Atualizar também o timestamp da verificação de token
          await _saveLastTokenVerificationTime();

          print('✅ Registro após login realizado com sucesso');
        } finally {
          // Garantir que isRegistering é resetado mesmo em caso de erro
          isRegistering.value = false;
        }

        // Test notification removed - now using proper queue notifications
      }
    } catch (e) {
      print('❌ Erro durante registro após login: $e');
    }
  }

  // Limpar recursos ao fechar
  @override
  void onClose() {
    _tokenVerificationTimer?.cancel();
    super.onClose();
  }

  /// Método para diagnóstico e resolução de problemas nas notificações
  Future<void> diagnosticarNotificacoes() async {
    print('🔍 Iniciando diagnóstico de notificações push...');

    try {
      // 1. Verificar token atual
      final token = await FirebaseMessaging.instance.getToken();
      print('🔑 Token FCM: ${token?.substring(0, 15)}...');

      // 2. Verificar permissões
      final settings =
          await FirebaseMessaging.instance.getNotificationSettings();
      print('📱 Status de permissão: ${settings.authorizationStatus}');

      // 3. Verificar instalação no Parse Server
      UserDataController? userDataController = Get.find<UserDataController>();
      final userData = await userDataController.getUserData();

      if (userData != null && userData.containsKey('userId')) {
        final deviceId = userData['userId'];
        print('👤 ID do usuário: $deviceId');

        // Chamar função de diagnóstico do Parse Server
        try {
          final ParseCloudFunction function =
              ParseCloudFunction('diagnosticarNotificacoes');
          final ParseResponse response = await function.execute(parameters: {
            'userId': deviceId,
            'tokenAtual': token, // Incluir token atual para verificação
            'verificarToken': true
          });

          if (response.success) {
            final result = response.result;
            print('📊 Diagnóstico do servidor:');
            print(result);

            if (result != null) {
              // Verificar consistência do token
              if (result['tokenCorreto'] == false) {
                print('⚠️ Token inconsistente detectado! Corrigindo...');
                // Se o token for diferente, forçar atualização no servidor
                if (token != null) {
                  await _registerTokenWithServer(token);
                }
              }

              // Verificar instalações
              if (result['instalacoes'] != null) {
                final installations = result['instalacoes'];
                for (var inst in installations) {
                  print('📝 Instalação: ${inst['objectId']}');
                  print('  - Canais: ${inst['channels']}');

                  // Verificar se o canal específico do paciente está presente
                  final channels = inst['channels'];
                  final String expectedChannel = 'patient_$deviceId';

                  if (channels is List && !channels.contains(expectedChannel)) {
                    print(
                        '⚠️ Canal específico não encontrado, tentando corrigir...');
                    await _adicionarCanalEspecifico(deviceId);
                  }
                }
              }
            }
          } else {
            print('❌ Erro no diagnóstico: ${response.error?.message}');
          }
        } catch (e) {
          print('❌ Erro ao chamar diagnóstico: $e');
        }
      }

      // 4. Tentar forçar atualização de token
      print('🔄 Tentando forçar atualização do token...');
      await forceTokenRefresh();

      // 5. Enviar notificação de teste
      await _enviarNotificacaoTeste();

      // 6. Atualizar timestamp de verificação
      await _saveLastTokenVerificationTime();
    } catch (e) {
      print('❌ Erro durante diagnóstico: $e');
    }
  }

  // Adicionar método público para forçar verificação do token a qualquer momento
  Future<void> forceTokenVerification() async {
    print('🔍 Forçando verificação de consistência do token...');

    // Desativar modo silencioso para logs completos
    final previousSilentMode = _logSilentMode;
    _logSilentMode = false;

    try {
      await _verifyTokenConsistency();
      print('✅ Verificação de token concluída');
    } catch (e) {
      print('❌ Erro durante verificação forçada do token: $e');
    } finally {
      // Restaurar o modo silencioso ao estado anterior
      _logSilentMode = previousSilentMode;
    }
  }

  // Limpar dados de notificação (usado ao fazer logout)
  Future<void> clearNotificationData() async {
    print('🧹 Limpando dados de notificação...');
    try {
      // Desregistrar do servidor
      isRegistered.value = false;

      // Limpar token atual (não remove do Firebase, apenas da memória)
      currentToken.value = '';

      // Cancelar timers
      _tokenVerificationTimer?.cancel();

      print('✅ Dados de notificação limpos com sucesso');
    } catch (e) {
      print('❌ Erro ao limpar dados de notificação: $e');
    }
  }

  /// Forçar o registro mesmo que não tenha passado o intervalo de 24 horas
  Future<void> forceRegister() async {
    print('🔄 Forçando registro de notificações...');
    if (currentToken.value.isEmpty) {
      await _getToken();
    }

    if (currentToken.value.isNotEmpty) {
      await _registerTokenWithServer(currentToken.value);
      await verificarECorrigirCanais();
      await _saveLastRegisterTime();

      // Também atualizar o timestamp de verificação de token
      await _saveLastTokenVerificationTime();

      print('✅ Registro forçado concluído com sucesso');
    } else {
      print('❌ Não foi possível obter token para registro forçado');
    }
  }

  /// Força a atualização do token FCM
  Future<void> forceTokenRefresh() async {
    print('🔄 Forçando atualização do token FCM...');
    try {
      await FirebaseMessaging.instance.deleteToken();
      final String? newToken = await FirebaseMessaging.instance.getToken();
      if (newToken != null) {
        print('✅ Token FCM atualizado: ${newToken.substring(0, 15)}...');
        currentToken.value = newToken;
        // Registrar o novo token
        await _registerTokenWithServer(newToken);
      } else {
        print('❌ Não foi possível obter novo token FCM');
      }
    } catch (e) {
      print('❌ Erro ao forçar atualização do token: $e');
    }
  }

  Future<void> _forceRegisterIgnoringInterval() async {
    if (currentToken.value.isEmpty) {
      await _getToken();
    }

    if (currentToken.value.isNotEmpty) {
      await _registerTokenWithServer(currentToken.value);
      await _saveLastRegisterTime();
    }
  }

  // Adicionar esta função para garantir que o canal específico esteja presente
  Future<void> _adicionarCanalEspecifico(String userId) async {
    try {
      print('🔧 Tentando adicionar canal específico para o usuário: $userId');

      final installation = await _buscarInstalacaoAtual(userId);
      if (installation != null) {
        final List<dynamic> channels =
            installation.get<List<dynamic>>('channels') ?? [];
        final String specificChannel = 'patient_$userId';

        if (!channels.contains(specificChannel)) {
          print('➕ Adicionando canal específico: $specificChannel');
          channels.add(specificChannel);
          installation.set('channels', channels);

          final response = await installation.save();

          if (response.success) {
            print('✅ Canal específico adicionado: $specificChannel');
          } else {
            print(
                '❌ Falha ao adicionar canal específico: ${response.error?.message}');
          }
        } else {
          print('ℹ️ Canal específico já existe: $specificChannel');
        }
      } else {
        print('⚠️ Nenhuma instalação encontrada para o usuário: $userId');
      }
    } catch (e) {
      print('❌ Erro ao adicionar canal específico: $e');
    }
  }

  Future<ParseObject?> _buscarInstalacaoAtual(String userId) async {
    try {
      print('🔍 Buscando instalação para o usuário: $userId');

      // 1. Tentar abordagem via Cloud Function primeiro (mais confiável)
      try {
        final ParseCloudFunction function =
            ParseCloudFunction('diagnosticarNotificacoes');
        final ParseResponse response =
            await function.execute(parameters: {'deviceId': userId});

        if (response.success &&
            response.result != null &&
            response.result['found'] == true &&
            response.result['installations'] != null &&
            response.result['installations'].isNotEmpty) {
          final String objectId =
              response.result['installations'][0]['objectId'];
          print('✅ Instalação encontrada via função cloud: $objectId');

          // Buscar a instalação completa pelo ID
          final queryById =
              QueryBuilder<ParseObject>(ParseObject('_Installation'))
                ..whereEqualTo('objectId', objectId);

          final objectResponse = await queryById.query();
          if (objectResponse.success &&
              objectResponse.results != null &&
              objectResponse.results!.isNotEmpty) {
            return objectResponse.results!.first as ParseObject;
          }
        }
      } catch (cloudError) {
        print('⚠️ Erro na abordagem via Cloud Function: $cloudError');
      }

      // 2. Tentar busca direta como fallback
      try {
        final query = QueryBuilder<ParseObject>(ParseObject('_Installation'))
          ..whereEqualTo('userId', userId);

        final response = await query.query();
        if (response.success &&
            response.results != null &&
            response.results!.isNotEmpty) {
          print(
              '✅ Instalação encontrada via query direta: ${response.results!.first.objectId}');
          return response.results!.first as ParseObject;
        }
      } catch (queryError) {
        print('⚠️ Erro na busca direta: $queryError');
      }

      print('⚠️ Nenhuma instalação encontrada para o usuário: $userId');
      return null;
    } catch (e) {
      print('❌ Erro geral ao buscar instalação: $e');
      return null;
    }
  }

  // Melhorar a função que processa mensagens recebidas com logs mais detalhados
  void _processIncomingMessage(RemoteMessage message) {
    print('📩 Processando mensagem recebida: ${message.messageId}');
    final notification = message.notification;
    final data = message.data;

    // Atualizar cache local com dados da notificação PRIMEIRO
    _updateLocalCacheFromNotification(data);

    print('📋 NOTIFICATION PROCESSING DETAILS:');
    print('   - MessageId: ${message.messageId}');
    if (notification != null) {
      print('   - Title: ${notification.title}');
      print('   - Body: ${notification.body}');
      print('   - Android Channel: ${notification.android?.channelId}');
      print('   - iOS Sound: ${notification.apple?.sound?.name}');
    }
    print('   - Data: $data');
    print('   - Category: ${message.category}');
    print('   - SenderId: ${message.senderId}');
    print('   - Timestamp: ${DateTime.now()}');

    // ✅ DETECTAR E PROCESSAR DIFERENTES TIPOS DE NOTIFICAÇÃO
    final tipo = data['tipo'] ?? data['type'] ?? '';
    print('🔔 Tipo de notificação detectado: $tipo');

    // ✅ ATUALIZAR CONTROLLER AUTOMATICAMENTE PARA TIPOS RELEVANTES
    final tiposQueRequeremAtualizacao = [
      'inicio_atendimento',
      'sua_vez',
      'chamado_atendimento',
      'mudanca_posicao',
      'posicao_alterada',
      'quase_sua_vez',
      'emergencia_pausa',
      'emergencia_resolvida',
      'removido_fila',
      'atendimento_finalizado'
    ];

    if (tiposQueRequeremAtualizacao.contains(tipo)) {
      print('🔄 Notificação requer atualização do controller: $tipo');

      try {
        if (Get.isRegistered<FilaPacienteController>()) {
          final controller = Get.find<FilaPacienteController>();

          // ✅ DIFERENTES TIPOS DE ATUALIZAÇÃO BASEADOS NO TIPO
          switch (tipo) {
            case 'inicio_atendimento':
            case 'sua_vez':
            case 'chamado_atendimento':
              print('🏥 NOTIFICAÇÃO DE INÍCIO DE ATENDIMENTO DETECTADA!');
              controller.atualizarDadosRapido();
              break;

            case 'emergencia_pausa':
              print('🚨 NOTIFICAÇÃO DE EMERGÊNCIA DETECTADA!');
              controller.verificarEmergenciasAtivas();
              break;

            case 'emergencia_resolvida':
              print('✅ NOTIFICAÇÃO DE EMERGÊNCIA RESOLVIDA DETECTADA!');
              controller.atualizarDadosRapido();
              break;

            case 'mudanca_posicao':
            case 'posicao_alterada':
            case 'quase_sua_vez':
              print('📊 NOTIFICAÇÃO DE MUDANÇA DE POSIÇÃO DETECTADA!');
              controller.atualizarDadosRapido();
              break;

            case 'removido_fila':
            case 'atendimento_finalizado':
              print('🔚 NOTIFICAÇÃO DE FINALIZAÇÃO DETECTADA!');
              controller.atualizarDadosRapido();
              break;

            default:
              controller.atualizarDadosRapido();
          }

          print(
              '✅ Controller da fila atualizado automaticamente para tipo: $tipo');
        }
      } catch (e) {
        print('⚠️ Erro ao atualizar controller: $e');
      }
    }

    // Gerar um ID único baseado no timestamp para evitar substituição de notificações
    final notificationId = DateTime.now().millisecondsSinceEpoch % 10000;

    // Se temos notificação, mostrar notificação local
    if (notification != null) {
      print('📬 Notificação recebida: ${notification.title}');
      _showLocalNotification(
        id: notificationId,
        title: notification.title ?? 'Nova notificação',
        body: notification.body ?? '',
        payload: data,
      );
    }
    // Se temos apenas dados, extrair título e mensagem dos dados
    else if (data.isNotEmpty) {
      final title = data['title'] ?? data['titulo'] ?? 'Nova atualização';
      final body = data['body'] ??
          data['alert'] ??
          data['mensagem'] ??
          'Você recebeu uma atualização';

      print('📬 Notificação de dados recebida: $title (tipo: $tipo)');
      _showLocalNotification(
        id: notificationId,
        title: title,
        body: body,
        payload: data,
      );
    }

    // Notificar controllers sobre a atualização via GetX
    if (data.containsKey('fila_id') || data.containsKey('filaId')) {
      final filaId = data['fila_id'] ?? data['filaId'];
      _notifyControllersOfUpdate(filaId, data);
    }
  }

  void _handleOpenedFromNotification(RemoteMessage message) {
    _processNotificationNavigation(message.data);
  }

  void _onLocalNotificationTap(NotificationResponse response) {
    print('🖱️ Notificação tocada: ${response.id}');

    try {
      if (response.payload != null) {
        final Map<String, dynamic> payload = json.decode(response.payload!);
        _processNotificationNavigation(payload);
      }
    } catch (e) {
      print('❌ Erro ao processar toque na notificação: $e');
    }
  }

  Future<void> _showLocalNotification({
    required int id,
    required String title,
    required String body,
    Map<String, dynamic>? payload,
  }) async {
    print('📱 Mostrando notificação local: "$title"');

    try {
      await _localNotifications.show(
        id,
        title,
        body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            channel.id,
            channel.name,
            channelDescription: channel.description,
            importance: Importance.high,
            priority: Priority.high,
            icon: 'ic_launcher',
          ),
          iOS: const DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        payload: payload != null ? json.encode(payload) : null,
      );
    } catch (e) {
      print('❌ Erro ao mostrar notificação local: $e');
    }
  }

  void _processNotificationNavigation(Map<String, dynamic> data) {
    print('🧭 Processando navegação por notificação...');
    print('📋 Dados: $data');

    final String? tipo = data['tipo'];
    if (tipo == null) {
      print('⚠️ Notificação sem tipo definido');
      return;
    }

    switch (tipo) {
      // ✅ MUDANÇAS DE POSIÇÃO NA FILA
      case 'posicao_alterada':
      case 'mudanca_posicao':
        print('📊 Navegando para fila - Posição alterada');
        _navigateToFilaAcompanhamento(data);
        break;

      // ✅ PRÓXIMO DA VEZ
      case 'quase_sua_vez':
        print('⚡ Navegando para fila - Quase sua vez');
        _navigateToFilaAcompanhamento(data, estaProximo: true);
        break;

      // ✅ CHAMADO PARA ATENDIMENTO
      case 'chamado_atendimento':
      case 'inicio_atendimento':
      case 'sua_vez':
        print('🏥 Navegando para atendimento - Paciente foi chamado!');
        _navigateToFilaAcompanhamento(data, emAtendimento: true);
        break;

      // ✅ EMERGÊNCIAS E PAUSAS
      case 'emergencia_pausa':
        print('🚨 Navegando para fila - Emergência detectada');
        _navigateToFilaAcompanhamento(data);
        break;

      case 'emergencia_resolvida':
        print('✅ Navegando para fila - Emergência resolvida');
        _navigateToFilaAcompanhamento(data);
        break;

      // ✅ SOLICITAÇÕES
      case 'solicitacao_aprovada':
      case 'adicionado_fila':
        print('✅ Navegando para carregamento - Solicitação aprovada');
        _navigateToCarregando(data);
        break;

      case 'solicitacao_recusada':
        print('❌ Navegando para home - Solicitação recusada');
        _navigateToHome();
        break;

      // ✅ FINALIZAÇÕES
      case 'atendimento_finalizado':
        print('✅ Navegando para finalização - Atendimento concluído');
        _navigateToAtendimentoFinalizado(data);
        break;

      case 'removido_fila':
        print('⚠️ Navegando para remoção - Removido da fila');
        _navigateToRemovidoFila(data);
        break;

      // ✅ MENSAGENS
      case 'mensagem_fila':
        print('💬 Navegando para fila - Nova mensagem');
        _navigateToFilaAcompanhamento(data, mostrarMensagens: true);
        break;

      // ✅ CASOS NÃO TRATADOS
      default:
        print('⚠️ Tipo de notificação não tratado: $tipo');
        print('📋 Dados da notificação: $data');
        _navigateToHome();
    }
  }

  void _navigateToFilaAcompanhamento(
    Map<String, dynamic> data, {
    bool emAtendimento = false,
    bool estaProximo = false,
    bool mostrarMensagens = false,
  }) {
    final filaId = data['fila_id'] ?? data['filaId'];
    if (filaId == null) {
      print('⚠️ ID da fila não encontrado');
      return;
    }

    print('🧭 Navegando para /fila_acompanhamento');
    Get.toNamed('/fila_acompanhamento', arguments: {
      'filaId': filaId,
      'emAtendimento': emAtendimento,
      'estaProximo': estaProximo,
      'mostrarMensagens': mostrarMensagens,
    });
  }

  void _navigateToCarregando(Map<String, dynamic> data) {
    final filaId = data['fila_id'] ?? data['filaId'];
    if (filaId == null) {
      print('⚠️ ID da fila não encontrado');
      return;
    }

    print('🧭 Navegando para /carregando');
    Get.toNamed('/carregando', arguments: {
      'filaId': filaId,
    });
  }

  void _navigateToHome() {
    print('🧭 Navegando para /home');
    Get.offAllNamed('/home');
  }

  void _navigateToAtendimentoFinalizado(Map<String, dynamic> data) {
    print('🧭 Navegando para /tela_paciente_atendido');
    Get.offAllNamed('/tela_paciente_atendido', arguments: {
      'medicoNome': data['medico'] ??
          data['nomeMedico'] ??
          data['medico_nome'] ??
          'Médico',
      'especialidade': data['especialidade'] ?? 'Especialidade',
      'hospitalNome': data['hospital'] ??
          data['hospitalNome'] ??
          data['hospital_nome'] ??
          'Hospital',
    });
  }

  void _navigateToRemovidoFila(Map<String, dynamic> data) {
    print('🧭 Navegando para /tela_paciente_removido');
    Get.offAllNamed('/tela_paciente_removido', arguments: {
      'motivoRemocao':
          data['motivo'] ?? data['motivo_pausa'] ?? 'removido_secretaria',
      'medicoNome': data['medico'] ??
          data['nomeMedico'] ??
          data['medico_nome'] ??
          'Médico',
      'especialidade': data['especialidade'] ?? 'Especialidade',
      'hospitalNome': data['hospital'] ??
          data['hospitalNome'] ??
          data['hospital_nome'] ??
          'Hospital',
    });
  }

  /// Envia relatório de diagnóstico para o servidor
  Future<void> sendDiagnosticReport() async {
    print('📊 Enviando relatório de diagnóstico...');
    try {
      // Vamos usar o método de diagnóstico que já implementamos
      await diagnosticarNotificacoes();
      // Adicionar verificação e correção de canais
      await verificarECorrigirCanais();
      print('✅ Relatório de diagnóstico enviado com sucesso');
    } catch (e) {
      print('❌ Erro ao enviar relatório de diagnóstico: $e');
    }
  }

  /// Verificar se deve registrar novamente com base no intervalo de 24 horas
  Future<bool> _shouldRegisterAgain() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastRegisterTime = prefs.getInt(_lastRegisterTimeKey);

      if (lastRegisterTime == null) {
        // Se nunca registrou, precisa registrar
        print('🕑 Primeiro registro de push notifications.');
        return true;
      }

      final now = DateTime.now().millisecondsSinceEpoch;
      final elapsedTime = now - lastRegisterTime;

      // Verificar se já se passaram 24 horas desde o último registro
      if (elapsedTime >= _registerIntervalMs) {
        print(
            '🕑 Intervalo de registro diário atingido (${elapsedTime ~/ (1000 * 60 * 60)} horas desde o último registro)');
        return true;
      }

      // Calcular quanto tempo falta para o próximo registro
      final timeUntilNextRegister = _registerIntervalMs - elapsedTime;
      final hoursLeft = timeUntilNextRegister ~/ (1000 * 60 * 60);
      final minutesLeft =
          (timeUntilNextRegister % (1000 * 60 * 60)) ~/ (1000 * 60);

      print(
          '🕑 Registro ainda válido. Próximo registro em aproximadamente $hoursLeft horas e $minutesLeft minutos');
      return false;
    } catch (e) {
      print('⚠️ Erro ao verificar tempo de registro: $e');
      // Em caso de erro, permitir o registro para garantir funcionamento
      return true;
    }
  }

  // Método para verificação da consistência do token - otimizado para menos verificações
  Future<void> _verifyTokenConsistency() async {
    // Verificar se já está na hora de verificar o token novamente
    // Agora verifica a cada 12 horas em vez de 6 horas
    if (!await _shouldVerifyTokenAgain()) {
      if (!_logSilentMode) {
        print('ℹ️ Verificação de token ainda recente, pulando...');
      }
      return;
    }

    try {
      if (!_logSilentMode) {
        print('🔄 Verificando consistência do token FCM...');
      }

      // Verificar se o usuário está logado
      UserDataController? userDataController;
      try {
        userDataController = Get.find<UserDataController>();
        final userData = await userDataController.getUserData();

        if (userData == null || !userData.containsKey('userId')) {
          if (!_logSilentMode) {
            print('ℹ️ Usuário não está logado, pulando verificação de token');
          }
          return;
        }

        final String userId = userData['userId'];

        // Obter token atual do Firebase - apenas verificar existência, sem forçar renovação
        final String? currentFirebaseToken = await _fcm.getToken();
        if (currentFirebaseToken == null) {
          if (!_logSilentMode) {
            print('⚠️ Não foi possível obter o token FCM atual');
          }
          return;
        }

        // Verificar se o token em memória está atualizado
        if (currentFirebaseToken != currentToken.value) {
          print('⚠️ Token em memória desatualizado! Atualizando...');
          print(
              '   - Token em memória: ${currentToken.value.substring(0, 15)}...');
          print(
              '   - Token atual Firebase: ${currentFirebaseToken.substring(0, 15)}...');
          currentToken.value = currentFirebaseToken;
        }

        // Verificar se o token está registrado no servidor apenas após 12 horas desde o último registro
        // Fazer verificação mais passiva sem consultar servidor constantemente
        final prefs = await SharedPreferences.getInstance();
        final lastRegisterTime = prefs.getInt(_lastRegisterTimeKey) ?? 0;
        final now = DateTime.now().millisecondsSinceEpoch;
        final hoursSinceLastRegister =
            (now - lastRegisterTime) / (60 * 60 * 1000);

        if (hoursSinceLastRegister > 12) {
          if (!_logSilentMode) {
            print(
                '🕒 Mais de 12 horas desde o último registro. Verificando consistência no servidor...');
          }

          // Verificação mais leve diretamente na tabela Installation
          final installation = await _buscarInstalacaoAtual(userId);
          if (installation != null) {
            final tokenServidor = installation.get<String>('deviceToken');

            // Verificar se o token no servidor está diferente
            if (tokenServidor != currentFirebaseToken) {
              print('⚠️ Token desatualizado no servidor! Atualizando...');
              await _registerTokenWithServer(currentFirebaseToken);
            } else if (!_logSilentMode) {
              print(
                  '✅ Token FCM está consistente entre dispositivo e servidor');
            }
          }
        } else if (!_logSilentMode) {
          print(
              '✅ Verificação completa pulada (última verificação foi há ${hoursSinceLastRegister.toStringAsFixed(1)} horas)');
        }

        // Registrar timestamp da verificação
        await _saveLastTokenVerificationTime();
      } catch (e) {
        if (!_logSilentMode) {
          print('⚠️ Erro ao verificar consistência do token: $e');
        }

        // Registrar timestamp da verificação mesmo com erro
        await _saveLastTokenVerificationTime();
      }
    } catch (e) {
      if (!_logSilentMode) {
        print('❌ Erro na verificação de consistência do token: $e');
      }
    }
  }

  /// Verifica se já é hora de verificar o token novamente com base no intervalo definido
  Future<bool> _shouldVerifyTokenAgain() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastVerificationTime = prefs.getInt(_lastTokenVerificationKey);

      if (lastVerificationTime == null) {
        // Se nunca verificou, precisa verificar
        if (!_logSilentMode) {
          print('🕑 Primeira verificação de token.');
        }
        return true;
      }

      final now = DateTime.now().millisecondsSinceEpoch;
      final elapsedTime = now - lastVerificationTime;

      // Verificar se já se passou o intervalo definido desde a última verificação
      if (elapsedTime >= _tokenVerificationIntervalMs) {
        if (!_logSilentMode) {
          print(
              '🕑 Intervalo de verificação de token atingido (${elapsedTime ~/ (1000 * 60 * 60)} horas desde a última verificação)');
        }
        return true;
      }

      return false;
    } catch (e) {
      if (!_logSilentMode) {
        print('⚠️ Erro ao verificar tempo de verificação de token: $e');
      }
      // Em caso de erro, permitir a verificação para garantir funcionamento
      return true;
    }
  }

  Future<void> _enviarNotificacaoTeste() async {
    try {
      print('📤 Enviando notificação de teste...');

      final userData = await Get.find<UserDataController>().getUserData();
      final deviceId = userData?['userId'];

      if (deviceId == null) {
        print('❌ ID do usuário não disponível para teste');
        return;
      }

      final ParseCloudFunction function =
          ParseCloudFunction('testarNotificacaoPush');
      final ParseResponse response = await function.execute(parameters: {
        'userId': deviceId,
        'mensagem':
            'Esta é uma notificação de teste do Fila App (${DateTime.now().toString()})'
      });

      if (response.success) {
        print('✅ Notificação de teste enviada com sucesso!');
        print('📊 Resposta: ${response.result}');
      } else {
        print(
            '❌ Erro ao enviar notificação de teste: ${response.error?.message}');
      }
    } catch (e) {
      print('❌ Exceção ao enviar notificação de teste: $e');
    }
  }

  /// Método inteligente para verificar se o registro é realmente necessário
  /// Este método é chamado periodicamente e só registra quando necessário
  Future<void> _forceRegisterIfNeeded() async {
    try {
      if (!_logSilentMode) {
        print(
            '🧠 Verificando de forma inteligente se o registro é necessário...');
      }

      // Caso 1: Usuário não está logado - não precisa registrar
      UserDataController? userDataController;
      try {
        userDataController = Get.find<UserDataController>();
        final userData = await userDataController.getUserData();

        if (userData == null || !userData.containsKey('userId')) {
          if (!_logSilentMode) {
            print('ℹ️ Usuário não está logado, registro não necessário.');
          }
          return;
        }

        // Caso 2: Token não está presente - não pode registrar
        if (currentToken.value.isEmpty) {
          if (!_logSilentMode) {
            print('⚠️ Token não disponível, não é possível registrar.');
          }
          return;
        }

        // Caso 3: Verificar se o token mudou desde o último registro
        final prefs = await SharedPreferences.getInstance();
        final lastToken = prefs.getString('last_registered_token') ?? '';

        if (lastToken.isNotEmpty && lastToken != currentToken.value) {
          if (!_logSilentMode) {
            print(
                '🔄 Token mudou desde o último registro! Forçando atualização...');
          }
          await _registerTokenWithServer(currentToken.value);
          await _saveLastRegisterTime();

          // Salvar o novo token
          await prefs.setString('last_registered_token', currentToken.value);
          return;
        }

        // Caso 4: Verificar se passou muito tempo desde o último registro
        if (await _shouldRegisterAgain()) {
          if (!_logSilentMode) {
            print('🕒 Prazo de registro expirado. Registrando novamente...');
          }
          await _registerTokenWithServer(currentToken.value);
          await _saveLastRegisterTime();

          // Salvar o token atual
          await prefs.setString('last_registered_token', currentToken.value);
          return;
        }

        // Se chegou até aqui, não precisa registrar novamente
        if (!_logSilentMode) {
          print('✅ Registro ainda válido, nenhuma ação necessária.');
        }
      } catch (e) {
        if (!_logSilentMode) {
          print('⚠️ UserDataController não disponível: $e');
        }
      }
    } catch (e) {
      if (!_logSilentMode) {
        print('❌ Erro na verificação inteligente de registro: $e');
      }
    }
  }

  // Novo método para registrar especificamente para notificações de fila
  Future<bool> registerForFilaNotifications(String idPaciente) async {
    if (!_logSilentMode) {
      print('🔔 Registrando para notificações de fila - Paciente: $idPaciente');
    }

    try {
      await _getToken();
      if (currentToken.value.isEmpty) {
        print('❌ Token FCM não disponível para registro');
        return false;
      }

      // Canal específico para o paciente
      final channels = ['patient_$idPaciente'];

      // Usar método existente para gerenciar canais
      await manageChannelSubscriptions(idPaciente);

      // Atualizar informações do paciente
      await _updatePacienteWithPushToken(idPaciente, currentToken.value);

      print('✅ Registro para notificações de fila concluído');
      return true;
    } catch (e) {
      print('❌ Erro ao registrar para notificações de fila: $e');
      return false;
    }
  }

  // Método para atualizar o token push no objeto Paciente
  Future<void> _updatePacienteWithPushToken(
      String idPaciente, String token) async {
    try {
      // Buscar o paciente pelo deviceId ou userId
      final queryPaciente = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereEqualTo('deviceId', idPaciente);

      final response = await queryPaciente.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final paciente = response.results!.first;
        paciente.set('pushToken', token);
        paciente.set('ultimoAcesso', DateTime.now());

        final acl = ParseACL();
        acl.setPublicReadAccess(allowed: true);
        acl.setPublicWriteAccess(allowed: true);
        paciente.setACL(acl);

        await paciente.save();
        print('✅ Token push atualizado no objeto Paciente');
      } else {
        // Se não encontrou com deviceId, tentar com userId
        final queryPacienteUserId =
            QueryBuilder<ParseObject>(ParseObject('Paciente'))
              ..whereEqualTo('userId', idPaciente);

        final responseUserId = await queryPacienteUserId.query();

        if (responseUserId.success &&
            responseUserId.results != null &&
            responseUserId.results!.isNotEmpty) {
          final paciente = responseUserId.results!.first;
          paciente.set('pushToken', token);
          paciente.set('ultimoAcesso', DateTime.now());

          final acl = ParseACL();
          acl.setPublicReadAccess(allowed: true);
          acl.setPublicWriteAccess(allowed: true);
          paciente.setACL(acl);

          await paciente.save();
          print('✅ Token push atualizado no objeto Paciente (por userId)');
        }
      }
    } catch (e) {
      print('⚠️ Erro ao atualizar token no Paciente: $e');
      // Não falhar o registro por causa disso
    }
  }

  // Método específico para tratar notificações de mudança de posição
  void _handleFilaNotification(RemoteMessage message) {
    final data = message.data;
    final tipo = data['tipo'];

    print('🔔 Processando notificação de fila - Tipo: $tipo');

    switch (tipo) {
      case 'posicao_alterada':
        _showPositionChangeNotification(message);
        break;
      case 'quase_sua_vez':
        _showAlmostYourTurnNotification(message);
        break;
      case 'sua_vez':
        _showYourTurnNotification(message);
        break;
      case 'atendimento_iniciado':
        _showServiceStartedNotification(message);
        break;
      case 'atendimento_finalizado':
        _showServiceFinishedNotification(message);
        break;
      default:
        _showGenericNotification(message);
    }
  }

  // Notificação para mudança de posição
  void _showPositionChangeNotification(RemoteMessage message) {
    _showLocalNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: message.notification?.title ?? 'Atualização na fila',
      body: message.notification?.body ?? 'Sua posição na fila foi atualizada',
    );
  }

  // Notificação para "quase sua vez"
  void _showAlmostYourTurnNotification(RemoteMessage message) {
    _showLocalNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: message.notification?.title ?? 'Quase sua vez!',
      body: message.notification?.body ??
          'Você está entre os próximos a serem atendidos',
    );
  }

  // Notificação para "sua vez"
  void _showYourTurnNotification(RemoteMessage message) {
    _showLocalNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: message.notification?.title ?? 'É a sua vez!',
      body: message.notification?.body ?? 'Você é o próximo a ser atendido',
    );
  }

  // Notificação para atendimento iniciado
  void _showServiceStartedNotification(RemoteMessage message) {
    _showLocalNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: message.notification?.title ?? 'Atendimento iniciado',
      body: message.notification?.body ?? 'Seu atendimento foi iniciado',
    );
  }

  // Notificação para atendimento finalizado
  void _showServiceFinishedNotification(RemoteMessage message) {
    _showLocalNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: message.notification?.title ?? 'Atendimento finalizado',
      body: message.notification?.body ?? 'Seu atendimento foi finalizado',
    );
  }

  // Notificação genérica
  void _showGenericNotification(RemoteMessage message) {
    _showLocalNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: message.notification?.title ?? 'Saúde Sem Espera',
      body: message.notification?.body ?? 'Nova notificação',
    );
  }

  // Método para testar configuração de notificações
  Future<void> _testNotificationSetup() async {
    print('🧪 [TEST] Testando configuração de notificações...');

    try {
      // Verificar se temos token
      if (currentToken.value.isEmpty) {
        print('❌ [TEST] Sem token FCM para testar');
        return;
      }

      // Verificar permissões
      final settings = await _fcm.getNotificationSettings();
      print('🔒 [TEST] Status de permissão: ${settings.authorizationStatus}');

      // Verificar se há usuário logado
      try {
        final userDataController = Get.find<UserDataController>();
        if (userDataController.userDataExists.value) {
          print('👤 [TEST] Usuário logado encontrado');

          // Forçar registro para teste
          await _forceRegisterForTesting();
        } else {
          print('⚠️ [TEST] Usuário não logado');
        }
      } catch (e) {
        print('⚠️ [TEST] UserDataController não disponível: $e');
      }
    } catch (e) {
      print('❌ [TEST] Erro no teste: $e');
    }
  }

  // Método para forçar registro durante teste
  Future<void> _forceRegisterForTesting() async {
    print('🚀 [TEST] Forçando registro para teste...');

    try {
      if (currentToken.value.isNotEmpty) {
        await _registerTokenWithServer(currentToken.value);
        print('✅ [TEST] Registro forçado concluído!');

        // Tentar enviar notificação de teste
        await _sendTestNotification();
      }
    } catch (e) {
      print('❌ [TEST] Erro no registro forçado: $e');
    }
  }

  // Método para enviar notificação de teste
  Future<void> _sendTestNotification() async {
    print('📨 [TEST] Enviando notificação de teste...');

    try {
      // Criar notificação local de teste primeiro
      await _showLocalNotification(
        id: 12345,
        title: '🧪 Teste de Notificação',
        body: 'Se você vê esta mensagem, as notificações estão funcionando!',
      );

      print('✅ [TEST] Notificação local de teste enviada!');
    } catch (e) {
      print('❌ [TEST] Erro ao enviar notificação de teste: $e');
    }
  }

  /// MÉTODOS OTIMIZADOS PARA REDUZIR REQUISIÇÕES AO BACK4APP

  /// Atualiza cache local com dados da notificação para evitar consultas desnecessárias
  void _updateLocalCacheFromNotification(Map<String, dynamic> data) {
    if (data.containsKey('fila_id') && data.containsKey('posicao')) {
      final filaId = data['fila_id'];
      final posicao = data['posicao'];

      _localCache['fila_$filaId'] = {
        'posicao': posicao,
        'lastUpdate': DateTime.now().millisecondsSinceEpoch,
        'source': 'push_notification'
      };

      print(
          '📦 Cache atualizado via push notification - Fila: $filaId, Posição: $posicao');
    }
  }

  /// Obtém dados do cache local se disponível e válido (reduz consultas ao servidor)
  Map<String, dynamic>? getCachedFilaData(String filaId) {
    final cacheKey = 'fila_$filaId';
    if (_localCache.containsKey(cacheKey)) {
      final cachedData = _localCache[cacheKey];
      final lastUpdate = cachedData['lastUpdate'] ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;

      // Cache válido por 5 minutos
      if (now - lastUpdate < 5 * 60 * 1000) {
        print('📦 Usando dados do cache para fila $filaId');
        return cachedData;
      } else {
        // Cache expirado, remover
        _localCache.remove(cacheKey);
      }
    }
    return null;
  }

  /// Operação em lote para registrar múltiplos dispositivos (reduz requisições)
  Future<bool> registerMultipleDevicesInBatch(
      List<Map<String, dynamic>> devices) async {
    try {
      print('🔄 Registrando ${devices.length} dispositivos em lote...');

      // Usar Cloud Function otimizada para registro em lote
      final ParseCloudFunction function =
          ParseCloudFunction('registerDevicesBatch');
      final ParseResponse response = await function.execute(
          parameters: {'devices': devices, 'batchSize': devices.length});

      if (response.success) {
        print('✅ Registro em lote realizado com sucesso!');
        return true;
      } else {
        print('❌ Erro no registro em lote: ${response.error?.message}');
        return false;
      }
    } catch (e) {
      print('❌ Exceção no registro em lote: $e');
      return false;
    }
  }

  /// Cache inteligente para reduzir consultas ao servidor
  Future<void> enableLocalCache() async {
    try {
      // Configurar cache local usando SharedPreferences para persistência
      final prefs = await SharedPreferences.getInstance();

      // Limpar cache antigo ao inicializar
      cleanExpiredCache();

      print('✅ Cache local habilitado para otimização');
    } catch (e) {
      print('❌ Erro ao habilitar cache local: $e');
    }
  }

  /// Usar Parse LiveQuery para atualizações em tempo real (sem polling)
  Future<void> setupLiveQueryForFila(String filaId) async {
    try {
      print('🔄 Configurando LiveQuery para fila $filaId...');

      // Criar query para a fila específica
      final queryBuilder = QueryBuilder<ParseObject>(ParseObject('FilaEspera'))
        ..whereEqualTo('objectId', filaId);

      // Configurar LiveQuery
      final liveQuery = await LiveQuery().client.subscribe(queryBuilder);

      // Escutar atualizações em tempo real
      liveQuery.on(LiveQueryEvent.update, (ParseObject object) {
        print('📡 Atualização recebida via LiveQuery para fila $filaId');

        // Atualizar cache local com dados da LiveQuery
        _localCache['fila_$filaId'] = {
          'posicao': object.get('posicao'),
          'status': object.get('status'),
          'lastUpdate': DateTime.now().millisecondsSinceEpoch,
          'source': 'live_query'
        };

        // Notificar controllers sobre a atualização sem fazer nova requisição
        _notifyControllersOfUpdate(filaId, object.toJson());
      });

      print('✅ LiveQuery configurado para fila $filaId');
    } catch (e) {
      print('❌ Erro ao configurar LiveQuery: $e');
    }
  }

  /// Notifica controllers sobre atualizações sem fazer requisições
  void _notifyControllersOfUpdate(String filaId, Map<String, dynamic> data) {
    // Usar GetX para notificar controllers sobre atualizações
    // Isso evita que eles façam requisições desnecessárias
    try {
      // Exemplo: notificar controller da fila
      Get.find<dynamic>()?.updateFilaFromPush(filaId, data);
    } catch (e) {
      print('ℹ️ Controller não encontrado para notificação: $e');
    }
  }

  /// Método otimizado para verificar status de múltiplas filas de uma vez
  Future<Map<String, dynamic>> getMultipleFilaStatusBatch(
      List<String> filaIds) async {
    try {
      print('🔄 Consultando status de ${filaIds.length} filas em lote...');

      // Usar Cloud Function para consulta em lote
      final ParseCloudFunction function =
          ParseCloudFunction('getMultipleFilaStatus');
      final ParseResponse response =
          await function.execute(parameters: {'filaIds': filaIds});

      if (response.success && response.result != null) {
        final results = response.result as Map<String, dynamic>;

        // Atualizar cache local com todos os resultados
        for (final filaId in filaIds) {
          if (results.containsKey(filaId)) {
            _localCache['fila_$filaId'] = {
              ...results[filaId],
              'lastUpdate': DateTime.now().millisecondsSinceEpoch,
              'source': 'batch_query'
            };
          }
        }

        print('✅ Status de ${filaIds.length} filas consultado em lote');
        return results;
      } else {
        print('❌ Erro na consulta em lote: ${response.error?.message}');
        return {};
      }
    } catch (e) {
      print('❌ Exceção na consulta em lote: $e');
      return {};
    }
  }

  /// Limpar cache antigo para otimizar memória
  void cleanExpiredCache() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final keysToRemove = <String>[];

    _localCache.forEach((key, value) {
      final lastUpdate = value['lastUpdate'] ?? 0;
      // Remover cache mais antigo que 10 minutos
      if (now - lastUpdate > 10 * 60 * 1000) {
        keysToRemove.add(key);
      }
    });

    for (final key in keysToRemove) {
      _localCache.remove(key);
    }

    if (keysToRemove.isNotEmpty) {
      print('🧹 Cache limpo: ${keysToRemove.length} entradas removidas');
    }
  }
}
