import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// Monitor de performance em tempo real
class PerformanceMonitor extends GetxService {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  // Métricas observáveis
  final RxDouble fps = 0.0.obs;
  final RxInt memoryUsage = 0.obs;
  final RxInt buildCount = 0.obs;
  final RxDouble averageFrameTime = 0.0.obs;
  final RxList<double> frameTimes = <double>[].obs;
  final RxMap<String, int> widgetBuildCounts = <String, int>{}.obs;
  final RxMap<String, double> networkLatencies = <String, double>{}.obs;

  // Performance thresholds
  static const double targetFPS = 60.0;
  static const double maxFrameTime = 16.67; // 60 FPS = 16.67ms per frame
  static const int maxMemoryMB = 200;

  Timer? _monitoringTimer;
  final List<double> _frameTimeHistory = [];
  final Map<String, Stopwatch> _networkStopwatches = {};

  @override
  void onInit() {
    super.onInit();
    startMonitoring();
  }

  /// Inicia monitoramento de performance (DESABILITADO para evitar spam de logs)
  void startMonitoring() {
    // ✅ DESABILITADO: Monitoramento estava causando spam de logs de performance
    // WidgetsBinding.instance.addPostFrameCallback(_onFrame);
    // _monitoringTimer = Timer.periodic(const Duration(seconds: 1), (_) {
    //   _updateMetrics();
    // });

    // Performance monitoring desabilitado para produção
  }

  /// Callback para cada frame renderizado
  void _onFrame(Duration timeStamp) {
    final frameTime =
        timeStamp.inMicroseconds / 1000.0; // Convert to milliseconds

    _frameTimeHistory.add(frameTime);
    if (_frameTimeHistory.length > 60) {
      _frameTimeHistory.removeAt(0);
    }

    // Calcular FPS baseado nos últimos frames
    if (_frameTimeHistory.length >= 2) {
      final frameDuration = _frameTimeHistory.last - _frameTimeHistory.first;
      final currentFPS =
          (_frameTimeHistory.length - 1) / (frameDuration / 1000.0);
      fps.value = currentFPS.clamp(0, 120);
    }

    // Atualizar tempo médio de frame
    if (_frameTimeHistory.isNotEmpty) {
      averageFrameTime.value =
          _frameTimeHistory.reduce((a, b) => a + b) / _frameTimeHistory.length;
    }

    // Agendar próximo frame
    WidgetsBinding.instance.addPostFrameCallback(_onFrame);
  }

  /// Atualiza métricas gerais
  void _updateMetrics() {
    _updateMemoryUsage();
    _analyzePerformance();
  }

  /// Monitora uso de memória
  void _updateMemoryUsage() {
    // Estimativa de uso de memória (simulado)
    final estimatedMemory = (buildCount.value * 0.1 +
            widgetBuildCounts.length * 0.5 +
            Random().nextInt(20))
        .round();
    memoryUsage.value = estimatedMemory;
  }

  /// Analisa performance e emite alertas
  void _analyzePerformance() {
    // Alerta de FPS baixo
    if (fps.value < targetFPS * 0.8) {
      _emitPerformanceAlert(
        'FPS Baixo',
        'FPS atual: ${fps.value.toStringAsFixed(1)} (Target: $targetFPS)',
        PerformanceAlertLevel.warning,
      );
    }

    // Alerta de frame time alto
    if (averageFrameTime.value > maxFrameTime * 1.5) {
      _emitPerformanceAlert(
        'Frame Time Alto',
        'Tempo médio: ${averageFrameTime.value.toStringAsFixed(2)}ms (Max: ${maxFrameTime}ms)',
        PerformanceAlertLevel.critical,
      );
    }

    // Alerta de memória alta
    if (memoryUsage.value > maxMemoryMB) {
      _emitPerformanceAlert(
        'Uso de Memória Alto',
        'Memória: ${memoryUsage.value}MB (Max: ${maxMemoryMB}MB)',
        PerformanceAlertLevel.warning,
      );
    }
  }

  /// Rastreia build de widget
  void trackWidgetBuild(String widgetName) {
    buildCount.value++;
    widgetBuildCounts[widgetName] = (widgetBuildCounts[widgetName] ?? 0) + 1;
  }

  /// Inicia rastreamento de requisição de rede
  void startNetworkRequest(String endpoint) {
    _networkStopwatches[endpoint] = Stopwatch()..start();
  }

  /// Finaliza rastreamento de requisição de rede
  void endNetworkRequest(String endpoint) {
    final stopwatch = _networkStopwatches[endpoint];
    if (stopwatch != null) {
      stopwatch.stop();
      networkLatencies[endpoint] = stopwatch.elapsedMilliseconds.toDouble();
      _networkStopwatches.remove(endpoint);
    }
  }

  /// Emite alerta de performance (DESABILITADO para evitar spam de logs)
  void _emitPerformanceAlert(
      String title, String message, PerformanceAlertLevel level) {
    // ✅ DESABILITADO: Logs de performance estavam causando spam
    // debugPrint('Performance: $title - $message');
  }

  /// Relatório de performance detalhado
  Map<String, dynamic> getDetailedReport() {
    return {
      'fps': fps.value,
      'average_frame_time_ms': averageFrameTime.value,
      'memory_usage_mb': memoryUsage.value,
      'total_builds': buildCount.value,
      'widget_build_counts': Map.from(widgetBuildCounts),
      'network_latencies_ms': Map.from(networkLatencies),
      'performance_score': _calculatePerformanceScore(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Calcula score de performance (0-100)
  double _calculatePerformanceScore() {
    double score = 100.0;

    // Penalidade por FPS baixo
    if (fps.value < targetFPS) {
      score -= (targetFPS - fps.value) / targetFPS * 30;
    }

    // Penalidade por frame time alto
    if (averageFrameTime.value > maxFrameTime) {
      score -= (averageFrameTime.value - maxFrameTime) / maxFrameTime * 20;
    }

    // Penalidade por uso de memória alto
    if (memoryUsage.value > maxMemoryMB) {
      score -= (memoryUsage.value - maxMemoryMB) / maxMemoryMB * 15;
    }

    // Penalidade por muitos rebuilds
    final avgBuilds = widgetBuildCounts.values.isNotEmpty
        ? widgetBuildCounts.values.reduce((a, b) => a + b) /
            widgetBuildCounts.length
        : 0;
    if (avgBuilds > 10) {
      score -= (avgBuilds - 10) / 10 * 15;
    }

    return score.clamp(0, 100);
  }

  /// Reset de métricas
  void resetMetrics() {
    buildCount.value = 0;
    widgetBuildCounts.clear();
    networkLatencies.clear();
    _frameTimeHistory.clear();
    // Métricas resetadas silenciosamente
  }

  @override
  void onClose() {
    _monitoringTimer?.cancel();
    super.onClose();
  }
}

/// Widget para exibir métricas de performance (DESABILITADO para produção)
class PerformanceOverlay extends StatelessWidget {
  final bool showDetailed;

  const PerformanceOverlay({
    super.key,
    this.showDetailed = false,
  });

  @override
  Widget build(BuildContext context) {
    // Retorna widget vazio - overlay desabilitado para produção
    return const SizedBox.shrink();
  }
}

/// Mixin para widgets que querem rastrear performance
mixin PerformanceTrackingMixin<T extends StatefulWidget> on State<T> {
  String get widgetName => T.toString();

  @override
  Widget build(BuildContext context) {
    final monitor = Get.find<PerformanceMonitor>();
    monitor.trackWidgetBuild(widgetName);
    return buildTracked(context);
  }

  Widget buildTracked(BuildContext context);
}

/// Níveis de alerta de performance
enum PerformanceAlertLevel { info, warning, critical }

/// Extensão para rastrear performance de requisições
extension NetworkPerformanceTracking on Future {
  Future<T> trackNetworkPerformance<T>(String endpoint) async {
    final monitor = Get.find<PerformanceMonitor>();
    monitor.startNetworkRequest(endpoint);

    try {
      final result = await this;
      monitor.endNetworkRequest(endpoint);
      return result as T;
    } catch (e) {
      monitor.endNetworkRequest(endpoint);
      rethrow;
    }
  }
}
