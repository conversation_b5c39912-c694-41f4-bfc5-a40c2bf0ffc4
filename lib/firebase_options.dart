// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCsuWQ_kw9CH669hR9VLv0NI5l7z7Y46Mc',
    appId: '1:224042695582:web:d40e6cef52738d2669b48f',
    messagingSenderId: '224042695582',
    projectId: 'saudesemesperaa',
    authDomain: 'saudesemesperaa.firebaseapp.com',
    storageBucket: 'saudesemesperaa.firebasestorage.app',
    measurementId: 'G-WPB6SYW6SZ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAsdVG36WFPoqc4J2h81_k1V8kCJPfdaWo',
    appId: '1:224042695582:android:bac16e5a6e4153fe69b48f',
    messagingSenderId: '224042695582',
    projectId: 'saudesemesperaa',
    storageBucket: 'saudesemesperaa.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBW-ypHNhvw1iSh1ZUY1aTq8-ssgV4BgP4',
    appId: '1:224042695582:ios:d666cffd3b9088de69b48f',
    messagingSenderId: '224042695582',
    projectId: 'saudesemesperaa',
    storageBucket: 'saudesemesperaa.firebasestorage.app',
    iosBundleId: 'br.com.saudesemespera',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBW-ypHNhvw1iSh1ZUY1aTq8-ssgV4BgP4',
    appId: '1:224042695582:ios:de34e6953007a2b969b48f',
    messagingSenderId: '224042695582',
    projectId: 'saudesemesperaa',
    storageBucket: 'saudesemesperaa.firebasestorage.app',
    iosBundleId: 'com.example.filaApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCsuWQ_kw9CH669hR9VLv0NI5l7z7Y46Mc',
    appId: '1:224042695582:web:3ce497ae6469db6e69b48f',
    messagingSenderId: '224042695582',
    projectId: 'saudesemesperaa',
    authDomain: 'saudesemesperaa.firebaseapp.com',
    storageBucket: 'saudesemesperaa.firebasestorage.app',
    measurementId: 'G-THTLNEHN9Z',
  );
}
