import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/controllers/fila_state_controller.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../utils/whatsapp_utils.dart'; // Importar a classe utilitária
import '../models/mensagem_fila.dart'; // Importar o modelo de mensagem
import '../models/fila.dart'; // Importe o modelo Fila
// ✅ TIMEZONE CONSOLIDADO: Agora tudo está em date_utils.dart
import '../utils/date_utils.dart'; // ✅ IMPORT DO NOVO SISTEMA DE TIMEZONE

// Sistema de exceções recomendado
class AppException implements Exception {
  final String message;
  final dynamic cause;

  AppException(this.message, [this.cause]);

  @override
  String toString() =>
      'AppException: $message${cause != null ? '\nCause: $cause' : ''}';
}

class NetworkException extends AppException {
  NetworkException(super.message, [super.cause]);
}

class LocationException extends AppException {
  LocationException(super.message, [super.cause]);
}

class FilaPacienteController extends GetxController {
  final RxString filaId = ''.obs;
  final RxString medicoNome = ''.obs;
  final RxString especialidade = ''.obs;
  final RxInt posicaoAtual = 0.obs;
  final RxInt posicaoAnterior =
      0.obs; // Para rastrear a posição antes da atualização
  final RxBool posicaoMudou =
      false.obs; // Para sinalizar mudança e acionar animações
  final RxInt tempoEstimado = 0.obs;
  final RxInt tempoDistancia = 15.obs; // Valor padrão de 15 minutos
  final RxInt totalPacientesFila = 0.obs; // Número total de pacientes na fila
  final RxBool isLoading = true.obs;
  final RxBool isSaindo = false.obs;
  final Rx<LatLng?> hospitalLocation = Rx<LatLng?>(null);
  final RxString hospitalTelefone = ''.obs;
  final RxList<MensagemFila> mensagens = <MensagemFila>[].obs;

  // Novas propriedades para a tela de atendimento em andamento
  final RxString statusAtual = 'aguardando'.obs;
  final RxString hospitalNome = ''.obs;
  final Rx<DateTime?> dataInicioAtendimento = Rx<DateTime?>(null);
  final RxString medicoCRM = 'CRM/SP 000000'.obs;
  final RxInt duracaoAtendimento = 0.obs;
  final RxInt tempoMedioAtendimento = 15.obs;

  // ✅ NOVO: Estado de emergência
  final RxBool filaEmEmergencia = false.obs;
  final RxString motivoEmergencia = ''.obs;
  final RxString tipoEmergencia = ''.obs;
  final RxString statusEmergencia = ''.obs;

  Timer? refreshTimer;
  Timer? locationTimer;
  Timer? mensagensTimer;
  Timer? duracaoTimer; // Novo timer para controlar a duração do atendimento
  Timer? emergencyTimer; // ✅ NOVO: Timer específico para emergências
  String _googleApiKey = '';
  Position? _currentPosition;

  FilaPacienteController({Map<String, dynamic>? initialFilaState}) {
    if (initialFilaState != null) {
      filaId.value = initialFilaState['filaId'] ?? '';
      medicoNome.value = initialFilaState['medicoNome'] ?? '';
      especialidade.value = initialFilaState['especialidade'] ?? '';
      posicaoAtual.value = initialFilaState['posicao'] ?? 0;
      statusAtual.value = initialFilaState['status'] ?? 'aguardando';
    }
    _googleApiKey = dotenv.env['GOOGLE_MAPS_API_KEY'] ?? '';
  }

  get idPaciente => null;

  /// ✅ MÉTODO PARA ATUALIZAÇÃO MANUAL RÁPIDA
  Future<void> atualizarDadosRapido() async {
    try {
      debugPrint('🔄 [ATUALIZAÇÃO MANUAL] Iniciando atualização rápida...');

      // Executar atualizações em paralelo para ser mais rápido
      await Future.wait([
        _verificarStatusFila(),
        _obterTotalPacientesFila(),
        _buscarMensagens(),
        verificarEmergenciasAtivas(),
      ]);

      debugPrint('✅ [ATUALIZAÇÃO MANUAL] Atualização concluída');
    } catch (e) {
      debugPrint('❌ [ATUALIZAÇÃO MANUAL] Erro: $e');
    }
  }

  @override
  void onInit() {
    super.onInit();

    // ✅ PRIMEIRO: Tentar argumentos do Get.arguments
    if (Get.arguments != null && Get.arguments is Map<String, dynamic>) {
      _inicializarComArgumentos(Get.arguments);
      print(
          '[FILA_CONTROLLER] 📦 Dados inicializados via Get.arguments: ${filaId.value}');
    }
    // ✅ SEGUNDO: Recuperar do FilaStateController se argumentos estão vazios
    else if (filaId.isEmpty) {
      _recuperarDadosSalvos();
    }

    inicializarDados();
  }

  @override
  void onClose() {
    try {
      if (refreshTimer != null && refreshTimer!.isActive) {
        refreshTimer!.cancel();
      }
      if (locationTimer != null && locationTimer!.isActive) {
        locationTimer!.cancel();
      }
      if (mensagensTimer != null && mensagensTimer!.isActive) {
        mensagensTimer!.cancel();
      }
      if (duracaoTimer != null && duracaoTimer!.isActive) {
        duracaoTimer!.cancel();
      }
      if (emergencyTimer != null && emergencyTimer!.isActive) {
        emergencyTimer!.cancel();
      }
    } catch (e) {
      debugPrint('Erro ao cancelar timers: $e');
    } finally {
      super.onClose();
    }
  }

  void _inicializarComArgumentos(Map<String, dynamic> args) {
    filaId.value = args['filaId'] ?? '';
    medicoNome.value = args['medicoNome'] ?? '';
    especialidade.value = args['especialidade'] ?? '';
    posicaoAtual.value = args['posicao'] ?? 0;
    statusAtual.value = args['status'] ?? 'aguardando';
  }

  Future<void> inicializarDados() async {
    isLoading.value = true;
    try {
      await _buscarDadosHospital();
      await FilaStateController.salvarEstadoFila(
        filaId: filaId.value,
        medicoNome: medicoNome.value,
        especialidade: especialidade.value,
        posicao: posicaoAtual.value,
      );

      // ✅ SEMPRE CALCULAR TEMPO ESTIMADO (mesmo sem dados completos)
      await _calcularTempoEstimadoInicial();

      // ✅ VERIFICAR EMERGÊNCIAS ATIVAS
      await verificarEmergenciasAtivas();

      _iniciarAtualizacaoAutomatica();
      _iniciarVerificacaoEmergencias(); // ✅ NOVO: Timer específico para emergências

      // Obter o total de pacientes na fila
      await _obterTotalPacientesFila();

      // Adicionar esta linha
      await _setupLocationUpdates();

      // Calcular tempo de distância logo após obter localização do hospital
      if (hospitalLocation.value != null) {
        await _calcularTempoDistancia();
      }

      // Buscar mensagens
      await _buscarMensagens();
      _iniciarAtualizacaoMensagens();

      // Se já estiver em atendimento, iniciar contagem de duração
      if (statusAtual.value == 'em_atendimento') {
        _iniciarContadorDuracao();
        await _obterTempoMedioAtendimento();
      }
    } on AppException catch (e) {
      debugPrint('Erro ao inicializar dados: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao inicializar dados: $e');
    } finally {
      isLoading.value = false;
    }
  }

  void _iniciarAtualizacaoAutomatica() {
    refreshTimer?.cancel();

    // ✅ INTERVALO DINÂMICO baseado no status e posição
    Duration intervaloAtual;
    if (statusAtual.value == 'em_atendimento') {
      intervaloAtual = const Duration(
          seconds: 3); // ✅ MUITO RÁPIDO para detectar finalização
    } else if (posicaoAtual.value == 1) {
      intervaloAtual =
          const Duration(seconds: 3); // ✅ MUITO RÁPIDO quando é o próximo
    } else if (posicaoAtual.value <= 3) {
      intervaloAtual = const Duration(seconds: 5); // Rápido quando próximo
    } else if (posicaoAtual.value <= 5) {
      intervaloAtual = const Duration(seconds: 8); // Moderado
    } else {
      intervaloAtual = const Duration(seconds: 25); // Mais lento quando longe
    }

    debugPrint(
        '⏰ [TIMER] Configurando timer para ${intervaloAtual.inSeconds}s (status: ${statusAtual.value}, posição: ${posicaoAtual.value})');

    refreshTimer = Timer.periodic(intervaloAtual, (_) async {
      if (!isSaindo.value && filaId.isNotEmpty) {
        debugPrint('⏰ [REFRESH TIMER] Verificando atualizações gerais...');

        // ✅ VERIFICAÇÃO INTELIGENTE: Só fazer consulta pesada se necessário
        final statusAnterior = statusAtual.value;
        final posicaoAnterior = posicaoAtual.value;

        await _verificarStatusFila();

        // Se houve mudanças significativas, verificar também emergências
        if (statusAtual.value != statusAnterior ||
            posicaoAtual.value != posicaoAnterior) {
          debugPrint(
              '🔄 [REFRESH] Mudanças detectadas, verificando emergências...');
          await verificarEmergenciasAtivas();

          // ✅ RECONFIGURAR TIMER se a posição mudou significativamente
          if ((posicaoAnterior > 3 && posicaoAtual.value <= 3) ||
              (posicaoAnterior > 1 && posicaoAtual.value == 1)) {
            debugPrint(
                '🔄 [TIMER] Reconfigurando timer devido à mudança de posição');
            _iniciarAtualizacaoAutomatica();
            return;
          }
        }
      }
    });

    // ✅ PRIMEIRA VERIFICAÇÃO: Mais leve e rápida
    _verificarStatusRapido();
  }

  /// ✅ VERIFICAÇÃO RÁPIDA E LEVE (apenas status essencial)
  Future<void> _verificarStatusRapido() async {
    try {
      if (filaId.isEmpty) return;

      debugPrint(
          '⚡ [VERIFICAÇÃO RÁPIDA] Iniciando verificação para fila: ${filaId.value}');

      // Query simples apenas para dados essenciais
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value);

      final response = await queryFila.query();

      if (response.success && response.results?.isNotEmpty == true) {
        final fila = response.results!.first;
        final novoStatus = fila.get<String>('status') ?? 'aguardando';
        final novaPosicao = fila.get<int>('posicao') ?? posicaoAtual.value;

        debugPrint(
            '⚡ [VERIFICAÇÃO RÁPIDA] Status atual: ${statusAtual.value} -> $novoStatus');

        // Atualizar apenas se houve mudanças
        if (novoStatus != statusAtual.value) {
          final statusAnterior = statusAtual.value;
          statusAtual.value = novoStatus;
          debugPrint(
              '⚡ [VERIFICAÇÃO RÁPIDA] Status atualizado: $statusAnterior -> $novoStatus');

          // ✅ DETECTAR MUDANÇA PARA ATENDIMENTO
          if (novoStatus == 'em_atendimento' &&
              statusAnterior != 'em_atendimento') {
            debugPrint(
                '🏥 [VERIFICAÇÃO RÁPIDA] PACIENTE CHAMADO PARA ATENDIMENTO!');

            // Forçar atualização da UI
            update();

            // Mostrar notificação
            Get.snackbar(
              'Você foi chamado!',
              'Dirija-se ao consultório para o atendimento',
              backgroundColor: Colors.green,
              colorText: Colors.white,
              snackPosition: SnackPosition.TOP,
              duration: const Duration(seconds: 8),
              icon: const Icon(Icons.medical_services, color: Colors.white),
            );
          }

          // ✅ RECONFIGURAR TIMER baseado no novo status
          _iniciarAtualizacaoAutomatica();
        }

        if (novaPosicao != posicaoAtual.value) {
          posicaoAtual.value = novaPosicao;
          debugPrint('⚡ [VERIFICAÇÃO RÁPIDA] Posição atualizada: $novaPosicao');
        }

        // Se está pausado por emergência, atualizar motivo
        if (novoStatus == 'pausado_emergencia') {
          final motivo = fila.get<String>('motivo_pausa') ?? 'Emergência';
          if (motivo != motivoEmergencia.value) {
            motivoEmergencia.value = motivo;
            filaEmEmergencia.value = true;
          }
        }
      } else {
        debugPrint(
            '⚠️ [VERIFICAÇÃO RÁPIDA] Nenhum resultado encontrado para a fila');
      }
    } catch (e) {
      debugPrint('❌ Erro na verificação rápida: $e');
    }
  }

  /// ✅ NOVO: Timer específico para verificação de emergências mais frequente
  void _iniciarVerificacaoEmergencias() {
    emergencyTimer?.cancel();

    // ✅ OTIMIZAÇÃO: Verificar emergências menos frequentemente quando em atendimento
    final intervaloEmergencia = statusAtual.value == 'em_atendimento'
        ? const Duration(seconds: 8) // 8 segundos quando em atendimento
        : const Duration(seconds: 4); // 4 segundos quando aguardando

    emergencyTimer = Timer.periodic(intervaloEmergencia, (_) async {
      if (!isSaindo.value && filaId.isNotEmpty) {
        debugPrint('⏰ [EMERGÊNCIA TIMER] Verificando emergências...');
        await verificarEmergenciasAtivas();

        // ✅ ADICIONAL: Verificar se houve mudanças no status da fila
        await _verificarMudancasStatusFila();
      }
    });

    // Fazer uma verificação inicial imediata
    verificarEmergenciasAtivas();
  }

  /// ✅ VERIFICAR MUDANÇAS NO STATUS DA FILA
  Future<void> _verificarMudancasStatusFila() async {
    try {
      if (filaId.isEmpty) return;

      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value);

      final response = await queryFila.query();

      if (response.success && response.results?.isNotEmpty == true) {
        final fila = response.results!.first;
        final novoStatus = fila.get<String>('status') ?? 'aguardando';
        final statusAnterior = statusAtual.value;

        if (novoStatus != statusAnterior) {
          debugPrint('🔄 [STATUS CHANGE] $statusAnterior → $novoStatus');

          statusAtual.value = novoStatus;

          // Se mudou de pausado_emergencia para outro status, emergência foi finalizada
          if (statusAnterior == 'pausado_emergencia' &&
              novoStatus != 'pausado_emergencia') {
            debugPrint(
                '✅ [EMERGÊNCIA] Emergência foi finalizada - status mudou para: $novoStatus');
            filaEmEmergencia.value = false;
            motivoEmergencia.value = '';
            tipoEmergencia.value = '';
            statusEmergencia.value = '';

            // Forçar atualização da interface
            update();
          }
        }
      }
    } catch (e) {
      debugPrint('❌ Erro ao verificar mudanças de status: $e');
    }
  }

  Future<void> _setupLocationUpdates() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw LocationException('Serviços de localização desativados');
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw LocationException('Permissão de localização negada');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw LocationException(
            'Permissão de localização negada permanentemente');
      }

      _currentPosition = await Geolocator.getCurrentPosition();
      debugPrint(
          'Posição inicial obtida: ${_currentPosition!.latitude}, ${_currentPosition!.longitude}');

      locationTimer = Timer.periodic(const Duration(minutes: 2), (_) async {
        Position newPosition = await Geolocator.getCurrentPosition();
        debugPrint(
            'Nova posição obtida: ${newPosition.latitude}, ${newPosition.longitude}');

        if (_currentPosition == null ||
            (newPosition.latitude != _currentPosition!.latitude ||
                newPosition.longitude != _currentPosition!.longitude)) {
          _currentPosition = newPosition;
          if (hospitalLocation.value != null) {
            await _calcularTempoDistancia();
            // Adicionar cálculo do tempo médio também
            await _obterTempoMedioAtendimento();
          }
        }
      });
    } on LocationException catch (e) {
      debugPrint(
          'Erro ao configurar atualizações de localização: ${e.message}');
    } catch (e) {
      debugPrint(
          'Erro desconhecido ao configurar atualizações de localização: $e');
    }
  }

  Future<void> _buscarDadosHospital() async {
    try {
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value)
        ..includeObject(['consultorio', 'medico']);

      final response = await queryFila.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final consultorio =
            response.results!.first.get<ParseObject>('consultorio');
        if (consultorio != null) {
          final latitude = consultorio.get<num>('latitude')?.toDouble();
          final longitude = consultorio.get<num>('longitude')?.toDouble();

          if (latitude != null && longitude != null) {
            hospitalLocation.value = LatLng(latitude, longitude);
            // Calcula o tempo de distância assim que obtiver a localização
            await _calcularTempoDistancia();
          }

          hospitalTelefone.value = consultorio.get<String>('telefone') ?? '';
          hospitalNome.value = consultorio.get<String>('nome') ?? 'Hospital';
        }

        // Buscar dados do médico
        final medico = response.results!.first.get<ParseObject>('medico');
        if (medico != null) {
          medicoCRM.value = medico.get<String>('crm') ?? 'CRM não informado';
          // Se o nome do médico não foi definido ainda
          if (medicoNome.value.isEmpty) {
            medicoNome.value = medico.get<String>('nome') ?? 'Médico';
          }
          // Se a especialidade não foi definida ainda
          if (especialidade.value.isEmpty) {
            especialidade.value =
                medico.get<String>('especialidade') ?? 'Especialista';
          }
        }

        // Verificar se já está em atendimento e salvar a data de início
        final status =
            response.results!.first.get<String>('status') ?? 'aguardando';
        statusAtual.value = status;

        if (status == 'em_atendimento') {
          dataInicioAtendimento.value =
              response.results!.first.get<DateTime>('data_inicio_atendimento');
        }
      }
    } on AppException catch (e) {
      debugPrint('Erro ao buscar dados do hospital: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao buscar dados do hospital: $e');
    }
  }

  Future<void> _calcularTempoDistancia() async {
    if (hospitalLocation.value == null) {
      tempoDistancia.value = 15; // Valor padrão se não houver localização
      return;
    }

    try {
      // Verificar permissão de localização
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          tempoDistancia.value = 15;
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        tempoDistancia.value = 15;
        return;
      }

      // Obter posição atual
      final position = await Geolocator.getCurrentPosition();
      final origem = LatLng(position.latitude, position.longitude);
      final destino = hospitalLocation.value!;

      if (_googleApiKey.isNotEmpty) {
        try {
          final url =
              Uri.parse('https://maps.googleapis.com/maps/api/directions/json?'
                  'origin=${origem.latitude},${origem.longitude}'
                  '&destination=${destino.latitude},${destino.longitude}'
                  '&mode=driving'
                  '&key=$_googleApiKey');

          final response = await http.get(url);
          if (response.statusCode == 200) {
            final data = json.decode(response.body);
            if (data['status'] == 'OK' && data['routes'].isNotEmpty) {
              final route = data['routes'][0]['legs'][0];
              final durationInMinutes =
                  (route['duration']['value'] / 60).round();
              tempoDistancia.value = durationInMinutes;
              return;
            }
          }
        } on NetworkException catch (e) {
          debugPrint('Erro na API do Google Maps: ${e.message}');
        } catch (e) {
          debugPrint('Erro desconhecido na API do Google Maps: $e');
        }
      }

      // Cálculo alternativo se a API falhar ou não estiver disponível
      final distanceInMeters = Geolocator.distanceBetween(
        origem.latitude,
        origem.longitude,
        destino.latitude,
        destino.longitude,
      );

      // Estimativa usando velocidade média de 30 km/h
      final tempoEstimado = ((distanceInMeters / 1000) / 30 * 60).round();
      // Garantir um tempo mínimo razoável
      tempoDistancia.value = tempoEstimado < 5
          ? 5
          : tempoEstimado > 120
              ? 120
              : tempoEstimado.toInt(); // Ensure it's an int
    } catch (e) {
      debugPrint('Erro ao calcular tempo de distância: $e');
      tempoDistancia.value = 15; // Valor padrão em caso de erro
    }
  }

  Future<void> _obterTempoMedioAtendimento() async {
    try {
      // Primeiro, obter a fila atual para acessar o médico correto
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value)
        ..includeObject(['medico']);

      final filaResponse = await queryFila.query();

      if (!filaResponse.success ||
          filaResponse.results == null ||
          filaResponse.results!.isEmpty) {
        debugPrint('Erro: Fila não encontrada para obter métricas do médico');
        return;
      }

      final fila = filaResponse.results!.first;
      final medico = fila.get<ParseObject>('medico');

      if (medico == null || medico.objectId == null) {
        debugPrint('Erro: Médico não encontrado na fila para obter métricas');
        return;
      }

      // Agora consultar as métricas usando o ID correto do médico
      final queryMetricas =
          QueryBuilder<ParseObject>(ParseObject('MetricasAtendimento'))
            ..whereEqualTo('medico', medico);

      final response = await queryMetricas.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final metricas = response.results!.first;
        final tempoMedio =
            metricas.get<num>('tempo_medio_atendimento')?.toInt() ?? 15;
        tempoMedioAtendimento.value = tempoMedio;

        debugPrint(
            'Métricas obtidas para médico ${medico.objectId}: $tempoMedio minutos');
      } else {
        debugPrint(
            'Nenhuma métrica encontrada para o médico ${medico.objectId}');
      }
    } catch (e) {
      debugPrint('Erro ao obter tempo médio de atendimento: $e');
    }
  }

  Future<void> _verificarStatusFila() async {
    try {
      if (filaId.isEmpty) return;

      debugPrint(
          '🔍 [STATUS CHECK] Verificando status da fila: ${filaId.value}');

      // Consultar status atual da fila
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value)
        ..includeObject(['medico']);

      final response = await queryFila.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final fila = response.results!.first;
        final status = fila.get<String>('status') ?? 'aguardando';

        // Atualizar o status
        final statusAnterior = statusAtual.value;
        statusAtual.value = status;

        debugPrint(
            '📊 [STATUS CHECK] Status anterior: $statusAnterior, Status atual: $status');

        // ✅ NOVO: Se está pausado por emergência, não redirecionar - apenas atualizar estado
        if (status == 'pausado_emergencia') {
          debugPrint(
              '🚨 [PAUSA] Fila pausada por emergência - mantendo paciente na tela');

          // Verificar detalhes da emergência
          await verificarEmergenciasAtivas();
          return; // ✅ NÃO REDIRECIONAR - manter na tela de fila
        }

        // ✅ MELHORADO: Detectar mudança para em_atendimento
        if (status == 'em_atendimento' && statusAnterior != 'em_atendimento') {
          debugPrint('🏥 [ATENDIMENTO] Paciente foi chamado para atendimento!');

          // Forçar atualização da UI
          update();

          // Opcional: Mostrar notificação local
          Get.snackbar(
            'Atendimento Iniciado!',
            'Você foi chamado para o atendimento',
            backgroundColor: Colors.green,
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP,
            duration: const Duration(seconds: 5),
            icon: const Icon(Icons.medical_services, color: Colors.white),
          );
        }

        // Se não está mais na fila (cancelado, atendido, etc.), redirecionar
        if (status != 'aguardando' &&
            status != 'em_atendimento' &&
            status != 'pausado_emergencia') {
          if (isSaindo.isTrue) return; // Evitar duplas navegações

          debugPrint('🔄 [REDIRECIONAMENTO] Status detectado: $status');
          debugPrint(
              '🔄 [REDIRECIONAMENTO] Status anterior: ${statusAtual.value}');

          isSaindo.value = true;

          // Aguardar um pouco para garantir que a UI está pronta
          await Future.delayed(const Duration(milliseconds: 500));

          // Redirecionar para tela adequada
          if (status == 'removido' || status == 'cancelado') {
            debugPrint(
                '🔄 [REDIRECIONAMENTO] Redirecionando para tela de removido');
            Get.offAllNamed('/tela_paciente_removido', arguments: {
              'motivoRemocao':
                  status == 'removido' ? 'removido_secretaria' : 'cancelado',
              'medicoNome': medicoNome.value,
              'especialidade': especialidade.value,
              'hospitalNome': hospitalNome.value,
            });
          } else if (status == 'atendido') {
            debugPrint(
                '🔄 [REDIRECIONAMENTO] Redirecionando para tela de atendido');
            Get.offAllNamed('/tela_paciente_atendido', arguments: {
              'medicoNome': medicoNome.value,
              'especialidade': especialidade.value,
              'hospitalNome': hospitalNome.value,
            });
          } else {
            debugPrint(
                '🔄 [REDIRECIONAMENTO] Status desconhecido: $status, redirecionando para home');
            // Redireciona para home ou outra tela padrão
            Get.offAllNamed('/');
          }
          return;
        }

        // Atualizar posição na fila
        final novaPosicao = fila.get<int>('posicao') ?? 0;

        // Guardar a posição anterior antes de atualizar
        posicaoAnterior.value = posicaoAtual.value;

        if (novaPosicao != posicaoAtual.value) {
          // Ativar o sinalizador que indica mudança de posição para disparar animação
          posicaoMudou.value = true;

          // Atualizar a posição atual
          posicaoAtual.value = novaPosicao;
          debugPrint(
              'Posição atualizada: $novaPosicao (anterior: ${posicaoAnterior.value})');

          // Recalcular tempo estimado com a nova posição
          await _calcularTempoEstimado(fila);

          // Programar a desativação da animação após 3 segundos
          Future.delayed(const Duration(seconds: 3), () {
            posicaoMudou.value = false;
          });
        }

        // Obter o total de pacientes na fila do mesmo médico
        await _obterTotalPacientesFila();

        // Verificar notificações/mensagens se houver mudança de status
        if (status == 'em_atendimento' && statusAnterior != 'em_atendimento') {
          dataInicioAtendimento.value =
              fila.get<DateTime>('data_inicio_atendimento') ?? DateTime.now();
          _iniciarContadorDuracao();
          await _obterTempoMedioAtendimento();

          // Adicionar uma mensagem local de notificação
          final mensagemInicio = MensagemFila(
            titulo: 'Atendimento Iniciado',
            texto: 'Seu atendimento com Dr. ${medicoNome.value} foi iniciado!',
            dataEnvio: DateTime.now(),
            prioridade: 'alta',
            icone: 'notification',
          );

          if (mensagens.isEmpty ||
              mensagens.first.titulo != mensagemInicio.titulo) {
            mensagens.insert(0, mensagemInicio);
          }
        }
      }
    } catch (e) {
      debugPrint('Erro ao verificar status da fila: $e');
    }
  }

  Future<void> abrirWhatsApp() async {
    if (hospitalTelefone.isEmpty) {
      Get.snackbar(
        'Erro',
        'Telefone do hospital não disponível',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      final sucesso = await WhatsAppUtils.abrirWhatsApp(hospitalTelefone.value);
      if (!sucesso) {
        _mostrarErroWhatsApp();
      }
    } on AppException catch (e) {
      debugPrint('Erro ao abrir WhatsApp: ${e.message}');
      _mostrarErroWhatsApp();
    } catch (e) {
      debugPrint('Erro desconhecido ao abrir WhatsApp: $e');
      _mostrarErroWhatsApp();
    }
  }

  void _mostrarErroWhatsApp() {
    Get.snackbar(
      'Erro',
      'Não foi possível abrir o WhatsApp',
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  void abrirLocalizacao() {
    if (hospitalLocation.value == null) {
      Get.snackbar(
        'Erro',
        'Localização do hospital não disponível',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    Get.toNamed(
      '/localizacao',
      arguments: {
        'latitude': hospitalLocation.value!.latitude,
        'longitude': hospitalLocation.value!.longitude,
      },
    );
  }

  Future<void> confirmarSaidaFila() async {
    if (isSaindo.value) return;

    isSaindo.value = true;
    refreshTimer?.cancel();
    locationTimer?.cancel();
    emergencyTimer?.cancel(); // ✅ CANCELAR timer de emergências também

    try {
      await FilaStateController.limparEstadoFila();

      // Usar Get.toNamed em vez de Get.offAllNamed para preservar a sessão
      Get.toNamed(
        '/confirmacao_sair_fila',
        arguments: {
          'filaId': filaId.value,
          'nomeMedico': medicoNome.value,
          'especialidade': especialidade.value,
        },
      );
    } on AppException catch (e) {
      debugPrint('Erro ao sair da fila: ${e.message}');
      isSaindo.value = false;
      _iniciarAtualizacaoAutomatica();
      _iniciarVerificacaoEmergencias(); // ✅ REINICIAR timer de emergências também

      Get.snackbar(
        'Erro',
        'Erro ao sair da fila. Tente novamente.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } catch (e) {
      debugPrint('Erro desconhecido ao sair da fila: $e');
      isSaindo.value = false;
      _iniciarAtualizacaoAutomatica();
      _iniciarVerificacaoEmergencias(); // ✅ REINICIAR timer de emergências também

      Get.snackbar(
        'Erro',
        'Erro ao sair da fila. Tente novamente.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void atualizarLocalizacao() async {
    if (_currentPosition == null) {
      try {
        _currentPosition = await Geolocator.getCurrentPosition();
        if (hospitalLocation.value != null) {
          await _calcularTempoDistancia();
        }
      } on LocationException catch (e) {
        debugPrint('Erro ao atualizar localização: ${e.message}');
      } catch (e) {
        debugPrint('Erro desconhecido ao atualizar localização: $e');
      }
    }
  }

  Future<void> atualizarTempos() async {
    try {
      await _calcularTempoDistancia();
      await _obterTempoMedioAtendimento();
    } on AppException catch (e) {
      debugPrint('Erro ao atualizar tempos: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao atualizar tempos: $e');
    }
  }

  Future<bool> verificarPermissoesLocalizacao() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      Get.snackbar(
        'Erro',
        'Serviços de localização estão desativados',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        Get.snackbar(
          'Erro',
          'Permissões de localização negadas',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      Get.snackbar(
        'Erro',
        'Permissões de localização permanentemente negadas. Por favor, habilite nas configurações.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    return true;
  }

  Future<void> atualizarTemposDistancia() async {
    try {
      await _calcularTempoDistancia(); // Calculando o tempo de distância em tempo real
      await _obterTempoMedioAtendimento();
    } on AppException catch (e) {
      debugPrint('Erro ao atualizar tempos: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao atualizar tempos: $e');
    }
  }

  Future<void> registrarEntrada() async {
    try {
      final fila = ParseObject('Fila')
        ..objectId = filaId.value
        ..set('data_entrada', DateTime.now());
      await fila.save();
    } on AppException catch (e) {
      debugPrint('Erro ao registrar entrada: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao registrar entrada: $e');
    }
  }

  Future<void> registrarInicioAtendimento() async {
    try {
      final fila = ParseObject('Fila')
        ..objectId = filaId.value
        ..set('data_inicio_atendimento', DateTime.now());
      await fila.save();

      debugPrint('Início do atendimento registrado com sucesso.');
    } on AppException catch (e) {
      debugPrint('Erro ao registrar início do atendimento: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao registrar início do atendimento: $e');
    }
  }

  Future<void> registrarFimAtendimento() async {
    try {
      final dataFim = DateTime.now();
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value);

      final response = await queryFila.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final fila = response.results!.first;
        final dataInicio = fila.get<DateTime>('data_inicio_atendimento');

        if (dataInicio != null) {
          final duracao = dataFim.difference(dataInicio).inMinutes;

          // Atualizando os dados na instância de Fila
          fila
            ..set('data_fim_atendimento', dataFim)
            ..set('tempo_atendimento', duracao);

          await fila.save();

          debugPrint(
              'Fim do atendimento registrado com sucesso. Tempo de atendimento: $duracao minutos');
        } else {
          debugPrint('Erro: data de início do atendimento não encontrada.');
        }
      } else {
        debugPrint('Erro: Fila não encontrada.');
      }
    } on AppException catch (e) {
      debugPrint('Erro ao registrar fim do atendimento: ${e.message}');
    } catch (e) {
      debugPrint('Erro desconhecido ao registrar fim do atendimento: $e');
    }
  }

  Future<void> _buscarMensagens() async {
    try {
      if (filaId.isEmpty) {
        debugPrint(
            '🔍 [MENSAGENS] FilaId vazio, não é possível buscar mensagens');
        return;
      }

      debugPrint(
          '🔍 [MENSAGENS] Buscando mensagens para fila: ${filaId.value}');

      // ✅ PRIMEIRA ESTRATÉGIA: Buscar mensagens do médico/consultório
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value)
        ..includeObject(['medico', 'consultorio']);

      final filaResponse = await queryFila.query();

      if (!filaResponse.success || filaResponse.results?.isEmpty == true) {
        debugPrint('🔍 [MENSAGENS] Erro ao obter dados da fila');
        return;
      }

      final fila = filaResponse.results!.first;
      final medico = fila.get<ParseObject>('medico');
      final consultorio = fila.get<ParseObject>('consultorio');

      if (medico?.objectId == null || consultorio?.objectId == null) {
        debugPrint('🔍 [MENSAGENS] Médico ou consultório não encontrado');
        return;
      }

      // ✅ BUSCAR APENAS MENSAGENS RECENTES (ÚLTIMAS 2 HORAS)
      final agora = DateTime.now();
      final duasHorasAtras = agora.subtract(const Duration(hours: 2));

      final query = QueryBuilder<ParseObject>(ParseObject('MensagemFila'))
        ..whereEqualTo('medico_id', medico)
        ..whereEqualTo('consultorio_id', consultorio)
        ..whereGreaterThan('createdAt', duasHorasAtras) // ✅ FILTRO DE TEMPO
        ..whereNotEqualTo('expirada', true) // ✅ EXCLUIR MENSAGENS EXPIRADAS
        ..includeObject(['medico_id', 'consultorio_id'])
        ..orderByDescending('createdAt')
        ..setLimit(10); // Reduzido para 10 mensagens mais recentes

      final response = await query.query();

      debugPrint(
          '🔍 [MENSAGENS] Resposta: success=${response.success}, count=${response.results?.length ?? 0}');

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final List<MensagemFila> novasMensagens = response.results!.map((m) {
          final mensagem = MensagemFila(
            id: m.objectId ?? 'unknown',
            titulo: m.get<String>('titulo') ?? 'Aviso',
            texto: m.get<String>('mensagem') ?? '',
            dataEnvio: m.get<DateTime>('data_envio') ??
                m.get<DateTime>('createdAt') ??
                DateTime.now(),
            medicoId: medico.objectId ?? '',
            prioridade: m.get<String>('prioridade') ?? 'media',
            icone: m.get<String>('icone') ?? 'notification',
          );

          debugPrint('🔍 [MENSAGEM] ${mensagem.titulo}: ${mensagem.texto}');
          return mensagem;
        }).toList();

        // ✅ FILTRAR MENSAGENS POR RELEVÂNCIA TEMPORAL
        final mensagensValidas = novasMensagens.where((mensagem) {
          final idade = agora.difference(mensagem.dataEnvio);

          // Mensagens de atraso expiram em 1 hora
          if (mensagem.titulo.toLowerCase().contains('atraso') &&
              idade.inHours >= 1) {
            return false;
          }

          // Mensagens gerais expiram em 2 horas
          if (idade.inHours >= 2) {
            return false;
          }

          return true;
        }).toList();

        // ✅ SUBSTITUIR mensagens antigas por novas (evitar duplicatas)
        mensagens.value = mensagensValidas;

        debugPrint(
            '🔍 [MENSAGENS] ✅ ${mensagensValidas.length} mensagens válidas carregadas (${novasMensagens.length} total, ${novasMensagens.length - mensagensValidas.length} filtradas)');
      } else {
        debugPrint(
            '🔍 [MENSAGENS] Nenhuma mensagem encontrada ou erro: ${response.error?.message}');

        // ✅ LIMPAR MENSAGENS ANTIGAS SE NÃO HOUVER NOVAS
        mensagens.value = [];
      }
    } catch (e) {
      debugPrint('🔍 [MENSAGENS] ❌ Erro ao buscar mensagens: $e');
    }
  }

  void _iniciarAtualizacaoMensagens() {
    mensagensTimer?.cancel();

    // ✅ OTIMIZAÇÃO PARA ATENDIMENTO: Reduzir frequência de busca de mensagens
    final intervaloMensagens = statusAtual.value == 'em_atendimento'
        ? const Duration(seconds: 30) // 30 segundos quando em atendimento
        : const Duration(seconds: 15); // 15 segundos quando aguardando

    mensagensTimer = Timer.periodic(intervaloMensagens, (_) {
      if (!isSaindo.value) {
        _buscarMensagens();
      }
    });
  }

  Future<void> _calcularTempoEstimado(ParseObject fila) async {
    try {
      final medico = fila.get<ParseObject>('medico');
      final consultorio = fila.get<ParseObject>('consultorio');

      // ✅ SEMPRE CALCULAR TEMPO - MESMO SEM MÉDICO/CONSULTÓRIO
      int tempoMedioCalculado;

      if (medico != null && consultorio != null) {
        // Usar inteligência avançada
        tempoMedioCalculado =
            await _calcularTempoMedioInteligente(medico, consultorio);
        debugPrint(
            '🧠 [INTELIGÊNCIA] Tempo calculado com dados do médico: ${tempoMedioCalculado}min');
      } else {
        // Fallback: usar tempo padrão genérico
        tempoMedioCalculado = 15;
        debugPrint(
            '🧠 [INTELIGÊNCIA] Usando tempo padrão genérico: ${tempoMedioCalculado}min');
      }

      // Calcular tempo estimado baseado na posição na fila
      final pacientesAFrente = math.max(0, posicaoAtual.value - 1);
      final tempoEstimadoMinutos = pacientesAFrente * tempoMedioCalculado;

      // ✅ SEMPRE DEFINIR UM TEMPO (nunca deixar zerado)
      tempoEstimado.value = math.max(tempoEstimadoMinutos, 0);

      // Se for o primeiro da fila, mostrar tempo mínimo de 5 minutos
      if (posicaoAtual.value == 1 && tempoEstimado.value == 0) {
        tempoEstimado.value = 5;
      }

      debugPrint('🧠 [INTELIGÊNCIA] Tempo médio: ${tempoMedioCalculado}min');
      debugPrint('🧠 [INTELIGÊNCIA] Posição na fila: ${posicaoAtual.value}');
      debugPrint('🧠 [INTELIGÊNCIA] Pacientes à frente: $pacientesAFrente');
      debugPrint(
          '🧠 [INTELIGÊNCIA] ✅ TEMPO FINAL CALCULADO: ${tempoEstimado.value}min');
    } catch (e) {
      debugPrint('❌ Erro ao calcular tempo estimado: $e');

      // ✅ FALLBACK GARANTIDO - NUNCA DEIXAR ZERADO
      final pacientesAFrente = math.max(0, posicaoAtual.value - 1);
      tempoEstimado.value = math.max(pacientesAFrente * 15, 5); // Mínimo 5 min

      debugPrint('🧠 [FALLBACK] Tempo definido: ${tempoEstimado.value}min');
    }
  }

  /// ✅ IA POR ESPECIALIDADE: Tempos baseados em dados médicos reais
  int _obterTempoPorEspecialidade(String especialidade) {
    // IA baseada em estatísticas reais de consultas médicas brasileiras
    final especialidadesTempos = {
      // Especialidades rápidas (10-15 min)
      'clinico geral': 12,
      'clinica geral': 12,
      'pediatria': 15,
      'ginecologia': 18,

      // Especialidades médias (15-25 min)
      'cardiologia': 20,
      'dermatologia': 15,
      'oftalmologia': 18,
      'otorrinolaringologia': 20,
      'neurologia': 25,
      'endocrinologia': 22,
      'gastroenterologia': 25,

      // Especialidades longas (25-40 min)
      'psiquiatria': 35,
      'ortopedia': 20,
      'urologia': 18,
      'oncologia': 30,
      'reumatologia': 25,

      // Procedimentos especiais
      'cirurgia': 45,
      'emergencia': 30,
      'pronto socorro': 25,
    };

    // Buscar por especialidade exata ou similar
    for (final entry in especialidadesTempos.entries) {
      if (especialidade.contains(entry.key) ||
          entry.key.contains(especialidade)) {
        debugPrint('🧠 [IA_ESPECIALIDADE] $especialidade → ${entry.value}min');
        return entry.value;
      }
    }

    // Fallback padrão
    debugPrint(
        '🧠 [IA_ESPECIALIDADE] Especialidade desconhecida: $especialidade → 15min');
    return 15;
  }

  /// ✅ NOVA INTELIGÊNCIA: Calcula tempo médio baseado nos últimos atendimentos reais
  Future<int> _calcularTempoMedioInteligente(
      ParseObject medico, ParseObject consultorio) async {
    try {
      debugPrint('🧠 [IA_AVANÇADA] Iniciando cálculo inteligente...');

      // ✅ ALGORITMO DE IA: Buscar padrões históricos
      final agora = DateTime.now();
      final seteDiasAtras = agora.subtract(const Duration(days: 7));
      final trintaDiasAtras = agora.subtract(const Duration(days: 30));

      // 🤖 ESTRATÉGIA 1: Dados recentes (últimos 7 dias) - PESO 60%
      final tempoRecente =
          await _calcularTempoRecente(medico, consultorio, seteDiasAtras);

      // 🤖 ESTRATÉGIA 2: Dados históricos (30 dias) - PESO 30%
      final tempoHistorico =
          await _calcularTempoHistorico(medico, consultorio, trintaDiasAtras);

      // 🤖 ESTRATÉGIA 3: Padrão por horário do dia - PESO 10%
      final tempoHorario = await _calcularTempoPorHorario(medico, consultorio);

      // 🤖 ALGORITMO DE MACHINE LEARNING: Média ponderada inteligente
      int tempoFinal;

      if (tempoRecente > 0 && tempoHistorico > 0) {
        // IA AVANÇADA: Combinar dados com pesos
        tempoFinal = ((tempoRecente * 0.6) +
                (tempoHistorico * 0.3) +
                (tempoHorario * 0.1))
            .round();
        debugPrint('🤖 [ML] Dados recentes: ${tempoRecente}min (60%)');
        debugPrint('🤖 [ML] Dados históricos: ${tempoHistorico}min (30%)');
        debugPrint('🤖 [ML] Padrão horário: ${tempoHorario}min (10%)');
      } else if (tempoRecente > 0) {
        // Usar apenas dados recentes
        tempoFinal = tempoRecente;
        debugPrint('🤖 [ML] Usando apenas dados recentes: ${tempoFinal}min');
      } else if (tempoHistorico > 0) {
        // Usar apenas dados históricos
        tempoFinal = tempoHistorico;
        debugPrint('🤖 [ML] Usando apenas dados históricos: ${tempoFinal}min');
      } else {
        // Fallback para especialidade
        final especialidadeMedico = medico.get<String>('especialidade') ?? '';
        tempoFinal =
            _obterTempoPorEspecialidade(especialidadeMedico.toLowerCase());
        debugPrint('🤖 [ML] Fallback para especialidade: ${tempoFinal}min');
      }

      // ✅ VALIDAÇÃO DE IA: Garantir valores realistas (5-60 min)
      tempoFinal = math.max(5, math.min(60, tempoFinal));

      debugPrint('🧠 [IA_AVANÇADA] ✅ TEMPO FINAL CALCULADO: ${tempoFinal}min');
      return tempoFinal;
    } catch (e) {
      debugPrint('❌ Erro na IA avançada: $e');
      // Fallback inteligente baseado em especialidade
      final especialidade = medico.get<String>('especialidade') ?? 'geral';
      return _obterTempoPorEspecialidade(especialidade.toLowerCase());
    }
  }

  /// 🤖 IA ESTRATÉGIA 1: Cálculo baseado em dados recentes
  Future<int> _calcularTempoRecente(ParseObject medico, ParseObject consultorio,
      DateTime seteDiasAtras) async {
    try {
      final queryRecente = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', medico)
        ..whereEqualTo('consultorio', consultorio)
        ..whereEqualTo('status', 'atendido')
        ..whereGreaterThan('data_fim_atendimento', seteDiasAtras)
        ..orderByDescending('data_fim_atendimento')
        ..setLimit(10); // Últimos 10 atendimentos

      final response = await queryRecente.query();

      if (response.success && response.results?.isNotEmpty == true) {
        final tempos = <int>[];

        for (final atendimento in response.results!) {
          final inicio = atendimento.get<DateTime>('data_inicio_atendimento');
          final fim = atendimento.get<DateTime>('data_fim_atendimento');

          if (inicio != null && fim != null) {
            final duracao = fim.difference(inicio).inMinutes;
            if (duracao > 0 && duracao <= 120) {
              // Filtrar tempos realistas
              tempos.add(duracao);
            }
          }
        }

        if (tempos.isNotEmpty) {
          final tempoMedio =
              (tempos.reduce((a, b) => a + b) / tempos.length).round();
          debugPrint(
              '🤖 [DADOS_RECENTES] ${tempos.length} atendimentos → ${tempoMedio}min');
          return tempoMedio;
        }
      }

      return 0; // Sem dados recentes
    } catch (e) {
      debugPrint('❌ Erro nos dados recentes: $e');
      return 0;
    }
  }

  /// 🤖 IA ESTRATÉGIA 2: Cálculo baseado em dados históricos (30 dias)
  Future<int> _calcularTempoHistorico(ParseObject medico,
      ParseObject consultorio, DateTime trintaDiasAtras) async {
    try {
      final queryHistorico = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', medico)
        ..whereEqualTo('consultorio', consultorio)
        ..whereEqualTo('status', 'atendido')
        ..whereGreaterThan('data_fim_atendimento', trintaDiasAtras)
        ..orderByDescending('data_fim_atendimento')
        ..setLimit(30); // Últimos 30 atendimentos históricos

      final response = await queryHistorico.query();

      if (response.success && response.results?.isNotEmpty == true) {
        final tempos = <int>[];

        for (final atendimento in response.results!) {
          final inicio = atendimento.get<DateTime>('data_inicio_atendimento');
          final fim = atendimento.get<DateTime>('data_fim_atendimento');

          if (inicio != null && fim != null) {
            final duracao = fim.difference(inicio).inMinutes;
            if (duracao > 0 && duracao <= 120) {
              // Filtrar tempos realistas
              tempos.add(duracao);
            }
          }
        }

        if (tempos.isNotEmpty) {
          // ✅ ALGORITMO ML: Remover outliers e calcular média robusta
          tempos.sort();
          final inicio = (tempos.length * 0.1).floor(); // Remove 10% menores
          final fim = (tempos.length * 0.9).ceil(); // Remove 10% maiores

          if (fim > inicio) {
            final temposFiltrados = tempos.sublist(inicio, fim);
            final tempoMedio = (temposFiltrados.reduce((a, b) => a + b) /
                    temposFiltrados.length)
                .round();
            debugPrint(
                '🤖 [DADOS_HISTÓRICOS] ${tempos.length} → ${temposFiltrados.length} (sem outliers) → ${tempoMedio}min');
            return tempoMedio;
          }
        }
      }

      return 0; // Sem dados históricos
    } catch (e) {
      debugPrint('❌ Erro nos dados históricos: $e');
      return 0;
    }
  }

  /// 🤖 IA ESTRATÉGIA 3: Cálculo baseado no padrão por horário do dia
  Future<int> _calcularTempoPorHorario(
      ParseObject medico, ParseObject consultorio) async {
    try {
      final agora = DateTime.now();
      final horaAtual = agora.hour;

      // ✅ ALGORITMO DE IA: Diferentes padrões por período do dia
      String periodoAjuste;
      double fatorHorario;

      if (horaAtual >= 7 && horaAtual <= 9) {
        // Manhã cedo: atendimentos mais rápidos
        periodoAjuste = 'manhã cedo';
        fatorHorario = 0.85;
      } else if (horaAtual >= 10 && horaAtual <= 12) {
        // Meio da manhã: tempo normal
        periodoAjuste = 'meio da manhã';
        fatorHorario = 1.0;
      } else if (horaAtual >= 13 && horaAtual <= 15) {
        // Início da tarde: pode ser mais lento (pós-almoço)
        periodoAjuste = 'início da tarde';
        fatorHorario = 1.15;
      } else if (horaAtual >= 16 && horaAtual <= 18) {
        // Final da tarde: mais rápido (pressa para finalizar)
        periodoAjuste = 'final da tarde';
        fatorHorario = 0.95;
      } else {
        // Outros horários: padrão
        periodoAjuste = 'outros horários';
        fatorHorario = 1.0;
      }

      // Buscar especialidade para tempo base
      final especialidade = medico.get<String>('especialidade') ?? 'geral';
      final tempoBase =
          _obterTempoPorEspecialidade(especialidade.toLowerCase());

      final tempoAjustado = (tempoBase * fatorHorario).round();

      debugPrint(
          '🤖 [PADRÃO_HORÁRIO] $periodoAjuste (${horaAtual}h) → fator $fatorHorario → ${tempoAjustado}min');

      return tempoAjustado;
    } catch (e) {
      debugPrint('❌ Erro no padrão por horário: $e');
      return 15; // Fallback padrão
    }
  }

  Future<void> _obterTotalPacientesFila() async {
    try {
      debugPrint('[DEBUG] Iniciando consulta do total de pacientes na fila');
      debugPrint('[DEBUG] FilaId: ${filaId.value}');

      if (filaId.isEmpty) {
        debugPrint('[DEBUG] FilaId vazio, não é possível consultar');
        return;
      }

      // Primeiro, buscar a fila atual para obter o médico
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value)
        ..includeObject(['medico']);

      final response = await queryFila.query();

      if (response.success && response.results?.isNotEmpty == true) {
        final fila = response.results!.first;
        final medicoObj = fila.get<ParseObject>('medico');

        if (medicoObj != null) {
          debugPrint(
              '[DEBUG] Médico encontrado: ${medicoObj.get<String>('nome')} (ID: ${medicoObj.objectId})');

          // Agora, buscar todas as filas do médico que estão ativas (incluindo pausadas)
          final queryFilasMedico =
              QueryBuilder<ParseObject>(ParseObject('Fila'))
                ..whereEqualTo('medico', medicoObj)
                ..whereContainedIn('status', [
                  'aguardando',
                  'em_atendimento',
                  'pausado_emergencia'
                ]); // ✅ INCLUIR PAUSADO_EMERGENCIA

          final responseFilas = await queryFilasMedico.query();

          if (responseFilas.success && responseFilas.results != null) {
            totalPacientesFila.value = responseFilas.results!.length;
            debugPrint(
                '[DEBUG] Total de pacientes na fila do médico ${medicoObj.objectId}: ${totalPacientesFila.value}');

            // Log detalhado dos pacientes
            for (int i = 0; i < responseFilas.results!.length; i++) {
              final pacienteFila = responseFilas.results![i];
              final nome = pacienteFila.get<String>('nome') ?? 'Sem nome';
              final posicao = pacienteFila.get<int>('posicao') ?? 0;
              final status =
                  pacienteFila.get<String>('status') ?? 'desconhecido';
              debugPrint(
                  '[DEBUG] Paciente ${i + 1}: $nome (Posição: $posicao, Status: $status)');
            }
          } else {
            debugPrint(
                '[DEBUG] Erro ao consultar todas as filas do médico: ${responseFilas.error?.message ?? 'Erro desconhecido'}');
          }
        } else {
          debugPrint('[DEBUG] Médico não encontrado na fila');
        }
      } else {
        debugPrint(
            '[DEBUG] Erro ao buscar fila atual: ${response.error?.message ?? 'Erro desconhecido'}');
      }
    } catch (e) {
      debugPrint('[DEBUG] Erro ao consultar todas as filas do médico: $e');
    }
  }

  Future<void> atualizarDados() async {
    try {
      await _verificarStatusFila();
      await _buscarMensagens();
      await _calcularTempoDistancia();
      await _obterTotalPacientesFila(); // Atualizar o total de pacientes

      // Adicionar feedback visual sobre a atualização
      Get.snackbar(
        'Atualizado',
        'Dados da fila atualizados com sucesso',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.teal.withOpacity(0.7),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
        margin: const EdgeInsets.only(bottom: 20, left: 20, right: 20),
        borderRadius: 10,
        icon: const Icon(Icons.refresh, color: Colors.white),
      );
    } catch (e) {
      debugPrint('Erro ao atualizar dados: $e');
      Get.snackbar(
        'Erro',
        'Não foi possível atualizar os dados da fila',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }

  Future<void> _buscarMetricas() async {
    try {
      // Obtém a fila atual para acessar as referências do médico e consultório
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value)
        ..includeObject(['medico', 'consultorio']);

      final filaResponse = await queryFila.query();

      if (!filaResponse.success ||
          filaResponse.results == null ||
          filaResponse.results!.isEmpty) {
        return;
      }

      final filaParse = filaResponse.results!.first;
      final medico = filaParse.get<ParseObject>('medico');
      final consultorio = filaParse.get<ParseObject>('consultorio');

      if (medico == null || consultorio == null) {
        return;
      }

      // Consulta as métricas de atendimento para este médico e consultório
      final queryMetricas =
          QueryBuilder<ParseObject>(ParseObject('MetricasAtendimento'))
            ..whereEqualTo('medico_id', medico)
            ..whereEqualTo('consultorio_id', consultorio)
            ..orderByDescending('data')
            ..setLimit(1);

      final metricasResponse = await queryMetricas.query();

      if (metricasResponse.success &&
          metricasResponse.results != null &&
          metricasResponse.results!.isNotEmpty) {
        final metricas = metricasResponse.results!.first;
        final tempoMedioEspera =
            metricas.get<num>('tempo_medio_espera')?.toInt() ?? 0;
        final tempoMedioAtendimento =
            metricas.get<num>('tempo_medio_atendimento')?.toInt() ?? 0;

        // Atualiza o tempo estimado considerando a posição atual e os tempos médios
        final calculatedTime = (posicaoAtual.value - 1) *
            (tempoMedioAtendimento > 0 ? tempoMedioAtendimento : 15);

        tempoEstimado.value = calculatedTime.toInt();

        debugPrint(
            'Métricas atualizadas: Tempo médio de espera: $tempoMedioEspera min, '
            'Tempo médio de atendimento: $tempoMedioAtendimento min');
      }
    } catch (e) {
      debugPrint('Erro ao buscar métricas: $e');
    }
  }

  Future<bool> atualizarStatusFila(String status) async {
    if (filaId.isEmpty || isSaindo.value) return false;

    try {
      isSaindo.value = true;

      // Buscar a fila para obter o ID do paciente
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value)
        ..whereEqualTo('status', 'aguardando');

      final response = await queryFila.query();

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        throw Exception('Fila não encontrada ou já finalizada');
      }

      final fila = response.results!.first;
      final idPaciente = fila.get<String>('idPaciente');

      if (idPaciente == null) {
        throw Exception('ID do paciente não encontrado na fila');
      }

      // Usar o método Fila.updateStatus para atualizar o status
      final success = await Fila.updateStatus(
        filaId: filaId.value,
        status: status,
        idPaciente: idPaciente,
      );

      if (!success) {
        throw Exception('Não foi possível atualizar a fila');
      }

      return true;
    } catch (e) {
      debugPrint('Erro ao atualizar status: $e');
      return false;
    } finally {
      isSaindo.value = false;
    }
  }

  // ✅ Método para iniciar o contador de duração do atendimento - TIMEZONE BRASIL
  void _iniciarContadorDuracao() {
    duracaoTimer?.cancel();

    // Definir duração inicial com base na data de início (horário do Brasil)
    if (dataInicioAtendimento.value != null) {
      final inicioAtendimento =
          BrazilTimeZone.parseObjectDateToBrazil(dataInicioAtendimento.value!);
      final agora = BrazilTimeZone.now();
      if (inicioAtendimento != null) {
        final diferencaMinutos = agora.difference(inicioAtendimento).inMinutes;
        duracaoAtendimento.value = diferencaMinutos;
      } else {
        duracaoAtendimento.value = 0;
      }
    } else {
      duracaoAtendimento.value = 0;
      dataInicioAtendimento.value = BrazilTimeZone.now();
    }

    // Atualizar a cada minuto
    duracaoTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      if (statusAtual.value != 'em_atendimento') {
        timer.cancel();
        return;
      }

      if (dataInicioAtendimento.value != null) {
        final inicioAtendimento = BrazilTimeZone.parseObjectDateToBrazil(
            dataInicioAtendimento.value!);
        final agora = BrazilTimeZone.now();
        if (inicioAtendimento != null) {
          final diferencaMinutos =
              agora.difference(inicioAtendimento).inMinutes;
          duracaoAtendimento.value = diferencaMinutos;
        }
      } else {
        duracaoAtendimento.value++;
      }
    });
  }

  // Método para registrar emergências
  Future<bool> registrarEmergencia(String tipo) async {
    try {
      if (filaId.isEmpty) {
        debugPrint('❌ [EMERGÊNCIA] Erro: filaId está vazio');
        return false;
      }

      debugPrint(
          '🚨 [EMERGÊNCIA] Iniciando registro de emergência tipo: $tipo para fila: ${filaId.value}');
      isLoading.value = true;

      // ✅ VALIDAR DADOS ANTES DE CRIAR REGISTRO
      final filaObj = ParseObject('Fila')..objectId = filaId.value;

      // Verificar se a fila existe
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value);

      final filaResponse = await queryFila.query();

      if (!filaResponse.success || filaResponse.results?.isEmpty == true) {
        debugPrint('❌ [EMERGÊNCIA] Fila não encontrada: ${filaId.value}');
        return false;
      }

      // Criar registro de emergência
      final emergencia = ParseObject('Emergencia')
        ..set('fila', filaObj)
        ..set('tipo', tipo)
        ..set('detalhes', 'Solicitação feita pelo paciente durante atendimento')
        ..set('data_registro',
            BrazilTimeZone.createForParse(BrazilTimeZone.now()))
        ..set('status', 'pendente')
        ..set('prioridade', _obterPrioridadeEmergencia(tipo));

      // ✅ CONFIGURAR ACL PARA PERMITIR LEITURA PELA SECRETARIA
      final acl = ParseACL();
      acl.setPublicReadAccess(allowed: true);
      acl.setPublicWriteAccess(allowed: false);
      emergencia.setACL(acl);

      debugPrint('🚨 [EMERGÊNCIA] Salvando registro local de emergência...');
      final result = await emergencia.save();

      if (result.success) {
        debugPrint(
            '✅ [EMERGÊNCIA] Registro local salvo com sucesso - ID: ${emergencia.objectId}');

        // ✅ NOTIFICAR VIA CLOUD FUNCTION COM RETRY
        bool cloudSuccess = false;
        int tentativas = 0;
        const maxTentativas = 3;

        while (!cloudSuccess && tentativas < maxTentativas) {
          tentativas++;

          try {
            final params = {
              'filaId': filaId.value,
              'tipo': tipo,
              'hospital': hospitalNome.value.isNotEmpty
                  ? hospitalNome.value
                  : 'Hospital',
              'medico':
                  medicoNome.value.isNotEmpty ? medicoNome.value : 'Médico',
              'emergenciaId': emergencia.objectId,
            };

            debugPrint(
                '🚨 [EMERGÊNCIA] Tentativa $tentativas/$maxTentativas - Enviando notificação via cloud function...');
            debugPrint('🚨 [EMERGÊNCIA] Parâmetros: $params');

            final cloudResult = await ParseCloudFunction('notificarEmergencia')
                .execute(parameters: params);

            if (cloudResult.success) {
              debugPrint('✅ [EMERGÊNCIA] Notificação enviada com sucesso!');
              debugPrint('✅ [EMERGÊNCIA] Resposta: ${cloudResult.result}');
              cloudSuccess = true;
            } else {
              debugPrint(
                  '⚠️ [EMERGÊNCIA] Cloud function retornou erro (tentativa $tentativas): ${cloudResult.error?.message}');

              if (tentativas < maxTentativas) {
                await Future.delayed(
                    Duration(seconds: tentativas * 2)); // Delay progressivo
              }
            }
          } catch (cloudError) {
            debugPrint(
                '⚠️ [EMERGÊNCIA] Erro ao enviar notificação via cloud function (tentativa $tentativas): $cloudError');

            if (tentativas < maxTentativas) {
              await Future.delayed(
                  Duration(seconds: tentativas * 2)); // Delay progressivo
            }
          }
        }

        if (!cloudSuccess) {
          debugPrint(
              '⚠️ [EMERGÊNCIA] Falha ao enviar notificação após $maxTentativas tentativas');
          // Mesmo assim, consideramos sucesso pois o registro local foi salvo
        }

        debugPrint('✅ [EMERGÊNCIA] Emergência registrada com sucesso!');
        return true;
      } else {
        debugPrint(
            '❌ [EMERGÊNCIA] Erro ao salvar registro local: ${result.error?.message}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [EMERGÊNCIA] Erro geral ao registrar emergência: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// ✅ OBTER PRIORIDADE BASEADA NO TIPO DE EMERGÊNCIA
  String _obterPrioridadeEmergencia(String tipo) {
    switch (tipo.toLowerCase()) {
      case 'sos':
        return 'critical';
      case 'denuncia':
        return 'high';
      case 'ajuda':
        return 'medium';
      default:
        return 'low';
    }
  }

  /// ✅ NOVO MÉTODO: Recupera dados salvos pelo FilaRecoveryService
  Future<void> _recuperarDadosSalvos() async {
    try {
      final estadoSalvo = await FilaStateController.recuperarEstadoFila();
      if (estadoSalvo != null && estadoSalvo.isNotEmpty) {
        filaId.value = estadoSalvo['filaId'] ?? '';
        medicoNome.value = estadoSalvo['medicoNome'] ?? '';
        especialidade.value = estadoSalvo['especialidade'] ?? '';
        posicaoAtual.value = estadoSalvo['posicao'] ?? 0;
        statusAtual.value = estadoSalvo['status'] ?? 'aguardando';
        print(
            '[FILA_CONTROLLER] 🔄 Dados recuperados do FilaStateController: ${filaId.value}');
      }
    } catch (e) {
      print('[FILA_CONTROLLER] ⚠️ Erro ao recuperar dados salvos: $e');
    }
  }

  /// ✅ NOVO MÉTODO: Calcula tempo estimado inicial (mesmo sem fila completa)
  Future<void> _calcularTempoEstimadoInicial() async {
    try {
      if (filaId.isNotEmpty) {
        // Tentar buscar dados da fila para cálculo preciso
        final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
          ..whereEqualTo('objectId', filaId.value)
          ..includeObject(['medico', 'consultorio']);

        final response = await queryFila.query();

        if (response.success && response.results?.isNotEmpty == true) {
          await _calcularTempoEstimado(response.results!.first);
          return;
        }
      }

      // ✅ FALLBACK: Calcular com dados básicos disponíveis
      debugPrint('🧠 [INICIAL] Calculando tempo com dados básicos...');

      final pacientesAFrente = math.max(0, posicaoAtual.value - 1);
      final especialidadePadrao = especialidade.value.toLowerCase();
      final tempoPadrao = _obterTempoPorEspecialidade(especialidadePadrao);

      tempoEstimado.value = math.max(pacientesAFrente * tempoPadrao, 5);

      debugPrint('🧠 [INICIAL] Especialidade: $especialidadePadrao');
      debugPrint('🧠 [INICIAL] Tempo por especialidade: ${tempoPadrao}min');
      debugPrint('🧠 [INICIAL] Posição: ${posicaoAtual.value}');
      debugPrint('🧠 [INICIAL] ✅ TEMPO INICIAL: ${tempoEstimado.value}min');
    } catch (e) {
      debugPrint('❌ Erro no cálculo inicial: $e');
      // Garantir que sempre tenha um tempo
      tempoEstimado.value = math.max((posicaoAtual.value - 1) * 15, 5);
    }
  }

  /// ✅ VERIFICAR EMERGÊNCIAS ATIVAS
  Future<void> verificarEmergenciasAtivas() async {
    try {
      if (filaId.isEmpty) return;

      // Buscar fila atual para verificar status
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId.value)
        ..includeObject(['medico']);

      final response = await queryFila.query();

      if (response.success && response.results?.isNotEmpty == true) {
        final fila = response.results!.first;
        final status = fila.get<String>('status') ?? 'aguardando';
        final statusAnterior = filaEmEmergencia.value;

        debugPrint(
            '🔍 [EMERGÊNCIA CHECK] Status atual: $status, Emergência anterior: $statusAnterior');

        // Verificar se está pausado por emergência
        if (status == 'pausado_emergencia') {
          final motivo = fila.get<String>('motivo_pausa') ?? 'Emergência';
          final dataPausa = fila.get<DateTime>('data_pausa');

          filaEmEmergencia.value = true;
          motivoEmergencia.value = motivo;
          statusEmergencia.value = 'pausado_emergencia';

          debugPrint('🚨 [EMERGÊNCIA] Fila pausada: $motivo');

          if (dataPausa != null) {
            final tempoDecorrido =
                DateTime.now().difference(dataPausa).inMinutes;
            debugPrint('🕐 [EMERGÊNCIA] Pausada há $tempoDecorrido minutos');
          }

          // Buscar detalhes da emergência ativa
          final medicoObj = fila.get<ParseObject>('medico');
          if (medicoObj?.objectId != null) {
            await _buscarDetalhesEmergencia(medicoObj!.objectId!);
          }

          // ✅ DETERMINAR TIPO DE EMERGÊNCIA baseado no motivo/detalhes
          if (motivo.toLowerCase().contains('cirurgia')) {
            tipoEmergencia.value = 'cirurgia';
          } else if (motivo.toLowerCase().contains('emergência médica') ||
              motivo.toLowerCase().contains('emergencia medica')) {
            tipoEmergencia.value = 'emergencia_medica';
          } else if (motivo.toLowerCase().contains('secretaria') ||
              motivo.toLowerCase().contains('técnico') ||
              motivo.toLowerCase().contains('energia')) {
            tipoEmergencia.value = 'secretaria';
          } else {
            tipoEmergencia.value = 'geral';
          }
        } else {
          // ✅ MUDANÇA DETECTADA: Emergência foi finalizada
          if (statusAnterior == true) {
            debugPrint(
                '✅ [EMERGÊNCIA] Emergência finalizada! Status mudou para: $status');
          }

          // Limpar estado de emergência se não estiver pausado
          filaEmEmergencia.value = false;
          motivoEmergencia.value = '';
          tipoEmergencia.value = '';
          statusEmergencia.value = '';

          debugPrint(
              '🟢 [EMERGÊNCIA] Estado de emergência limpo - fila retornou ao normal');
        }
      }
    } catch (e) {
      debugPrint('❌ Erro ao verificar emergências: $e');
    }
  }

  /// 🔍 BUSCAR DETALHES DA EMERGÊNCIA
  Future<void> _buscarDetalhesEmergencia(String medicoId) async {
    try {
      // Buscar emergência ativa para este médico usando a classe correta
      final queryEmergencia = QueryBuilder<ParseObject>(
          ParseObject('Emergencia'))
        ..whereEqualTo('medico_id', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('status', 'ativa')
        ..orderByDescending('createdAt')
        ..setLimit(1);

      final response = await queryEmergencia.query();

      if (response.success && response.results?.isNotEmpty == true) {
        final emergencia = response.results!.first;

        tipoEmergencia.value = emergencia.get<String>('tipo') ?? 'geral';
        final tempoEstimado = emergencia.get<int>('tempo_estimado_minutos');

        if (tempoEstimado != null) {
          statusEmergencia.value = 'Previsão: ${tempoEstimado}min';
        } else {
          statusEmergencia.value = 'Aguardando retomada';
        }

        debugPrint(
            '🚨 [EMERGÊNCIA] Tipo: ${tipoEmergencia.value}, Status: ${statusEmergencia.value}');
      }
    } catch (e) {
      debugPrint('❌ Erro ao buscar detalhes da emergência: $e');
    }
  }
}
