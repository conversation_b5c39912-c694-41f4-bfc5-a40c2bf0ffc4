import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:flutter/foundation.dart';
import '../utils/date_utils.dart';
import '../services/push_notification_service.dart';
import '../conexao.dart';

class NotificationController extends GetxController {
  final RxMap<String, int> notificacoes = <String, int>{}.obs;
  final RxBool isAnimating = false.obs;
  // Cache functionality now integrated in PushNotificationService
  PushNotificationService? _pushService;

  // Controle de acesso
  DateTime _lastCheck =
      BrazilTimeZone.now().subtract(const Duration(minutes: 5));
  final int _minCheckIntervalMs = 3000; // 3 segundos entre verificações
  // Removed _normalIntervalMs as it's no longer used after cache removal

  @override
  void onInit() {
    super.onInit();
    _initializePushService();
  }

  void _initializePushService() {
    if (Get.isRegistered<PushNotificationService>()) {
      _pushService = Get.find<PushNotificationService>();
    }
  }

  Future<void> verificarNotificacoes(
      String medicoId, String consultorioId) async {
    // Verificar intervalo mínimo entre verificações
    final agora = BrazilTimeZone.now();
    final elapsed = agora.difference(_lastCheck).inMilliseconds;

    // Se a verificação é frequente demais, ignorar (exceto se for a primeira)
    if (elapsed < _minCheckIntervalMs && notificacoes.isNotEmpty) {
      debugPrint('Verificação de notificações adiada - muito frequente');
      return;
    }

    try {
      // Consulta direta com throttling
      final querySolicitacoes =
          QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
            ..whereEqualTo(
                'medicoId', ParseObject('Medico')..objectId = medicoId)
            ..whereEqualTo('hospitalId',
                ParseObject('consultorio')..objectId = consultorioId)
            ..whereEqualTo('status', 'pendente');

      final resultado = await querySolicitacoes.query();

      int novoNumero = 0;
      if (resultado.success && resultado.results != null) {
        novoNumero = resultado.results!.length;
      }
      final numeroAntigo = notificacoes[medicoId] ?? 0;

      if (novoNumero > numeroAntigo) {
        isAnimating.value = true;
        await Future.delayed(const Duration(milliseconds: 500));
        isAnimating.value = false;
      }

      notificacoes[medicoId] = novoNumero;
      _lastCheck = agora;
      update();
    } catch (e) {
      debugPrint('Erro ao verificar notificações: $e');
    }
  }

  Future<void> marcarComoProcessada(String solicitacaoId) async {
    try {
      final queryFila =
          QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
            ..whereEqualTo('objectId', solicitacaoId);

      final response = await queryFila.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final solicitacao = response.results!.first;
        final medicoId = solicitacao.get<ParseObject>('medicoId')?.objectId;

        if (medicoId != null) {
          notificacoes[medicoId] = (notificacoes[medicoId] ?? 1) - 1;

          // Cache invalidation handled by PushNotificationService
          debugPrint('Notificação processada para médico: $medicoId');

          update();
        }
      }
    } catch (e) {
      debugPrint('Erro ao marcar solicitação como processada: $e');
    }
  }

  Future<List> buscarSolicitacoesPendentes(
      String medicoId, String consultorioId) async {
    try {
      // Consulta direta sem cache manager
      final querySolicitacoes =
          QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
            ..whereEqualTo(
                'medicoId', ParseObject('Medico')..objectId = medicoId)
            ..whereEqualTo('hospitalId',
                ParseObject('consultorio')..objectId = consultorioId)
            ..whereEqualTo('status', 'pendente')
            ..orderByAscending('createdAt');

      final response = await querySolicitacoes.query();

      if (response.success && response.results != null) {
        return response.results!;
      }
      return [];
    } catch (e) {
      debugPrint('Erro ao buscar solicitações: $e');
      return [];
    }
  }

  void marcarTodasComoLidas(String medicoId) {
    notificacoes[medicoId] = 0;
    update();
  }

  // Função para registrar token de notificações push
  Future<bool> registrarTokenNotificacoes(String deviceId, String token) async {
    try {
      final cloudFunction = ParseCloudFunction('registerDeviceForNotifications');
      final resultado = await Conexao.throttledExecuteCloudFunction(
        cloudFunction,
        parameters: {
          'deviceId': deviceId,
          'token': token,
        },
      );

      return resultado.success && (resultado.result as Map<String, dynamic>?)?['success'] == true;
    } catch (e) {
      debugPrint('Erro ao registrar token: $e');
      return false;
    }
  }

  // Função para testar notificações push
  Future<bool> enviarNotificacaoTeste(String deviceId) async {
    try {
      final cloudFunction = ParseCloudFunction('sendTestNotification');
      final resultado = await Conexao.throttledExecuteCloudFunction(
        cloudFunction,
        parameters: {
          'deviceId': deviceId,
          'message':
              'Esta é uma notificação de teste enviada em ${BrazilTimeZone.now()}',
        },
      );

      return resultado.success && (resultado.result as Map<String, dynamic>?)?['success'] == true;
    } catch (e) {
      debugPrint('Erro ao enviar notificação de teste: $e');
      return false;
    }
  }

  // Função para enviar notificação via novo sistema FCM
  Future<bool> enviarNotificacaoFCM({
    required String pacienteId,
    required String titulo,
    required String mensagem,
    Map<String, dynamic>? dados,
    String tipo = 'geral',
    String prioridade = 'normal',
  }) async {
    try {
      final cloudFunction = ParseCloudFunction('enviarNotificacaoFCM');
      final resultado = await Conexao.throttledExecuteCloudFunction(
        cloudFunction,
        parameters: {
          'pacienteId': pacienteId,
          'titulo': titulo,
          'mensagem': mensagem,
          'dados': dados ?? {},
          'tipo': tipo,
          'prioridade': prioridade,
        },
      );

      final sucesso = resultado.success && 
          (resultado.result as Map<String, dynamic>?)?['success'] == true;

      if (sucesso) {
        debugPrint('✅ Notificação FCM enviada com sucesso para paciente: $pacienteId');
      } else {
        final erro = (resultado.result as Map<String, dynamic>?)?['error'] ?? 'Erro desconhecido';
        debugPrint('❌ Erro ao enviar notificação FCM: $erro');
      }

      return sucesso;
    } catch (e) {
      debugPrint('❌ Erro ao enviar notificação FCM: $e');
      return false;
    }
  }

  // Função especial para notificações de aceitação na fila
  Future<bool> notificarAceitacaoFila({
    required String pacienteId,
    required String nomePaciente,
    required String nomeMedico,
    required String especialidade,
    required int posicao,
    String? filaId,
    String? hospitalNome,
  }) async {
    return await enviarNotificacaoFCM(
      pacienteId: pacienteId,
      titulo: '✅ Solicitação Aceita!',
      mensagem: 'Sua consulta com Dr. $nomeMedico foi aprovada! Você está na ${posicao}ª posição da fila.',
      dados: {
        'filaId': filaId ?? '',
        'nomeMedico': nomeMedico,
        'especialidade': especialidade,
        'posicao': posicao.toString(),
        'hospitalNome': hospitalNome ?? '',
        'action': 'open_fila_screen',
      },
      tipo: 'aceito_fila',
      prioridade: 'high',
    );
  }

  // Função para diagnosticar problemas de notificação
  Future<Map<String, dynamic>> diagnosticarNotificacoes(String deviceId) async {
    try {
      final cloudFunction = ParseCloudFunction('diagnosticarNotificacoes');
      final resultado = await Conexao.throttledExecuteCloudFunction(
        cloudFunction,
        parameters: {
          'deviceId': deviceId,
        },
      );
      return resultado.result as Map<String, dynamic>? ?? {};
    } catch (e) {
      debugPrint('Erro ao diagnosticar notificações: $e');
      return {'status': 'erro', 'mensagem': 'Erro ao diagnosticar: $e'};
    }
  }
}
