// lib/main.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart' as dotenv;
import 'package:fila_app/controllers/login_controller.dart';
import 'package:fila_app/controllers/notification_controller.dart';
import 'package:fila_app/controllers/user_data_controller.dart'; // Importar UserDataController
import 'package:fila_app/services/auth_service.dart';
import 'package:fila_app/services/session_manager.dart'; // Importar SessionManager
import 'package:fila_app/services/connectivity_service.dart';
import 'package:fila_app/services/sync_manager.dart';
import 'package:fila_app/services/push_notification_service.dart';
import 'package:fila_app/services/throttling_service.dart'; // Importar serviço de throttling
// Removed obsolete service imports - functionality consolidated into PushNotificationService
import 'package:fila_app/conexao.dart';
import 'package:fila_app/routes/app_routes.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:fila_app/utils/ui_utils.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'package:fila_app/modules/admin/admin_module.dart';
import 'package:fila_app/services/performance_monitor.dart';
import 'package:fila_app/services/background_processor.dart';
import 'package:fila_app/utils/widget_performance_optimizer.dart';
import 'package:fila_app/services/fila_recovery_service.dart';
import 'package:fila_app/services/performance_optimizer.dart';
import 'package:fila_app/services/performance_fix_service.dart';
import 'package:flutter_svg/flutter_svg.dart';

// Handler para mensagens em background
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  print('Mensagem recebida em background: ${message.notification?.title}');
}

// ========================================
// 🚀 INICIALIZAÇÃO COMPLETA DOS SERVIÇOS
// ========================================

Future<void> initAllServices() async {
  try {
    print('🔧 Iniciando carregamento completo de serviços...');

    // ═══════════════════════════════════════════════════════════
    // ETAPA 0: CORREÇÕES DE PERFORMANCE (PRIMEIRA PRIORIDADE)
    // ═══════════════════════════════════════════════════════════

    print('⚡ Aplicando correções de performance críticas...');
    await PerformanceFixService.initialize();
    await PerformanceFixService.applyLogSpecificFixes();
    await PerformanceFixService.fixFrameTimeIssue();
    print('✅ Correções de performance aplicadas');

    // ═══════════════════════════════════════════════════════════
    // ETAPA 1: INFRAESTRUTURA BÁSICA (Obrigatórios para tudo)
    // ═══════════════════════════════════════════════════════════

    // 1.1 Carregar variáveis de ambiente
    try {
      await dotenv.dotenv.load(fileName: ".env");
      print('✅ Arquivo .env carregado');
    } catch (e) {
      print('⚠️ Erro ao carregar .env: $e');
    }

    // 1.2 Inicializar Parse Server (CRÍTICO - tudo depende disso)
    print('🔗 Inicializando Parse Server...');
    await Conexao.initialize();
    print('✅ Parse Server conectado');

    // 1.3 Throttling Service (para controlar requisições)
    print('⏱️ Inicializando ThrottlingService...');
    if (!Get.isRegistered<ThrottlingService>()) {
      Get.put(ThrottlingService(), permanent: true);
    }
    print('✅ ThrottlingService inicializado');

    // ═══════════════════════════════════════════════════════════
    // ETAPA 2: CONECTIVIDADE E SESSÃO (Base para autenticação)
    // ═══════════════════════════════════════════════════════════

    // 2.1 Connectivity Service
    print('🌐 Inicializando ConnectivityService...');
    if (!Get.isRegistered<ConnectivityService>()) {
      final connectivityService = ConnectivityService();
      Get.put(connectivityService, permanent: true);
    }
    print('✅ ConnectivityService inicializado');

    // 2.2 SessionManager
    print('🔐 Inicializando SessionManager...');
    if (!Get.isRegistered<SessionManager>()) {
      Get.put(SessionManager(), permanent: true);
    }
    print('✅ SessionManager inicializado');

    // ═══════════════════════════════════════════════════════════
    // ETAPA 3: IDENTIFICAÇÃO DO USUÁRIO (Dados e Dispositivo)
    // ═══════════════════════════════════════════════════════════

    // 3.1 UserDataController (AGORA que o Parse está pronto)
    print('👤 Inicializando UserDataController...');
    if (!Get.isRegistered<UserDataController>()) {
      Get.put(UserDataController(), permanent: true);
    }

    // 3.2 Verificar dados do usuário (com nova lógica inteligente)
    print('🔍 Verificando dados do usuário...');
    final userDataController = Get.find<UserDataController>();
    await userDataController.checkUserData();
    print('✅ UserDataController inicializado');

    // ═══════════════════════════════════════════════════════════
    // ETAPA 4: SERVIÇOS DE COMUNICAÇÃO (Push, Sync)
    // ═══════════════════════════════════════════════════════════

    // 4.1 Push Notification Service
    print('📱 Inicializando PushNotificationService...');
    if (!Get.isRegistered<PushNotificationService>()) {
      final pushService = PushNotificationService();
      Get.put(pushService, permanent: true);
      await pushService.init();
    }
    print('✅ PushNotificationService inicializado');

    // 4.2 Sync Manager
    print('🔄 Inicializando SyncManager...');
    if (!Get.isRegistered<SyncManager>()) {
      final syncManager = SyncManager();
      Get.put(syncManager, permanent: true);
    }
    print('✅ SyncManager inicializado');

    // ═══════════════════════════════════════════════════════════
    // ETAPA 5: SERVIÇOS DE APLICAÇÃO (Cache, Recuperação)
    // ═══════════════════════════════════════════════════════════

    // 5.1 All fila optimization functionality is now consolidated in PushNotificationService
    print(
        '✅ Fila optimization services consolidated in PushNotificationService');

    // 5.2 API caching functionality is integrated in PushNotificationService
    print('✅ API caching integrated in PushNotificationService');

    // 5.7 Fila Recovery Service
    print('🎯 Inicializando FilaRecoveryService...');
    if (!Get.isRegistered<FilaRecoveryService>()) {
      Get.put(FilaRecoveryService(), permanent: true);
    }
    print('✅ FilaRecoveryService inicializado');

    // ═══════════════════════════════════════════════════════════
    // ETAPA 6: OTIMIZAÇÃO E PERFORMANCE (Por último)
    // ═══════════════════════════════════════════════════════════

    // 6.1 Performance Monitor (DESABILITADO para evitar spam de logs)
    print('⚡ Inicializando serviços de otimização...');
    // ✅ DESABILITADO: PerformanceMonitor estava causando spam de logs
    // if (!Get.isRegistered<PerformanceMonitor>()) {
    //   Get.put(PerformanceMonitor());
    // }

    // 6.2 Preloading functionality integrated in PushNotificationService

    // 6.3 Background Processor
    if (!Get.isRegistered<BackgroundProcessor>()) {
      Get.put(BackgroundProcessor());
    }

    // 6.4 Widget Cache (limpeza)
    WidgetPerformanceOptimizer.clearWidgetCache();

    // 6.5 Performance Optimizer (para resolver problemas do log)
    await PerformanceOptimizer.instance.initialize();
    await PerformanceOptimizer.instance.applyLogBasedFixes();

    print('✅ Serviços de otimização inicializados');

    // ═══════════════════════════════════════════════════════════
    // ETAPA 7: FCM TOKEN (Verificação final)
    // ═══════════════════════════════════════════════════════════

    try {
      final token = await FirebaseMessaging.instance.getToken();
      if (token != null) {
        print('✅ FCM Token obtido');
      }
    } catch (e) {
      print('⚠️ Erro ao obter FCM token: $e');
    }

    print('🎉 Todos os serviços carregados com sucesso!');
  } catch (e, stackTrace) {
    print('❌ Erro ao inicializar serviços: $e');
    print('Stack trace: $stackTrace');
    rethrow;
  }
}

void main() async {
  await _initializeApp();
}

Future<void> _initializeApp() async {
  try {
    // ⚡ INICIALIZAÇÃO ULTRA-RÁPIDA - APENAS O ESSENCIAL
    WidgetsFlutterBinding.ensureInitialized();

    // Configurações básicas (rápidas)
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    UiUtils.setTranslucentStatusBar();
    UiUtils.setEdgeToEdgeDisplay();

    // Firebase mínimo (com await para garantir configuração correta)
    print('🔥 Inicializando Firebase...');
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase inicializado!');

    // Configurar handler de background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    print('✅ Background message handler configurado!');

    // Solicitar permissões de notificação imediatamente
    try {
      await FirebaseMessaging.instance.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );
      print('✅ Permissões de notificação solicitadas!');
    } catch (e) {
      print('⚠️ Erro ao solicitar permissões: $e');
    }

    // Controllers básicos APENAS para não dar erro (serão reinicializados depois)
    if (!Get.isRegistered<LoginController>()) {
      Get.put(LoginController());
    }
    if (!Get.isRegistered<NotificationController>()) {
      Get.put(NotificationController());
    }
    // NÃO inicializar UserDataController aqui - será feito no initAllServices

    // 🚀 MOSTRAR APP IMEDIATAMENTE
    print('🚀 Mostrando interface rapidamente...');
    runApp(const MyApp());
  } catch (e) {
    debugPrint('ERRO NA INICIALIZAÇÃO: $e');
    runApp(const MyApp());
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String initialRoute = '/';
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadServicesAndCheckUser();
  }

  Future<void> _loadServicesAndCheckUser() async {
    try {
      print('🔄 Carregando serviços na tela de loading...');

      // 🎯 CARREGAR TODOS OS SERVIÇOS DURANTE A TELA DE LOADING
      await initAllServices();

      // Pequena pausa para garantir que tudo foi inicializado
      await Future.delayed(Duration(milliseconds: 500));

      // 🔍 PRIORIDADE 1: VERIFICAR FILA ATIVA PRIMEIRO (PACIENTES)
      print('🔍 Verificando fila ativa primeiro...');

      try {
        final filaRecoveryService = Get.find<FilaRecoveryService>();
        print('🔧 [DEBUG] FilaRecoveryService encontrado');

        final queueResult =
            await filaRecoveryService.checkAndRecoverActiveQueue();

        print('🔧 [DEBUG] Resultado da verificação de fila: $queueResult');

        if (queueResult['hasActiveQueue'] == true) {
          print('✅ Fila ativa encontrada! Paciente tem prioridade.');

          // Usar a rota correta baseada no status
          final targetRoute = queueResult['targetRoute'] ?? '/fila_paciente';
          final queueData = queueResult['queueData'];
          final filaArguments =
              queueResult['filaArguments']; // ✅ NOVOS ARGUMENTOS

          print('📍 Redirecionando para: $targetRoute');
          print(
              '📊 Dados da fila: ${queueData?['filaId']} - Status: ${queueData?['status']}');
          print('📦 Argumentos da fila: $filaArguments');

          // ✅ NAVEGAR COM ARGUMENTOS ao invés de apenas definir initialRoute
          WidgetsBinding.instance.addPostFrameCallback((_) {
            print(
                '🚀 [NAVIGATION] Navegando para: $targetRoute com argumentos');
            Get.offAllNamed(targetRoute, arguments: filaArguments);
          });

          setState(() {
            initialRoute = targetRoute;
            isLoading = false;
          });
          return;
        } else {
          print('❌ Nenhuma fila ativa encontrada');
        }
      } catch (e) {
        print('⚠️ Erro ao verificar fila: $e');
        print('🔧 [DEBUG] Stack trace: ${StackTrace.current}');
      }

      // 🔍 PRIORIDADE 2: VERIFICAR USUÁRIO LOGADO (PROFISSIONAIS)
      print('🔍 Verificando usuário profissional...');

      final result = await AuthService.checkCurrentUser();
      print('🔧 [DEBUG] Resultado AuthService: $result');

      if (result['success'] && result.containsKey('route')) {
        print(
            '✅ Usuário profissional autenticado, redirecionando para: ${result['route']}');
        setState(() {
          initialRoute = result['route'];
          isLoading = false;
        });
        return;
      }

      // 3. Ir para splash
      print('➡️ Redirecionando para splash');
      setState(() {
        initialRoute = '/';
        isLoading = false;
      });
    } catch (e) {
      debugPrint('❌ Erro durante carregamento: $e');
      print('🔧 [DEBUG] Stack trace detalhado: ${StackTrace.current}');
      setState(() {
        initialRoute = '/';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return MaterialApp(
        debugShowCheckedModeBanner: false,
        home: Scaffold(
          body: Container(
            width: double.infinity,
            height: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0x90dcf8ec), Color(0xFF75CBBB)],
                stops: [0.01, 1.0],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: SafeArea(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo do app - usando logo2.svg para tela de carregamento
                    SvgPicture.asset(
                      'assets/logo2.svg',
                      width: MediaQuery.of(context).size.width * 0.7,
                      height: MediaQuery.of(context).size.height * 0.25,
                    ),

                    const SizedBox(height: 30),

                    // Indicador de loading elegante
                    SizedBox(
                      width: 32,
                      height: 32,
                      child: CircularProgressIndicator(
                        strokeWidth: 3,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    }

    return GetMaterialApp(
      title: 'Fila App',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      localizationsDelegates: GlobalMaterialLocalizations.delegates,
      supportedLocales: const [Locale('pt', 'BR')],
      initialRoute: initialRoute,
      getPages: AppRoutes.getPages(initialRoute: initialRoute),
    );
  }
}
