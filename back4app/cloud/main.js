/**
 * Cloud Code principal do Fila App
 *
 * Este arquivo contém todas as funções de Cloud Code necessárias para o aplicativo
 * Versão consolidada para plano gratuito do Back4App (arquivo único)
 */

// Nota: Removemos a configuração do job de limpeza de tokens expirados
// pois não é mais necessário com o método nativo do Parse

// ========================================
// 🧪 FUNÇÃO DE TESTE PARA NOTIFICAÇÕES
// ========================================

Parse.Cloud.define("sendTestNotification", async (request) => {
  console.log("🧪 [TEST] Função de teste chamada com parâmetros:", request.params);

  try {
    const { pacienteId, titulo, mensagem, dados } = request.params;

    if (!pacienteId) {
      throw new Error("pacienteId é obrigatório");
    }

    // Buscar o paciente
    const pacienteQuery = new Parse.Query("Paciente");
    pacienteQuery.equalTo("deviceId", pacienteId);

    const paciente = await pacienteQuery.first({ useMasterKey: true });

    if (!paciente) {
      // Tentar buscar por userId
      const pacienteQueryUserId = new Parse.Query("Paciente");
      pacienteQueryUserId.equalTo("userId", pacienteId);

      const pacienteByUserId = await pacienteQueryUserId.first({ useMasterKey: true });

      if (!pacienteByUserId) {
        throw new Error(`Paciente não encontrado: ${pacienteId}`);
      }

      paciente = pacienteByUserId;
    }

    const pushToken = paciente.get("pushToken");
    console.log("📱 [TEST] Token do paciente:", pushToken ? pushToken.substring(0, 20) + "..." : "Não encontrado");

    if (!pushToken) {
      return {
        success: false,
        error: "Paciente não tem token push registrado",
        patientData: {
          objectId: paciente.id,
          nome: paciente.get("nome"),
          deviceId: paciente.get("deviceId"),
          userId: paciente.get("userId")
        }
      };
    }

    // Criar notificação
    const Notification = Parse.Object.extend("Notification");
    const notification = new Notification();

    notification.set("title", titulo || "🧪 Teste de Notificação");
    notification.set("body", mensagem || "Esta é uma notificação de teste");
    notification.set("data", dados || { tipo: "teste" });
    notification.set("userId", pacienteId);
    notification.set("pushToken", pushToken);
    notification.set("read", false);
    notification.set("sent", false);

    const acl = new Parse.ACL();
    acl.setPublicReadAccess(true);
    acl.setPublicWriteAccess(true);
    notification.setACL(acl);

    await notification.save(null, { useMasterKey: true });

    console.log("✅ [TEST] Notificação criada no banco");

    return {
      success: true,
      message: "Notificação de teste criada",
      notificationId: notification.id,
      patientData: {
        objectId: paciente.id,
        nome: paciente.get("nome"),
        pushToken: pushToken ? "Existe" : "Não encontrado"
      }
    };

  } catch (error) {
    console.error("❌ [TEST] Erro na função de teste:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

// ========================================
// 📱 FUNÇÃO PARA NOTIFICAÇÃO DE ACEITAÇÃO NA FILA
// ========================================

Parse.Cloud.define("enviarNotificacaoAceitacao", async (request) => {
  console.log("📱 [ACEITAÇÃO] Função de notificação de aceitação chamada");
  console.log("📱 [ACEITAÇÃO] Parâmetros:", request.params);

  try {
    const {
      pacienteId,
      nomePaciente,
      nomeMedico,
      especialidade,
      posicao,
      filaId,
      hospitalNome
    } = request.params;

    if (!pacienteId) {
      throw new Error("pacienteId é obrigatório");
    }

    // Buscar o paciente pelo deviceId ou objectId
    let paciente;

    try {
      const pacienteQuery = new Parse.Query("Paciente");

      // Tentar primeiro pelo deviceId
      if (pacienteId.startsWith('P-')) {
        pacienteQuery.equalTo("deviceId", pacienteId);
      } else {
        // Se não, tentar pelo objectId
        pacienteQuery.equalTo("objectId", pacienteId);
      }

      paciente = await pacienteQuery.first({ useMasterKey: true });

      if (!paciente) {
        console.log(`⚠️ [ACEITAÇÃO] Paciente não encontrado: ${pacienteId}`);
        return { success: false, error: "Paciente não encontrado" };
      }
    } catch (e) {
      console.log(`❌ [ACEITAÇÃO] Erro ao buscar paciente: ${e.message}`);
      return { success: false, error: "Erro ao buscar paciente" };
    }

    // Obter token de notificação
    const pushToken = paciente.get("pushToken");

    if (!pushToken) {
      console.log(`⚠️ [ACEITAÇÃO] Token de push não encontrado para o paciente: ${pacienteId}`);
      return { success: false, error: "Token de push não encontrado" };
    }

    // Montar mensagem personalizada
    const posicaoTexto = posicao === 1 ? "1ª posição" :
      posicao === 2 ? "2ª posição" :
        posicao === 3 ? "3ª posição" :
          `${posicao}ª posição`;

    const titulo = "✅ Solicitação Aceita!";
    const mensagem = `Sua consulta com Dr. ${nomeMedico} foi aprovada! Você está na ${posicaoTexto} da fila.`;

    const especialidadeTexto = especialidade ? ` - ${especialidade}` : '';

    try {
      // Criar notificação no banco
      const Notification = Parse.Object.extend("Notification");
      const notification = new Notification();

      notification.set("title", titulo);
      notification.set("body", mensagem);
      notification.set("data", {
        tipo: 'aceito_fila',
        filaId: filaId || '',
        nomeMedico: nomeMedico || '',
        especialidade: especialidade || '',
        posicao: posicao.toString(),
        hospitalNome: hospitalNome || '',
        timestamp: new Date().toISOString(),
        action: 'open_fila_screen'
      });
      notification.set("userId", pacienteId);
      notification.set("pushToken", pushToken);
      notification.set("read", false);
      notification.set("sent", true);

      const acl = new Parse.ACL();
      acl.setPublicReadAccess(true);
      acl.setPublicWriteAccess(true);
      notification.setACL(acl);

      const notificationResult = await notification.save(null, { useMasterKey: true });
      console.log(`✅ [ACEITAÇÃO] Notificação registrada no banco: ${notificationResult.id}`);

      return {
        success: true,
        message: "Notificação de aceitação enviada com sucesso",
        notificationId: notificationResult.id,
        pacienteNome: nomePaciente,
        posicao: posicao
      };

    } catch (e) {
      console.log(`❌ [ACEITAÇÃO] Erro ao criar notificação: ${e.message}`);
      return { success: false, error: `Erro ao criar notificação: ${e.message}` };
    }

  } catch (e) {
    console.log(`❌ [ACEITAÇÃO] Erro geral: ${e.message}`);
    return { success: false, error: e.message };
  }
});

// ========================================
// 📱 FUNÇÃO PARA NOTIFICAÇÃO DE ACEITAÇÃO NA FILA
// ========================================

Parse.Cloud.define("enviarNotificacaoAceitacao", async (request) => {
  console.log("📱 [ACEITAÇÃO] Função de notificação de aceitação chamada");
  console.log("📱 [ACEITAÇÃO] Parâmetros:", request.params);

  try {
    const {
      pacienteId,
      nomePaciente,
      nomeMedico,
      especialidade,
      posicao,
      filaId,
      hospitalNome
    } = request.params;

    if (!pacienteId) {
      throw new Error("pacienteId é obrigatório");
    }

    // Buscar o paciente pelo deviceId ou objectId
    let paciente;

    try {
      const pacienteQuery = new Parse.Query("Paciente");

      // Tentar primeiro pelo deviceId
      if (pacienteId.startsWith('P-')) {
        pacienteQuery.equalTo("deviceId", pacienteId);
      } else {
        // Se não, tentar pelo objectId
        pacienteQuery.equalTo("objectId", pacienteId);
      }

      paciente = await pacienteQuery.first({ useMasterKey: true });

      if (!paciente) {
        console.log(`⚠️ [ACEITAÇÃO] Paciente não encontrado: ${pacienteId}`);
        return { success: false, error: "Paciente não encontrado" };
      }
    } catch (e) {
      console.log(`❌ [ACEITAÇÃO] Erro ao buscar paciente: ${e.message}`);
      return { success: false, error: "Erro ao buscar paciente" };
    }

    // Obter token de notificação
    const pushToken = paciente.get("pushToken");

    if (!pushToken) {
      console.log(`⚠️ [ACEITAÇÃO] Token de push não encontrado para o paciente: ${pacienteId}`);
      return { success: false, error: "Token de push não encontrado" };
    }

    // Montar mensagem personalizada
    const posicaoTexto = posicao === 1 ? "1ª posição" :
      posicao === 2 ? "2ª posição" :
        posicao === 3 ? "3ª posição" :
          `${posicao}ª posição`;

    const titulo = "✅ Solicitação Aceita!";
    const mensagem = `Sua consulta com Dr. ${nomeMedico} foi aprovada! Você está na ${posicaoTexto} da fila.`;

    const especialidadeTexto = especialidade ? ` - ${especialidade}` : '';

    try {
      // Criar notificação no banco
      const Notification = Parse.Object.extend("Notification");
      const notification = new Notification();

      notification.set("title", titulo);
      notification.set("body", mensagem);
      notification.set("data", {
        tipo: 'aceito_fila',
        filaId: filaId || '',
        nomeMedico: nomeMedico || '',
        especialidade: especialidade || '',
        posicao: posicao.toString(),
        hospitalNome: hospitalNome || '',
        timestamp: new Date().toISOString(),
        action: 'open_fila_screen'
      });
      notification.set("userId", pacienteId);
      notification.set("pushToken", pushToken);
      notification.set("read", false);
      notification.set("sent", true);

      const acl = new Parse.ACL();
      acl.setPublicReadAccess(true);
      acl.setPublicWriteAccess(true);
      notification.setACL(acl);

      const notificationResult = await notification.save(null, { useMasterKey: true });
      console.log(`✅ [ACEITAÇÃO] Notificação registrada no banco: ${notificationResult.id}`);

      return {
        success: true,
        message: "Notificação de aceitação enviada com sucesso",
        notificationId: notificationResult.id,
        pacienteNome: nomePaciente,
        posicao: posicao
      };

    } catch (e) {
      console.log(`❌ [ACEITAÇÃO] Erro ao criar notificação: ${e.message}`);
      return { success: false, error: `Erro ao criar notificação: ${e.message}` };
    }

  } catch (e) {
    console.log(`❌ [ACEITAÇÃO] Erro geral: ${e.message}`);
    return { success: false, error: e.message };
  }
});

// ========================================
// 🔄 FUNÇÃO PARA NOTIFICAR MUDANÇAS DE POSIÇÃO QUANDO ALGUÉM INICIA ATENDIMENTO
// ========================================

Parse.Cloud.define("notificarMudancasPosicaoFila", async (request) => {
  console.log("🔄 [MUDANÇA POSIÇÃO] Função chamada");
  console.log("🔄 [MUDANÇA POSIÇÃO] Parâmetros:", request.params);

  try {
    const { medicoId, consultorioId, pacienteQueIniciouId } = request.params;

    if (!medicoId || !consultorioId) {
      throw new Error("medicoId e consultorioId são obrigatórios");
    }

    // Buscar informações do médico
    const medicoQuery = new Parse.Query("Medico");
    const medico = await medicoQuery.get(medicoId, { useMasterKey: true });
    const nomeMedico = medico.get("nome") || "Médico";
    const especialidade = medico.get("especialidade") || "";

    // Buscar todos os pacientes aguardando na fila do mesmo médico
    const filaQuery = new Parse.Query("Fila");
    filaQuery.equalTo("medico", medico);
    filaQuery.equalTo("status", "aguardando");
    filaQuery.ascending("posicao");

    const filaResults = await filaQuery.find({ useMasterKey: true });

    console.log(`🔄 [MUDANÇA POSIÇÃO] Encontrados ${filaResults.length} pacientes aguardando`);

    if (filaResults.length === 0) {
      return { success: true, message: "Nenhum paciente aguardando na fila" };
    }

    // Função para obter dados do paciente
    const obterDadosPaciente = async (filaItem) => {
      try {
        // Primeiro tentar pelo campo paciente (relação)
        const pacienteRelacao = filaItem.get("paciente");
        if (pacienteRelacao) {
          const pacienteQuery = new Parse.Query("Paciente");
          pacienteQuery.equalTo("user", pacienteRelacao);
          const paciente = await pacienteQuery.first({ useMasterKey: true });

          if (paciente) {
            return {
              deviceId: paciente.get("deviceId"),
              nome: paciente.get("nome"),
              pushToken: paciente.get("pushToken")
            };
          }
        }

        // Se não encontrou, tentar pelos campos diretos
        return {
          deviceId: filaItem.get("deviceId") || filaItem.get("idPaciente"),
          nome: filaItem.get("nome_paciente") || filaItem.get("nome"),
          pushToken: null // Precisará buscar em outra query
        };
      } catch (e) {
        console.log(`⚠️ [MUDANÇA POSIÇÃO] Erro ao obter dados do paciente: ${e.message}`);
        return null;
      }
    };

    // Processar notificações para cada paciente
    const notificacoesEnviadas = [];

    for (let i = 0; i < filaResults.length; i++) {
      const filaItem = filaResults[i];
      const posicaoAtual = filaItem.get("posicao");
      const dadosPaciente = await obterDadosPaciente(filaItem);

      if (!dadosPaciente || !dadosPaciente.deviceId) {
        console.log(`⚠️ [MUDANÇA POSIÇÃO] Dados do paciente não encontrados para posição ${posicaoAtual}`);
        continue;
      }

      // Buscar token se não encontrou
      let pushToken = dadosPaciente.pushToken;
      if (!pushToken && dadosPaciente.deviceId) {
        try {
          const pacienteQuery = new Parse.Query("Paciente");
          pacienteQuery.equalTo("deviceId", dadosPaciente.deviceId);
          const paciente = await pacienteQuery.first({ useMasterKey: true });

          if (paciente) {
            pushToken = paciente.get("pushToken");
          }
        } catch (e) {
          console.log(`⚠️ [MUDANÇA POSIÇÃO] Erro ao buscar token: ${e.message}`);
        }
      }

      if (!pushToken) {
        console.log(`⚠️ [MUDANÇA POSIÇÃO] Token não encontrado para ${dadosPaciente.deviceId}`);
        continue;
      }

      // Definir mensagem baseada na posição
      let titulo, mensagem;

      if (posicaoAtual === 1) {
        titulo = "🎯 Agora é sua vez!";
        mensagem = `O atendimento anterior foi iniciado e agora você é o próximo! Dr. ${nomeMedico} te chamará em breve.`;
      } else if (posicaoAtual === 2) {
        titulo = "🚀 Você é o próximo!";
        mensagem = `Você subiu para a 2ª posição na fila do Dr. ${nomeMedico}. Prepare-se para ser atendido!`;
      } else if (posicaoAtual === 3) {
        titulo = "⏰ Entre os próximos!";
        mensagem = `Você agora está na 3ª posição na fila do Dr. ${nomeMedico}. Fique atento!`;
      } else {
        titulo = "📍 Posição atualizada";
        mensagem = `Você está na ${posicaoAtual}ª posição na fila do Dr. ${nomeMedico}. A fila está andando!`;
      }

      try {
        // Criar notificação no banco
        const Notification = Parse.Object.extend("Notification");
        const notification = new Notification();

        notification.set("title", titulo);
        notification.set("body", mensagem);
        notification.set("data", {
          tipo: 'mudanca_posicao',
          filaId: filaItem.id,
          nomeMedico: nomeMedico,
          especialidade: especialidade,
          posicao: posicaoAtual.toString(),
          timestamp: new Date().toISOString(),
          action: 'open_fila_screen'
        });
        notification.set("userId", dadosPaciente.deviceId);
        notification.set("pushToken", pushToken);
        notification.set("read", false);
        notification.set("sent", true);

        const acl = new Parse.ACL();
        acl.setPublicReadAccess(true);
        acl.setPublicWriteAccess(true);
        notification.setACL(acl);

        const notificationResult = await notification.save(null, { useMasterKey: true });

        notificacoesEnviadas.push({
          paciente: dadosPaciente.nome,
          posicao: posicaoAtual,
          notificationId: notificationResult.id
        });

        console.log(`✅ [MUDANÇA POSIÇÃO] Notificação enviada para ${dadosPaciente.nome} (posição ${posicaoAtual})`);

      } catch (e) {
        console.log(`❌ [MUDANÇA POSIÇÃO] Erro ao enviar notificação para posição ${posicaoAtual}: ${e.message}`);
      }
    }

    return {
      success: true,
      message: `${notificacoesEnviadas.length} notificações de mudança de posição enviadas`,
      notificacoesEnviadas: notificacoesEnviadas
    };

  } catch (e) {
    console.log(`❌ [MUDANÇA POSIÇÃO] Erro geral: ${e.message}`);
    return { success: false, error: e.message };
  }
});

// Configuração para LiveQuery e classes que devem ser observadas
// Nota: No Back4App, o LiveQuery é configurado no painel de controle, não via código
// Esta seção foi comentada para evitar erros
/*
// Este código não funciona no Back4App
Parse.Server.startLiveQueryServer({
  classNames: [
    'Fila',
    'MensagemFila',
    'Notificacao',
    'FilaSolicitacao',
    'Medico',
    'Secretaria',
    'consultorio',
    'MetricasAtendimento',
    'HospitalMedicoUpdate'
  ]
});
*/

// Em vez disso, vamos registrar as classes que queremos monitorar com LiveQuery
console.log('Registrando classes para LiveQuery:');
const liveQueryClasses = [
  'Fila',
  'MensagemFila',
  'Notificacao',
  'FilaSolicitacao',
  'Medico',
  'Secretaria',
  'consultorio',
  'MetricasAtendimento',
  'HospitalMedicoUpdate',  // Adicionado para suportar atualizações de vinculação médico-hospital
  'FilaUpdate',            // Adicionado para suportar atualizações de fila em tempo real
  'MensagemUpdate'         // Adicionado para suportar atualizações de mensagens em tempo real
];
console.log(`Classes registradas: ${liveQueryClasses.join(', ')}`);

// Você precisa habilitar o LiveQuery para estas classes no painel do Back4App
// Veja: https://www.back4app.com/docs/parse-server/live-query

// Mapeamento de áreas profissionais para tipos de usuário
const areaToUserTypeMap = {
  // Áreas médicas - todas mapeiam para 'medico'
  'medicina': 'medico',
  'odontologia': 'medico',
  'fisioterapia': 'medico',
  'psicologia': 'medico',
  'nutrição': 'medico',
  'biomedicina': 'medico',
  'enfermagem': 'medico',
  'fonoaudiologia': 'medico',
  'técnico em radiologia': 'medico',

  // Outros tipos de usuário
  'consultorio': 'consultorio',
  'secretaria': 'secretaria',
  'admin': 'admin',
  'paciente': 'paciente',
};

// Função para mapear área profissional para tipo de usuário
function mapAreaToUserType(area) {
  if (!area) return 'medico'; // Padrão para valores nulos ou vazios

  // Normalizar a área (converter para minúsculas)
  const normalizedArea = area.trim().toLowerCase();

  // Verificar se a área existe no mapeamento
  if (areaToUserTypeMap.hasOwnProperty(normalizedArea)) {
    return areaToUserTypeMap[normalizedArea];
  }

  // Se não encontrar, assume que é um médico (comportamento padrão)
  console.log(`AVISO: Área profissional não mapeada: ${area}. Usando "medico" como padrão.`);
  return 'medico';
}

// Trigger para garantir que o tipo de usuário esteja correto e o nome seja definido
Parse.Cloud.afterSave(Parse.User, async (request) => {
  // Ignorar se não for um novo usuário ou uma atualização
  if (!request.object) return;

  try {
    const user = request.object;
    const tipo = user.get('tipo');
    const areaProfissional = user.get('areaProfissional');
    const username = user.get('username');
    const nome = user.get('nome');
    const email = user.get('email');

    let needsUpdate = false;

    // 1. Corrigir o tipo de usuário se necessário
    if (tipo || areaProfissional) {
      // Verificar se o tipo precisa ser corrigido
      if (areaProfissional) {
        const correctType = mapAreaToUserType(areaProfissional);

        // Se o tipo atual for diferente do tipo correto, corrigir
        if (tipo !== correctType) {
          console.log(`Corrigindo tipo de usuário: ${tipo} -> ${correctType} (baseado na área profissional: ${areaProfissional})`);
          user.set('tipo', correctType);
          needsUpdate = true;
        }
      }
      // Se não tiver área profissional, mas o tipo for uma área médica
      else if (tipo && tipo.toLowerCase() !== 'medico' && tipo.toLowerCase() !== 'consultorio' &&
        tipo.toLowerCase() !== 'secretaria' && tipo.toLowerCase() !== 'admin' &&
        tipo.toLowerCase() !== 'paciente') {

        // Verificar se o tipo é uma área profissional
        const correctType = mapAreaToUserType(tipo);

        // Se for uma área profissional, corrigir o tipo e salvar a área
        if (correctType === 'medico' && tipo.toLowerCase() !== 'medico') {
          console.log(`Corrigindo tipo de usuário: ${tipo} -> ${correctType} (salvando área profissional: ${tipo})`);
          user.set('tipo', correctType);
          user.set('areaProfissional', tipo);
          needsUpdate = true;
        }
      }
    }

    // 2. Garantir que o campo nome esteja definido
    if (!nome) {
      // Se não tiver nome, usar o username ou email
      if (username && username !== email) {
        console.log(`Definindo nome do usuário ${user.id} como o username: ${username}`);
        user.set('nome', username);
        needsUpdate = true;
      } else if (email) {
        // Usar a parte do email antes do @
        const emailName = email.split('@')[0];
        console.log(`Definindo nome do usuário ${user.id} baseado no email: ${emailName}`);
        user.set('nome', emailName);
        needsUpdate = true;
      }
    }

    // Salvar as alterações se necessário
    if (needsUpdate) {
      await user.save(null, { useMasterKey: true });
    }
  } catch (error) {
    console.error('Erro ao processar afterSave do usuário:', error);
  }
});

// Configurar LiveQuery para garantir que está ativo
Parse.Cloud.afterSave(Parse.Config, async () => {
  try {
    // Verificar se o LiveQuery está configurado
    const config = await Parse.Config.get({ useMasterKey: true });
    const liveQueryEnabled = config.get('liveQueryEnabled');

    if (!liveQueryEnabled) {
      const newConfig = new Parse.Config();
      newConfig.set('liveQueryEnabled', true);
      newConfig.set('liveQueryClasses', [
        'Fila',
        'MensagemFila',
        'Notificacao',
        'FilaSolicitacao',
        'Medico',
        'Secretaria',
        'consultorio',
        'MetricasAtendimento',
        'HospitalMedicoUpdate',  // Adicionado para suportar atualizações de vinculação médico-hospital
        'FilaUpdate',            // Adicionado para suportar atualizações de fila em tempo real
        'MensagemUpdate'         // Adicionado para suportar atualizações de mensagens em tempo real
      ]);
      await newConfig.save(null, { useMasterKey: true });
      console.log('LiveQuery configurado com sucesso');
    }
  } catch (e) {
    console.error('Erro ao configurar LiveQuery:', e);
  }
});

// ==========================================
// ✅ FUNÇÕES DE TIMEZONE E DATA PARA BRASIL (GMT-3)
// ==========================================

/**
 * Obtém data/hora atual no timezone do Brasil (GMT-3)
 */
Parse.Cloud.define('getNowBrasil', async (request) => {
  try {
    const now = new Date();
    const brasilTime = new Date(now.getTime() - (3 * 60 * 60 * 1000)); // UTC-3

    return {
      success: true,
      brasilTime: brasilTime,
      iso8601: brasilTime.toISOString().replace('Z', '-03:00'),
      timestamp: brasilTime.getTime(),
      formatted: brasilTime.toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' })
    };
  } catch (error) {
    console.error('Erro ao obter data do Brasil:', error);
    return { success: false, error: error.message };
  }
});

/**
 * Converte timestamp para timezone do Brasil
 */
Parse.Cloud.define('timestampToBrasil', async (request) => {
  try {
    const { timestamp } = request.params;
    if (!timestamp) {
      throw new Error('Timestamp é obrigatório');
    }

    const date = new Date(timestamp);
    const brasilTime = new Date(date.getTime() - (3 * 60 * 60 * 1000)); // UTC-3

    return {
      success: true,
      originalTimestamp: timestamp,
      brasilTime: brasilTime,
      iso8601: brasilTime.toISOString().replace('Z', '-03:00'),
      formatted: brasilTime.toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' })
    };
  } catch (error) {
    console.error('Erro ao converter timestamp:', error);
    return { success: false, error: error.message };
  }
});

// ==========================================
// ✅ FUNÇÕES DE LIVEQUERY E STATUS
// ==========================================

/**
 * Verifica status do LiveQuery
 */
Parse.Cloud.define('checkLiveQueryStatus', async (request) => {
  try {
    const config = await Parse.Config.get({ useMasterKey: true });

    return {
      success: true,
      liveQueryEnabled: config.get('liveQueryEnabled') || false,
      registeredClasses: liveQueryClasses,
      serverTime: new Date(),
      brasilTime: new Date(Date.now() - (3 * 60 * 60 * 1000))
    };
  } catch (error) {
    console.error('Erro ao verificar status do LiveQuery:', error);
    return { success: false, error: error.message };
  }
});

/**
 * Força atualização de configuração do LiveQuery
 */
Parse.Cloud.define('updateLiveQueryConfig', async (request) => {
  try {
    const config = new Parse.Config();
    config.set('liveQueryEnabled', true);
    config.set('liveQueryClasses', liveQueryClasses);
    config.set('lastUpdated', new Date());

    await config.save(null, { useMasterKey: true });

    return {
      success: true,
      message: 'Configuração do LiveQuery atualizada',
      classes: liveQueryClasses
    };
  } catch (error) {
    console.error('Erro ao atualizar configuração do LiveQuery:', error);
    return { success: false, error: error.message };
  }
});

// ==========================================
// ✅ TRIGGERS PARA TIMEZONE AUTOMÁTICO
// ==========================================

// Trigger para garantir timezone correto em objetos Fila
Parse.Cloud.beforeSave('Fila', async (request) => {
  const fila = request.object;
  const brasilNow = new Date(Date.now() - (3 * 60 * 60 * 1000)); // UTC-3

  // Se é um novo objeto, definir createdAt no timezone do Brasil
  if (!fila.existed()) {
    fila.set('createdAtBrasil', brasilNow);
  }

  // Sempre atualizar updatedAt no timezone do Brasil
  fila.set('updatedAtBrasil', brasilNow);

  // Log para debug
  console.log(`[FILA] Timestamps Brasil atualizados para ${fila.id || 'novo objeto'}`);
});

// Trigger para garantir timezone correto em objetos MensagemFila
Parse.Cloud.beforeSave('MensagemFila', async (request) => {
  const mensagem = request.object;
  const brasilNow = new Date(Date.now() - (3 * 60 * 60 * 1000)); // UTC-3

  if (!mensagem.existed()) {
    mensagem.set('createdAtBrasil', brasilNow);
  }

  mensagem.set('updatedAtBrasil', brasilNow);

  console.log(`[MENSAGEM] Timestamps Brasil atualizados para ${mensagem.id || 'novo objeto'}`);
});

// Trigger para garantir timezone correto em objetos Notificacao
Parse.Cloud.beforeSave('Notificacao', async (request) => {
  const notificacao = request.object;
  const brasilNow = new Date(Date.now() - (3 * 60 * 60 * 1000)); // UTC-3

  if (!notificacao.existed()) {
    notificacao.set('createdAtBrasil', brasilNow);
  }

  notificacao.set('updatedAtBrasil', brasilNow);

  console.log(`[NOTIFICACAO] Timestamps Brasil atualizados para ${notificacao.id || 'novo objeto'}`);
});

// Configuração inicial - executada quando o servidor inicia ou o código é implantado
Parse.Cloud.afterSave(Parse.User, async () => {
  // Verificar se o serviço de e-mail está configurado
  try {
    const emailConfig = await Parse.Config.get('mailgunConfig');
    if (emailConfig) {
      Parse.Config.set('emailServiceConfigured', true);
    } else {
      Parse.Config.set('emailServiceConfigured', false);
      console.warn('Serviço de email não está configurado');
    }
  } catch (e) {
    console.error('Erro ao configurar serviço de email:', e);
    Parse.Config.set('emailServiceConfigured', false);
  }
});

// Função para adicionar role a um usuário
Parse.Cloud.define("addUserRole", async (request) => {
  try {
    const { userId, role } = request.params;

    // Verificar parâmetros
    if (!userId || !role) {
      throw new Error("Parâmetros userId e role são obrigatórios");
    }

    // Sanitizar o nome da role (remover caracteres não permitidos)
    // Apenas caracteres alfanuméricos, sublinhados, hífens e espaços são permitidos
    const sanitizedRole = role.replace(/[^a-zA-Z0-9_\- ]/g, '_');

    console.log(`Adicionando role ${sanitizedRole} (original: ${role}) ao usuário ${userId}`);

    // Buscar o usuário
    const userQuery = new Parse.Query(Parse.User);
    const user = await userQuery.get(userId, { useMasterKey: true });

    if (!user) {
      throw new Error("Usuário não encontrado");
    }

    // Verificar se o usuário já tem esta role
    const roles = user.get("roles") || [];
    if (roles.includes(sanitizedRole)) {
      console.log(`Usuário ${userId} já possui a role ${sanitizedRole}`);
      return {
        success: true,
        message: "Usuário já possui esta role"
      };
    }

    // Adicionar a role ao usuário
    roles.push(sanitizedRole);
    user.set("roles", roles);
    await user.save(null, { useMasterKey: true });

    // Garantir que a role existe no Parse Server
    const roleQuery = new Parse.Query(Parse.Role);
    roleQuery.equalTo("name", sanitizedRole);
    let roleObj = await roleQuery.first({ useMasterKey: true });

    if (!roleObj) {
      // Se a role não existir, criar
      roleObj = new Parse.Role(sanitizedRole, new Parse.ACL());
      await roleObj.save(null, { useMasterKey: true });
      console.log(`Role ${sanitizedRole} criada no sistema`);
    }

    // Adicionar o usuário à role
    const relation = roleObj.getUsers();
    relation.add(user);
    await roleObj.save(null, { useMasterKey: true });

    console.log(`Role ${sanitizedRole} adicionada ao usuário ${userId} com sucesso`);

    return {
      success: true,
      message: `Role ${sanitizedRole} adicionada com sucesso`
    };
  } catch (error) {
    console.error("Erro ao adicionar role:", error);
    throw new Error(`Erro ao adicionar role: ${error.message}`);
  }
});

// Função para adicionar paciente na fila através da secretária (com solicitação prévia)
Parse.Cloud.define("adicionarPacienteNaFila", async (request) => {
  try {
    const { solicitacaoId, medicoId, consultorioId, secretariaId } = request.params;

    // Validar parâmetros
    if (!solicitacaoId || !medicoId || !consultorioId) {
      throw new Error("Parâmetros obrigatórios não fornecidos");
    }

    console.log(`Processando solicitação: [solicitação: ${solicitacaoId}, médico: ${medicoId}, consultório: ${consultorioId}]`);

    // Verificar se a solicitação existe
    const solicitacaoQuery = new Parse.Query("FilaSolicitacao");
    const solicitacao = await solicitacaoQuery.get(solicitacaoId, { useMasterKey: true });
    if (!solicitacao) {
      throw new Error("Solicitação não encontrada");
    }

    // Verificar médico
    const medicoQuery = new Parse.Query("Medico");
    const medico = await medicoQuery.get(medicoId, { useMasterKey: true });
    if (!medico) {
      throw new Error("Médico não encontrado");
    }

    // Verificar consultório
    const consultorioQuery = new Parse.Query("consultorio");
    const consultorio = await consultorioQuery.get(consultorioId, { useMasterKey: true });
    if (!consultorio) {
      throw new Error("Consultório não encontrado");
    }

    // Buscar secretária se fornecida
    let secretaria = null;
    if (secretariaId) {
      const secretariaQuery = new Parse.Query("Secretaria");
      secretaria = await secretariaQuery.get(secretariaId, { useMasterKey: true });
    }

    // Obter dados do paciente
    const idPaciente = solicitacao.get("idPaciente");
    if (!idPaciente) {
      throw new Error("ID do paciente não encontrado na solicitação");
    }

    // Buscar paciente com o userId (migração completa para classe Paciente)
    const pacienteQuery = new Parse.Query("Paciente");
    pacienteQuery.equalTo("userId", idPaciente);
    let paciente = await pacienteQuery.first({ useMasterKey: true });

    if (!paciente) {
      // Criar novo paciente com os dados da solicitação
      paciente = new Parse.Object("Paciente");
      paciente.set("userId", idPaciente);
      paciente.set("nome", solicitacao.get("nomePaciente") || "Paciente");
      paciente.set("telefone", solicitacao.get("telefonePaciente") || "");
      paciente.set("dataCadastro", new Date());
      paciente.set("ultimoAcesso", new Date());
      paciente.set("em_fila", false);

      // Configurar ACL pública
      const aclPaciente = new Parse.ACL();
      aclPaciente.setPublicReadAccess(true);
      aclPaciente.setPublicWriteAccess(true);
      paciente.setACL(aclPaciente);

      await paciente.save(null, { useMasterKey: true });
    } else {
      // Atualizar dados existentes se necessário
      let needsUpdate = false;

      const nomeSolicitacao = solicitacao.get("nomePaciente");
      const telefoneSolicitacao = solicitacao.get("telefonePaciente");

      if (nomeSolicitacao && nomeSolicitacao !== paciente.get("nome")) {
        paciente.set("nome", nomeSolicitacao);
        needsUpdate = true;
      }

      if (telefoneSolicitacao && telefoneSolicitacao !== paciente.get("telefone")) {
        paciente.set("telefone", telefoneSolicitacao);
        needsUpdate = true;
      }

      paciente.set("ultimoAcesso", new Date());
      needsUpdate = true;

      if (needsUpdate) {
        await paciente.save(null, { useMasterKey: true });
      }
    }

    // Obter a última posição na fila para este médico
    const filaQuery = new Parse.Query("Fila");
    filaQuery.equalTo("medico", medico);
    filaQuery.equalTo("consultorio", consultorio);
    filaQuery.containedIn("status", ["aguardando", "em_atendimento"]);
    filaQuery.descending("posicao");

    const filaResult = await filaQuery.first({ useMasterKey: true });
    const proximaPosicao = filaResult ? (filaResult.get("posicao") || 0) + 1 : 1;

    // Criar nova entrada na fila
    const novaFila = new Parse.Object("Fila");
    novaFila.set("nome", paciente.get("nome") || "Paciente");
    novaFila.set("telefone", paciente.get("telefone") || "");
    novaFila.set("idPaciente", idPaciente);
    novaFila.set("status", "aguardando");
    novaFila.set("posicao", proximaPosicao);
    novaFila.set("data_entrada", new Date());
    novaFila.set("medico", medico);
    novaFila.set("consultorio", consultorio);
    novaFila.set("solicitacao", solicitacao);

    // Associar secretária se fornecida
    if (secretaria) {
      novaFila.set("secretaria_responsavel", secretaria);
    }

    // Definir ACL aberta para resolver problemas de permissão
    const acl = new Parse.ACL();
    acl.setPublicReadAccess(true);
    acl.setPublicWriteAccess(true);
    novaFila.setACL(acl);

    // Salvar a fila
    const filaSalva = await novaFila.save(null, { useMasterKey: true });

    // Atualizar status do paciente como em fila
    paciente.set("em_fila", true);
    paciente.set("ultima_fila", new Date());
    await paciente.save(null, { useMasterKey: true });

    // Atualizar status da solicitação
    solicitacao.set("status", "finalizada");
    await solicitacao.save(null, { useMasterKey: true });

    // Criar notificação
    const notificacao = new Parse.Object("Notificacao");
    notificacao.set("tipo", "entrada_fila");
    notificacao.set("solicitacao_id", solicitacao.get("solicitacao_id") || solicitacao.id);
    notificacao.set("fila_id", filaSalva.id);
    notificacao.set("medico_id", medicoId);
    notificacao.set("consultorio_id", consultorioId);
    notificacao.set("lida", false);
    notificacao.setACL(acl);
    await notificacao.save(null, { useMasterKey: true });

    // Enviar notificação para o paciente
    try {
      const notification = new Parse.Object("Notification");
      notification.set("title", "Adicionado à Fila");
      notification.set("body", `Você foi adicionado à fila do Dr. ${medico.get("nome")}`);
      notification.set("data", { type: "fila_aceita", filaId: filaSalva.id });
      notification.set("userId", idPaciente);
      notification.set("read", false);
      notification.set("sent", false);
      notification.setACL(acl);
      await notification.save(null, { useMasterKey: true });
    } catch (e) {
      console.warn("Erro ao criar notificação:", e);
      // Não interrompe o fluxo se falhar
    }

    return {
      success: true,
      filaId: filaSalva.id,
      posicao: proximaPosicao
    };
  } catch (error) {
    console.error("Erro ao adicionar paciente na fila:", error);
    throw new Error(error.message);
  }
});

// Função para adicionar paciente diretamente na fila (sem solicitação prévia)
Parse.Cloud.define("adicionarPacienteNaFilaDireto", async (request) => {
  try {
    const {
      pacienteId,
      userId,
      medicoId,
      consultorioId,
      secretariaId,
      nome,
      telefone
    } = request.params;

    // Validar parâmetros
    if ((!pacienteId && !userId) || !medicoId || !consultorioId) {
      throw new Error("Parâmetros obrigatórios não fornecidos");
    }

    console.log(`Adicionando paciente diretamente na fila: [paciente: ${pacienteId || userId}, médico: ${medicoId}, consultório: ${consultorioId}]`);

    // Verificar médico
    const medicoQuery = new Parse.Query("Medico");
    const medico = await medicoQuery.get(medicoId, { useMasterKey: true });
    if (!medico) {
      throw new Error("Médico não encontrado");
    }

    // Verificar consultório
    const consultorioQuery = new Parse.Query("consultorio");
    const consultorio = await consultorioQuery.get(consultorioId, { useMasterKey: true });
    if (!consultorio) {
      throw new Error("Consultório não encontrado");
    }

    // Buscar secretária se fornecida
    let secretaria = null;
    if (secretariaId) {
      const secretariaQuery = new Parse.Query("Secretaria");
      secretaria = await secretariaQuery.get(secretariaId, { useMasterKey: true });
    }

    // Buscar objeto Paciente
    let paciente = null;
    if (pacienteId) {
      const pacienteQuery = new Parse.Query("Paciente");
      paciente = await pacienteQuery.get(pacienteId, { useMasterKey: true });
    } else if (userId) {
      const pacienteQuery = new Parse.Query("Paciente");
      pacienteQuery.equalTo("userId", userId);
      paciente = await pacienteQuery.first({ useMasterKey: true });

      if (!paciente) {
        // Criar novo paciente se não existir
        paciente = new Parse.Object("Paciente");
        paciente.set("userId", userId);
        paciente.set("nome", nome || "Paciente");
        paciente.set("telefone", telefone || "");
        paciente.set("dataCadastro", new Date());
        paciente.set("ultimoAcesso", new Date());
        paciente.set("em_fila", false);

        // Configurar ACL pública
        const aclPaciente = new Parse.ACL();
        aclPaciente.setPublicReadAccess(true);
        aclPaciente.setPublicWriteAccess(true);
        paciente.setACL(aclPaciente);

        await paciente.save(null, { useMasterKey: true });
      }
    }

    if (!paciente) {
      throw new Error("Paciente não encontrado");
    }

    // Obter a última posição na fila para este médico
    const filaQuery = new Parse.Query("Fila");
    filaQuery.equalTo("medico", medico);
    filaQuery.equalTo("consultorio", consultorio);
    filaQuery.containedIn("status", ["aguardando", "em_atendimento"]);
    filaQuery.descending("posicao");

    const filaResult = await filaQuery.first({ useMasterKey: true });
    const proximaPosicao = filaResult ? (filaResult.get("posicao") || 0) + 1 : 1;

    // Criar nova entrada na fila
    const novaFila = new Parse.Object("Fila");
    novaFila.set("nome", paciente.get("nome") || "Paciente");
    novaFila.set("telefone", paciente.get("telefone") || "");
    novaFila.set("idPaciente", paciente.get("userId"));
    novaFila.set("status", "aguardando");
    novaFila.set("posicao", proximaPosicao);
    novaFila.set("data_entrada", new Date());
    novaFila.set("medico", medico);
    novaFila.set("consultorio", consultorio);
    novaFila.set("usuario", paciente);

    // Associar secretária se fornecida
    if (secretaria) {
      novaFila.set("secretaria_responsavel", secretaria);
    }

    // Definir ACL aberta para resolver problemas de permissão
    const acl = new Parse.ACL();
    acl.setPublicReadAccess(true);
    acl.setPublicWriteAccess(true);

    // Dar permissões específicas ao consultório e secretária
    if (consultorio.get("user_consultorio")) {
      acl.setReadAccess(consultorio.get("user_consultorio").id, true);
      acl.setWriteAccess(consultorio.get("user_consultorio").id, true);
    }

    if (secretaria && secretaria.get("user_secretaria")) {
      acl.setReadAccess(secretaria.get("user_secretaria").id, true);
      acl.setWriteAccess(secretaria.get("user_secretaria").id, true);
    }

    // Se tivermos um médico com usuário associado, dar permissões
    try {
      const medicoUserQuery = new Parse.Query(Parse.User);
      medicoUserQuery.equalTo("tipo", "medico");
      medicoUserQuery.equalTo("medicoId", medico.id);
      const medicoUser = await medicoUserQuery.first({ useMasterKey: true });

      if (medicoUser) {
        acl.setReadAccess(medicoUser.id, true);
        acl.setWriteAccess(medicoUser.id, true);
      }
    } catch (e) {
      console.warn("Erro ao buscar usuário do médico:", e);
    }

    novaFila.setACL(acl);

    // Salvar a fila
    const filaSalva = await novaFila.save(null, { useMasterKey: true });

    // Atualizar status do usuário como em fila
    paciente.set("em_fila", true);
    paciente.set("ultima_fila", new Date());
    await paciente.save(null, { useMasterKey: true });

    // Tentar atualizar no Paciente para compatibilidade
    if (pacienteId) {
      try {
        const pacienteQuery = new Parse.Query("Paciente");
        pacienteQuery.equalTo("userId", pacienteId);
        const paciente = await pacienteQuery.first({ useMasterKey: true });

        if (paciente) {
          paciente.set("em_fila", true);
          paciente.set("ultima_fila", new Date());
          await paciente.save(null, { useMasterKey: true });
        }
      } catch (e) {
        console.warn("Erro ao atualizar paciente:", e);
        // Não interrompe o fluxo se falhar
      }
    }

    // Enviar notificação para o paciente
    try {
      if (pacienteId) {
        const notification = new Parse.Object("Notification");
        notification.set("title", "Adicionado à Fila");
        notification.set("body", `Você foi adicionado à fila do Dr. ${medico.get("nome")}`);
        notification.set("data", { type: "fila_aceita", filaId: filaSalva.id });
        notification.set("userId", pacienteId);
        notification.set("read", false);
        notification.set("sent", false);
        notification.setACL(acl);
        await notification.save(null, { useMasterKey: true });
      }
    } catch (e) {
      console.warn("Erro ao criar notificação:", e);
      // Não interrompe o fluxo se falhar
    }

    return {
      success: true,
      filaId: filaSalva.id,
      posicao: proximaPosicao
    };
  } catch (error) {
    console.error("Erro ao adicionar paciente diretamente na fila:", error);
    throw new Error(error.message);
  }
});

// Função para configurações adicionais
Parse.Cloud.define('configureSiteSettings', async (request) => {
  // Verificar se o usuário é um admin
  if (!request.user || request.user.get('tipo') !== 'admin') {
    throw new Parse.Error(
      Parse.Error.OPERATION_FORBIDDEN,
      'Apenas administradores podem configurar as definições do site'
    );
  }

  try {
    // Atualizar as configurações do site
    const { siteName, siteUrl, emailSettings } = request.params;

    // Criar ou atualizar as configurações
    const configQuery = new Parse.Query('SiteConfiguration');
    let config = await configQuery.first({ useMasterKey: true });

    if (!config) {
      config = new Parse.Object('SiteConfiguration');
    }

    if (siteName) config.set('siteName', siteName);
    if (siteUrl) config.set('siteUrl', siteUrl);

    if (emailSettings) {
      config.set('emailFrom', emailSettings.from);

      // Salvar configurações de email de forma segura usando Parse Config
      await Parse.Config.save({
        'mailgunConfig': {
          'domain': emailSettings.domain,
          'apiKey': emailSettings.apiKey
        }
      }, { masterKey: Parse.masterKey });

      Parse.Config.set('emailServiceConfigured', true);
    }

    await config.save(null, { useMasterKey: true });

    return {
      success: true,
      message: 'Configurações atualizadas com sucesso'
    };
  } catch (error) {
    console.error('Erro ao configurar definições do site:', error);
    return {
      success: false,
      message: 'Erro ao atualizar configurações: ' + error.message
    };
  }
});

// Validação de instalação do Cloud Code
Parse.Cloud.define('testCloudCode', async () => {
  return {
    success: true,
    message: 'Cloud Code está funcionando corretamente',
    version: '1.0.0',
    modules: ['password_reset'],
    timestamp: new Date()
  };
});

// main.js - Arquivo principal de Cloud Functions do Parse Server

// =============================================================================
// FUNÇÕES DE AUTENTICAÇÃO E REGISTRO
// =============================================================================

// Função para registrar usuário e perfil específico
Parse.Cloud.define("registrarUsuario", async (request) => {
  try {
    const {
      username,
      email,
      senha,
      tipo,
      nome,
      crm,
      cpf,
      especializacao,
      cnpj,
      tipoHospital,
      telefone,
      latitude,
      longitude,
      ativo
    } = request.params;

    // Verificar parâmetros obrigatórios
    if (!email || !senha || !tipo) {
      throw new Error("Email, senha e tipo são obrigatórios");
    }

    // Se o nome não foi fornecido, usar o username ou email
    const nomeUsuario = nome || username || email.split('@')[0];

    console.log(`Iniciando registro de usuário tipo ${tipo}: ${username} (${email})`);

    // Verificar se já existe usuário
    const queryEmail = new Parse.Query(Parse.User);
    queryEmail.equalTo('email', email);
    const queryUsername = new Parse.Query(Parse.User);
    queryUsername.equalTo('username', username);

    const emailQuery = Parse.Query.or(queryEmail, queryUsername);
    const existingUser = await emailQuery.first({ useMasterKey: true });

    // =============================================================================
    // FUNÇÕES DE QR CODE
    // =============================================================================

    // Função para gerar QR Code para médico
    Parse.Cloud.define("gerarQRCodeMedico", async (request) => {
      try {
        const { medicoId, consultorioId } = request.params;

        if (!medicoId || !consultorioId) {
          throw new Error("IDs do médico e consultório são necessários");
        }

        console.log(`Gerando QR Code para médico ${medicoId} no consultório ${consultorioId}`);

        // Validar consultório e médico usando Pointer para evitar problemas de referência
        const consultorioPointer = {
          __type: 'Pointer',
          className: 'consultorio',
          objectId: consultorioId
        };

        const medicoPointer = {
          __type: 'Pointer',
          className: 'Medico',
          objectId: medicoId
        };

        // Verificar se consultório existe
        const consultorioQuery = new Parse.Query("consultorio");
        let consultorio;
        try {
          consultorio = await consultorioQuery.get(consultorioId, { useMasterKey: true });
          if (!consultorio) {
            throw new Error("Consultório não encontrado");
          }
        } catch (e) {
          console.error("Erro ao buscar consultório:", e);
          throw new Error(`Consultório não encontrado ou inacessível: ${e.message}`);
        }

        // Verificar se médico existe
        const medicoQuery = new Parse.Query("Medico");
        let medico;
        try {
          medico = await medicoQuery.get(medicoId, { useMasterKey: true });
          if (!medico) {
            throw new Error("Médico não encontrado");
          }
        } catch (e) {
          console.error("Erro ao buscar médico:", e);
          throw new Error(`Médico não encontrado ou inacessível: ${e.message}`);
        }

        console.log(`Médico e consultório validados com sucesso`);

        // Invalidar QR codes antigos
        try {
          const qrCodesAntigos = new Parse.Query("QRCodeGerado");
          qrCodesAntigos.equalTo("medico_id", medicoPointer);
          qrCodesAntigos.equalTo("consultorio_id", consultorioPointer);
          qrCodesAntigos.equalTo("valido", true);

          const qrCodesParaInvalidar = await qrCodesAntigos.find({ useMasterKey: true });
          console.log(`Encontrados ${qrCodesParaInvalidar.length} QR codes antigos para invalidar`);

          for (const qrCode of qrCodesParaInvalidar) {
            qrCode.set("valido", false);
            qrCode.set("data_invalidacao", new Date());
            await qrCode.save(null, { useMasterKey: true });
          }
        } catch (e) {
          console.warn("Erro ao invalidar QR codes antigos:", e);
          // Continuar mesmo com erro para não bloquear a geração de novo QR code
        }

        // Gerar ID único para o QR Code
        const qrId = uuidv4();

        // Contagem sequencial
        let sequencial = "0001"; // Valor padrão
        try {
          const qrCodeQuery = new Parse.Query("QRCodeGerado");
          qrCodeQuery.equalTo("medico_id", medicoPointer);
          qrCodeQuery.equalTo("consultorio_id", consultorioPointer);

          const qrCodesExistentes = await qrCodeQuery.count({ useMasterKey: true });
          sequencial = (qrCodesExistentes + 1).toString().padStart(4, '0');
        } catch (e) {
          console.warn("Erro ao calcular sequencial:", e);
          // Usar valor padrão definido acima
        }

        console.log(`Criando novo QR Code com ID: ${qrId} e sequencial: ${sequencial}`);

        // Criar o registro de QR Code com valores explícitos para garantir consistência
        const qrCode = new Parse.Object("QRCodeGerado");

        // Dados básicos
        qrCode.set("qr_id", qrId);
        qrCode.set("sequencial", sequencial);

        // Referências
        qrCode.set("medico_id", medico);
        qrCode.set("consultorio_id", consultorio);

        // Status
        qrCode.set("valido", true);
        qrCode.set("impresso", false);
        qrCode.set("status", "ativo");

        // Metadados
        qrCode.set("data_criacao", new Date());
        qrCode.set("data_expiracao", new Date(new Date().setDate(new Date().getDate() + 30))); // Expira em 30 dias
        qrCode.set("versao", "2.0");

        // ACL ampla para evitar problemas de permissão
        const acl = new Parse.ACL();
        acl.setPublicReadAccess(true);
        acl.setPublicWriteAccess(true);
        qrCode.setACL(acl);

        let savedQRCode;
        try {
          savedQRCode = await qrCode.save(null, { useMasterKey: true });
          console.log(`QR Code salvo com sucesso: objectId=${savedQRCode.id}`);
        } catch (e) {
          console.error("Erro ao salvar QR Code:", e);
          throw new Error(`Erro ao salvar QR Code no banco de dados: ${e.message}`);
        }

        // Dados do médico e consultório para retorno
        const medicoNome = medico.get("nome") || "Médico";
        const especialidade = medico.get("especialidade") || "Especialidade não informada";
        const consultorioNome = consultorio.get("nome") || "Consultório";

        // Verificar mais uma vez se o QR code foi realmente criado para garantir consistência
        const verificacao = new Parse.Query("QRCodeGerado");
        verificacao.equalTo("qr_id", qrId);
        const qrCodeVerificado = await verificacao.first({ useMasterKey: true });

        if (!qrCodeVerificado) {
          throw new Error("QR Code criado mas não encontrado na verificação");
        }

        // Registrar a criação do QR Code no log
        try {
          const qrLog = new Parse.Object("QRCodeLog");
          qrLog.set("action", "criacao");
          qrLog.set("qr_code", savedQRCode);
          qrLog.set("details", "QR Code gerado pela secretária/médico");
          qrLog.set("timestamp", new Date());
          qrLog.set("medico_id", medico);
          qrLog.set("consultorio_id", consultorio);
          await qrLog.save(null, { useMasterKey: true });
        } catch (e) {
          console.warn("Erro ao salvar log de QR code:", e);
          // Não interromper o fluxo se o log falhar
        }

        const resultado = {
          success: true,
          qrCode: {
            id: savedQRCode.id,
            qrId: qrId,
            sequencial: sequencial,
            dataExpiracao: qrCode.get("data_expiracao"),
            medicoNome: medicoNome,
            especialidade: especialidade,
            consultorioNome: consultorioNome
          }
        };

        console.log("Resultado da geração de QR Code:", JSON.stringify(resultado));

        return resultado;
      } catch (error) {
        console.error("Erro ao gerar QR Code:", error);
        throw new Error(`Erro ao gerar QR Code: ${error.message}`);
      }
    });

    // Função UUID para gerar identificadores únicos
    function uuidv4() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }

    // ✅ NOVO: Cloud Function para reordenar posições na fila automaticamente
    Parse.Cloud.define("reordenarPosicoesFilaAutomatico", async (request) => {
      const { medicoId, consultorioId, filaIdExcluir } = request.params;

      if (!medicoId || !consultorioId) {
        throw new Error("medicoId e consultorioId são obrigatórios");
      }

      console.log(`🔄 Iniciando reordenação automática da fila - Médico: ${medicoId}, Consultório: ${consultorioId}`);

      try {
        // 1. Buscar todas as filas ativas do médico/consultório (exceto a que deve ser excluída)
        const filaQuery = new Parse.Query("Fila");
        filaQuery.equalTo("medico", { __type: "Pointer", className: "Medico", objectId: medicoId });
        filaQuery.equalTo("consultorio", { __type: "Pointer", className: "consultorio", objectId: consultorioId });
        filaQuery.containedIn("status", ["aguardando", "em_atendimento"]);
        filaQuery.ascending("posicao");

        if (filaIdExcluir) {
          filaQuery.notEqualTo("objectId", filaIdExcluir);
        }

        const filas = await filaQuery.find({ useMasterKey: true });

        if (filas.length === 0) {
          console.log("ℹ️ Nenhuma fila encontrada para reordenar");
          return { success: true, message: "Nenhuma fila para reordenar", filasAtualizadas: 0 };
        }

        console.log(`📋 Encontradas ${filas.length} filas para reordenar`);

        // 2. Reordenar as posições
        let filasAtualizadas = 0;
        for (let i = 0; i < filas.length; i++) {
          const fila = filas[i];
          const novaPosicao = i + 1;
          const posicaoAnterior = fila.get("posicao");

          if (posicaoAnterior !== novaPosicao) {
            console.log(`📍 Atualizando posição da fila ${fila.id}: ${posicaoAnterior} -> ${novaPosicao}`);

            fila.set("posicao", novaPosicao);

            // Configurar ACL pública
            const acl = new Parse.ACL();
            acl.setPublicReadAccess(true);
            acl.setPublicWriteAccess(true);
            fila.setACL(acl);

            await fila.save(null, { useMasterKey: true });
            filasAtualizadas++;

            // ✅ IMPORTANTE: A notificação será enviada automaticamente pelo trigger afterSave
            console.log(`✅ Posição atualizada para fila ${fila.id}: ${posicaoAnterior} -> ${novaPosicao}`);
          }
        }

        console.log(`✅ Reordenação concluída - ${filasAtualizadas} filas atualizadas`);

        return {
          success: true,
          message: `Reordenação concluída com sucesso`,
          filasProcessadas: filas.length,
          filasAtualizadas: filasAtualizadas
        };

      } catch (error) {
        console.error("❌ Erro ao reordenar posições da fila:", error);
        throw new Error(`Erro ao reordenar posições: ${error.message}`);
      }
    });

    if (existingUser) {
      if (existingUser.get('email') === email) {
        throw new Error("Este e-mail já está cadastrado");
      } else {
        throw new Error("Este nome de usuário já está em uso");
      }
    }

    // Mapear a área profissional para o tipo de usuário correto
    let userType = tipo;
    const areaProfissional = request.params.areaProfissional;

    // Se tiver área profissional, usar o mapeamento
    if (areaProfissional) {
      userType = mapAreaToUserType(areaProfissional);
      console.log(`Área profissional '${areaProfissional}' mapeada para tipo de usuário '${userType}'`);
    } else if (tipo && tipo.toLowerCase() !== 'medico' && tipo.toLowerCase() !== 'consultorio' &&
      tipo.toLowerCase() !== 'secretaria' && tipo.toLowerCase() !== 'admin' &&
      tipo.toLowerCase() !== 'paciente') {
      // Se o tipo não for um dos tipos padrão, tentar mapear
      const mappedType = mapAreaToUserType(tipo);
      console.log(`Tipo '${tipo}' mapeado para '${mappedType}'`);
      userType = mappedType;
    }

    // Criar usuário base
    console.log(`Criando usuário base com tipo: ${userType}`);
    const user = new Parse.User();
    user.set('username', nomeUsuario); // Usar o nome como username
    user.set('password', senha);
    user.set('email', email);
    user.set('tipo', userType);
    user.set('dataCadastro', new Date());

    // Armazenar o nome como campo separado
    user.set('nome', nomeUsuario);

    // Se tiver área profissional, salvar como campo separado
    if (areaProfissional) {
      user.set('areaProfissional', areaProfissional);
    }

    // Salvar usuário
    console.log("Salvando usuário");
    await user.save(null, { useMasterKey: true });
    console.log(`Usuário criado com ID: ${user.id}`);

    // Criar perfil específico
    if (tipo === 'medico') {
      if (!crm || !cpf || !especializacao) {
        await user.destroy({ useMasterKey: true });
        throw new Error("Dados do médico incompletos");
      }

      console.log("Criando perfil de médico");
      const medico = new Parse.Object("Medico");
      medico.set('nome', nome || username);
      medico.set('crm', crm);
      medico.set('cpf', Number(cpf));
      medico.set('especialidade', especializacao);
      medico.set('user_medico', user);
      medico.set('ativo', ativo !== undefined ? ativo : true);
      medico.set('dataCadastro', new Date());

      // Configurar ACL (Access Control List)
      const acl = new Parse.ACL();
      acl.setPublicReadAccess(true);
      acl.setReadAccess(user.id, true);
      acl.setWriteAccess(user.id, true);
      medico.setACL(acl);

      await medico.save(null, { useMasterKey: true });
      console.log(`Perfil médico criado com ID: ${medico.id}`);

      // Criar relação bidirecional
      const relation = user.relation("user_medico");
      relation.add(medico);
      await user.save(null, { useMasterKey: true });
    } else if (tipo === 'consultorio') {
      if (!cnpj || !tipoHospital || !telefone || latitude === undefined || longitude === undefined) {
        await user.destroy({ useMasterKey: true });
        throw new Error("Dados do hospital incompletos");
      }

      console.log("Criando perfil de consultório");
      const consultorio = new Parse.Object("consultorio");
      consultorio.set('cnpj', cnpj);
      consultorio.set('tipo', tipoHospital);
      consultorio.set('telefone', telefone);
      consultorio.set('latitude', latitude);
      consultorio.set('longitude', longitude);
      consultorio.set('nome', nome || username);
      consultorio.set('ativo', ativo !== undefined ? ativo : true);
      consultorio.set('dataCadastro', new Date());
      consultorio.set('user_consultorio', user);
      consultorio.set('medicos_vinculados', []);

      await consultorio.save(null, { useMasterKey: true });
      console.log(`Perfil consultório criado com ID: ${consultorio.id}`);

    } else if (tipo === 'secretaria') {
      if (!cpf || !telefone) {
        await user.destroy({ useMasterKey: true });
        throw new Error("Dados da secretária incompletos");
      }

      // Verificar se o consultório foi fornecido
      if (!request.params.consultorioId) {
        await user.destroy({ useMasterKey: true });
        throw new Error("Consultório não especificado para a secretária");
      }

      // Obter o consultório
      const consultorioObj = await new Parse.Query("consultorio")
        .get(request.params.consultorioId, { useMasterKey: true });

      console.log("Criando perfil de secretária");
      const secretaria = new Parse.Object("Secretaria");
      secretaria.set('nome', nome || username);
      secretaria.set('email', email);
      secretaria.set('telefone', telefone);
      secretaria.set('cpf', cpf);
      secretaria.set('ativo', ativo !== undefined ? ativo : true);
      secretaria.set('dataCadastro', new Date());
      secretaria.set('user_secretaria', user);
      secretaria.set('consultorio', consultorioObj);

      await secretaria.save(null, { useMasterKey: true });
      console.log(`Perfil secretária criado com ID: ${secretaria.id}`);

    } else if (tipo === 'admin') {
      // Para administradores, apenas configurar a flag isAdmin
      user.set('isAdmin', true);
      await user.save(null, { useMasterKey: true });
      console.log(`Usuário admin configurado com ID: ${user.id}`);
    }

    return {
      success: true,
      message: "Usuário registrado com sucesso",
      userId: user.id,
      tipo: tipo
    };

  } catch (error) {
    console.error("Erro ao registrar usuário:", error);
    throw new Error(`Erro no registro: ${error.message}`);
  }
});

// =============================================================================
// FUNÇÕES DE VINCULAÇÃO MÉDICO-HOSPITAL
// =============================================================================

// Função para vincular médico ao hospital - MELHORADA
Parse.Cloud.define("vincularMedicoHospital", async (request) => {
  try {
    const { medicoId, hospitalId } = request.params;

    if (!medicoId || !hospitalId) {
      throw new Error("Parâmetros incompletos");
    }

    console.log(`[VINCULAR] Iniciando vinculação do médico ${medicoId} ao hospital ${hospitalId}`);

    // Obter o objeto médico
    const medicoQuery = new Parse.Query("Medico");
    const medico = await medicoQuery.get(medicoId, { useMasterKey: true });
    if (!medico) {
      console.error(`[VINCULAR] Médico com ID ${medicoId} não encontrado`);
      throw new Error(`Médico com ID ${medicoId} não encontrado`);
    }
    console.log(`[VINCULAR] Médico encontrado: ${medico.id}, Nome: ${medico.get("nome") || "Sem nome"}`);

    // Obter o objeto hospital
    const hospitalQuery = new Parse.Query("consultorio");
    const hospital = await hospitalQuery.get(hospitalId, { useMasterKey: true });
    if (!hospital) {
      console.error(`[VINCULAR] Hospital com ID ${hospitalId} não encontrado`);
      throw new Error(`Hospital com ID ${hospitalId} não encontrado`);
    }
    console.log(`[VINCULAR] Hospital encontrado: ${hospital.id}, Nome: ${hospital.get("nome") || "Sem nome"}`);

    // Adicionar médico à lista de médicos vinculados ao hospital
    let medicosVinculados = hospital.get("medicos_vinculados") || [];
    console.log(`[VINCULAR] Lista atual de médicos vinculados: ${medicosVinculados.length} médicos`);

    // Verificar se o médico já está vinculado - lógica melhorada
    const medicoJaVinculado = medicosVinculados.some(m => {
      // Caso 1: m é um ponteiro com __type
      if (typeof m === 'object' && m.__type === 'Pointer') {
        return m.objectId === medicoId;
      }
      // Caso 2: m tem propriedade id
      if (m && m.id) {
        return m.id === medicoId;
      }
      // Caso 3: m tem propriedade objectId
      if (m && m.objectId) {
        return m.objectId === medicoId;
      }
      return false;
    });

    if (!medicoJaVinculado) {
      console.log(`[VINCULAR] Adicionando médico ${medicoId} à lista de médicos vinculados do hospital ${hospitalId}`);

      // Criar um ponteiro para o médico
      const medicoPointer = {
        __type: 'Pointer',
        className: 'Medico',
        objectId: medicoId
      };

      // Adicionar o ponteiro à lista
      medicosVinculados.push(medicoPointer);
      hospital.set("medicos_vinculados", medicosVinculados);

      // Salvar o hospital com a nova lista
      const saveResult = await hospital.save(null, { useMasterKey: true });
      console.log(`[VINCULAR] Hospital salvo com sucesso: ${saveResult.id}`);

      // Verificar se a relação MedicoConsultorio já existe
      const medicoConsultorioQuery = new Parse.Query("MedicoConsultorio");
      const medicoPointerObj = Parse.Object.createWithoutData("Medico", medicoId);
      const hospitalPointerObj = Parse.Object.createWithoutData("consultorio", hospitalId);

      medicoConsultorioQuery.equalTo("medicoConsultorio_medico", medicoPointerObj);
      medicoConsultorioQuery.equalTo("medicoConsultorio_consultorio", hospitalPointerObj);

      const relacaoExistente = await medicoConsultorioQuery.first({ useMasterKey: true });

      if (!relacaoExistente) {
        // Criar entrada na tabela MedicoConsultorio para manter a relação
        const medicoConsultorio = new Parse.Object("MedicoConsultorio");

        // Criar as relações
        const medicoRelation = medicoConsultorio.relation("medicoConsultorio_medico");
        medicoRelation.add(medico);

        const hospitalRelation = medicoConsultorio.relation("medicoConsultorio_consultorio");
        hospitalRelation.add(hospital);

        // Salvar a relação
        const relationResult = await medicoConsultorio.save(null, { useMasterKey: true });
        console.log(`[VINCULAR] Relação MedicoConsultorio criada: ${relationResult.id}`);
      } else {
        console.log(`[VINCULAR] Relação MedicoConsultorio já existe: ${relacaoExistente.id}`);
      }

      // Trigger para atualizar LiveQuery
      try {
        // Criar um objeto de notificação temporário para acionar o LiveQuery
        const updateNotification = new Parse.Object("HospitalMedicoUpdate");
        updateNotification.set("hospitalId", hospitalId);
        updateNotification.set("medicoId", medicoId);
        updateNotification.set("action", "vincular");
        updateNotification.set("timestamp", new Date());
        await updateNotification.save(null, { useMasterKey: true });
        console.log(`[VINCULAR] Notificação de atualização criada para LiveQuery`);
      } catch (notifyError) {
        console.warn(`[VINCULAR] Erro ao criar notificação de atualização: ${notifyError.message}`);
        // Não interrompe o fluxo principal
      }
    } else {
      console.log(`[VINCULAR] Médico ${medicoId} já está vinculado ao hospital ${hospitalId}`);
    }

    return {
      success: true,
      message: medicoJaVinculado ? "Médico já estava vinculado" : "Médico vinculado com sucesso",
      hospitalId: hospitalId,
      medicoId: medicoId
    };
  } catch (error) {
    console.error("[VINCULAR] Erro ao vincular médico:", error);
    throw new Error(`Erro ao vincular médico: ${error.message}`);
  }
});

// Função para desvincular médico do hospital - MELHORADA
Parse.Cloud.define("desvincularMedicoHospital", async (request) => {
  try {
    const { medicoId, hospitalId } = request.params;

    if (!medicoId || !hospitalId) {
      throw new Error("Parâmetros incompletos");
    }

    console.log(`[DESVINCULAR] Iniciando desvinculação do médico ${medicoId} do hospital ${hospitalId}`);

    // Obter o objeto hospital
    const hospitalQuery = new Parse.Query("consultorio");
    const hospital = await hospitalQuery.get(hospitalId, { useMasterKey: true });
    if (!hospital) {
      console.error(`[DESVINCULAR] Hospital com ID ${hospitalId} não encontrado`);
      throw new Error(`Hospital com ID ${hospitalId} não encontrado`);
    }
    console.log(`[DESVINCULAR] Hospital encontrado: ${hospital.id}, Nome: ${hospital.get("nome") || "Sem nome"}`);

    // Obter o objeto médico para logs
    try {
      const medicoQuery = new Parse.Query("Medico");
      const medico = await medicoQuery.get(medicoId, { useMasterKey: true });
      if (medico) {
        console.log(`[DESVINCULAR] Médico encontrado: ${medico.id}, Nome: ${medico.get("nome") || "Sem nome"}`);
      }
    } catch (medicoError) {
      console.warn(`[DESVINCULAR] Aviso: Não foi possível obter detalhes do médico: ${medicoError.message}`);
      // Não interrompe o fluxo principal
    }

    // Remover médico da lista de médicos vinculados ao hospital
    let medicosVinculados = hospital.get("medicos_vinculados") || [];
    console.log(`[DESVINCULAR] Lista atual de médicos vinculados: ${medicosVinculados.length} médicos`);

    // Melhorar a lógica de filtragem para lidar com diferentes formatos de ponteiros
    const novaLista = medicosVinculados.filter(m => {
      // Caso 1: m é um ponteiro com __type
      if (typeof m === 'object' && m.__type === 'Pointer') {
        return m.objectId !== medicoId;
      }
      // Caso 2: m tem propriedade id
      if (m && m.id) {
        return m.id !== medicoId;
      }
      // Caso 3: m tem propriedade objectId
      if (m && m.objectId) {
        return m.objectId !== medicoId;
      }
      return true; // Manter se não conseguir determinar
    });

    let medicoRemovido = false;
    if (medicosVinculados.length !== novaLista.length) {
      medicoRemovido = true;
      console.log(`[DESVINCULAR] Removendo médico ${medicoId} da lista de médicos vinculados do hospital ${hospitalId}`);
      console.log(`[DESVINCULAR] Lista original: ${medicosVinculados.length} médicos, Nova lista: ${novaLista.length} médicos`);

      // Atualizar a lista de médicos vinculados
      hospital.set("medicos_vinculados", novaLista);

      // Salvar o hospital com a nova lista
      const saveResult = await hospital.save(null, { useMasterKey: true });
      console.log(`[DESVINCULAR] Hospital salvo com sucesso: ${saveResult.id}`);

      // Remover entrada na tabela MedicoConsultorio
      const medicoConsultorioQuery = new Parse.Query("MedicoConsultorio");
      const medicoPointer = Parse.Object.createWithoutData("Medico", medicoId);
      const hospitalPointer = Parse.Object.createWithoutData("consultorio", hospitalId);

      medicoConsultorioQuery.equalTo("medicoConsultorio_medico", medicoPointer);
      medicoConsultorioQuery.equalTo("medicoConsultorio_consultorio", hospitalPointer);

      const relacoes = await medicoConsultorioQuery.find({ useMasterKey: true });

      if (relacoes.length > 0) {
        console.log(`[DESVINCULAR] Removendo ${relacoes.length} relações MedicoConsultorio`);
        await Parse.Object.destroyAll(relacoes, { useMasterKey: true });
        console.log(`[DESVINCULAR] Relações MedicoConsultorio removidas com sucesso`);
      } else {
        console.log(`[DESVINCULAR] Nenhuma relação MedicoConsultorio encontrada para remover`);
      }

      // Trigger para atualizar LiveQuery
      try {
        // Criar um objeto de notificação temporário para acionar o LiveQuery
        const updateNotification = new Parse.Object("HospitalMedicoUpdate");
        updateNotification.set("hospitalId", hospitalId);
        updateNotification.set("medicoId", medicoId);
        updateNotification.set("action", "desvincular");
        updateNotification.set("timestamp", new Date());
        await updateNotification.save(null, { useMasterKey: true });
        console.log(`[DESVINCULAR] Notificação de atualização criada para LiveQuery`);
      } catch (notifyError) {
        console.warn(`[DESVINCULAR] Erro ao criar notificação de atualização: ${notifyError.message}`);
        // Não interrompe o fluxo principal
      }
    } else {
      console.log(`[DESVINCULAR] Médico ${medicoId} não estava vinculado ao hospital ${hospitalId}`);
    }

    return {
      success: true,
      message: medicoRemovido ? "Médico desvinculado com sucesso" : "Médico não estava vinculado",
      hospitalId: hospitalId,
      medicoId: medicoId
    };
  } catch (error) {
    console.error("[DESVINCULAR] Erro ao desvincular médico:", error);
    throw new Error(`Erro ao desvincular médico: ${error.message}`);
  }
});

// Função para notificar sobre mudanças na fila
Parse.Cloud.define("notificarMudancaFila", async (request) => {
  try {
    const { filaId, consultorioId, medicoId, acao } = request.params;

    if (!filaId || !consultorioId || !medicoId || !acao) {
      throw new Error("Parâmetros incompletos");
    }

    console.log(`[FILA] Notificando mudança na fila: ${filaId}, ação: ${acao}`);

    // Criar um objeto de notificação para acionar o LiveQuery
    const filaUpdate = new Parse.Object("FilaUpdate");
    filaUpdate.set("filaId", filaId);
    filaUpdate.set("consultorioId", consultorioId);
    filaUpdate.set("medicoId", medicoId);
    filaUpdate.set("acao", acao);
    filaUpdate.set("timestamp", new Date());

    await filaUpdate.save(null, { useMasterKey: true });
    console.log(`[FILA] Notificação de atualização criada para LiveQuery: ${filaUpdate.id}`);

    return {
      success: true,
      message: "Notificação de mudança na fila enviada com sucesso"
    };
  } catch (error) {
    console.error("[FILA] Erro ao notificar mudança na fila:", error);
    throw new Error(`Erro ao notificar mudança na fila: ${error.message}`);
  }
});

// ✅ TRIGGER PRINCIPAL CONSOLIDADO PARA FILA
Parse.Cloud.afterSave("Fila", async (request) => {
  try {
    const fila = request.object;
    const isNew = request.original === undefined;

    // Obter dados necessários
    const filaId = fila.id;
    const consultorio = fila.get("consultorio");
    const medico = fila.get("medico");
    const idPaciente = fila.get("idPaciente") || fila.get("deviceId");
    const statusAtual = fila.get("status");
    const posicaoAtual = fila.get("posicao");

    // Log para debug
    console.log(`[FILA TRIGGER] ${isNew ? 'Nova fila' : 'Fila atualizada'}: ${filaId}`);
    console.log(`[FILA TRIGGER] Status: ${statusAtual}, Posição: ${posicaoAtual}, Paciente: ${idPaciente}`);

    if (!consultorio || !medico) {
      console.log("[FILA TRIGGER] Fila sem consultório ou médico, ignorando");
      return;
    }

    const consultorioId = consultorio.id || consultorio.objectId;
    const medicoId = medico.id || medico.objectId;

    if (!consultorioId || !medicoId) {
      console.log("[FILA TRIGGER] IDs de consultório ou médico não encontrados");
      return;
    }

    // ✅ DETECTAR MUDANÇAS DE POSIÇÃO E STATUS
    if (!isNew && request.original) {
      const statusAnterior = request.original.get("status");
      const posicaoAnterior = request.original.get("posicao");

      // 1. Detectar mudança de posição
      if (posicaoAnterior && posicaoAtual !== posicaoAnterior) {
        console.log(`[FILA TRIGGER] Mudança de posição detectada: ${posicaoAnterior} → ${posicaoAtual}`);

        if (idPaciente) {
          try {
            // Gerar mensagem personalizada baseada na posição
            let titulo = "Sua posição na fila mudou";
            let mensagem = `Sua posição foi atualizada para ${posicaoAtual}`;

            if (posicaoAtual === 1) {
              titulo = "É a sua vez!";
              mensagem = "Agora é a sua vez! Dirija-se ao consultório.";
            } else if (posicaoAtual === 2) {
              titulo = "Você é o próximo!";
              mensagem = "Você é o próximo paciente a ser atendido.";
            } else if (posicaoAtual === 3) {
              titulo = "Entre os próximos!";
              mensagem = "Você está entre os próximos a serem atendidos.";
            }

            // Criar notificação no banco
            const notification = new Parse.Object("Notification");
            notification.set("title", titulo);
            notification.set("body", mensagem);
            notification.set("data", {
              tipo: 'mudanca_posicao',
              filaId: filaId,
              posicao: posicaoAtual.toString(),
              posicaoAnterior: posicaoAnterior.toString(),
              timestamp: new Date().toISOString(),
              action: 'open_fila_screen'
            });
            notification.set("userId", idPaciente);
            notification.set("read", false);
            notification.set("sent", true);

            const acl = new Parse.ACL();
            acl.setPublicReadAccess(true);
            acl.setPublicWriteAccess(true);
            notification.setACL(acl);

            await notification.save(null, { useMasterKey: true });

            console.log(`[FILA TRIGGER] Notificação de mudança de posição enviada para ${idPaciente}: ${posicaoAnterior} → ${posicaoAtual}`);
          } catch (e) {
            console.error(`[FILA TRIGGER] Erro ao enviar notificação de posição: ${e.message}`);
          }
        }
      }

      // 2. Detectar quando alguém inicia atendimento
      if (statusAnterior === "aguardando" && statusAtual === "em_atendimento") {
        console.log(`[FILA TRIGGER] Paciente iniciou atendimento (${filaId}), notificando paciente e outros...`);

        try {
          // ✅ PRIMEIRO: Notificar o próprio paciente que foi chamado para atendimento
          if (idPaciente) {
            const notificationPaciente = new Parse.Object("Notification");
            notificationPaciente.set("title", "Você foi chamado!");
            notificationPaciente.set("body", "Dirija-se ao consultório para o atendimento");
            notificationPaciente.set("data", {
              tipo: 'inicio_atendimento',
              filaId: filaId,
              status: 'em_atendimento',
              timestamp: new Date().toISOString(),
              action: 'open_fila_screen',
              priority: 'high'
            });
            notificationPaciente.set("userId", idPaciente);
            notificationPaciente.set("read", false);
            notificationPaciente.set("sent", true);

            const acl = new Parse.ACL();
            acl.setPublicReadAccess(true);
            acl.setPublicWriteAccess(true);
            notificationPaciente.setACL(acl);

            await notificationPaciente.save(null, { useMasterKey: true });
            console.log(`[FILA TRIGGER] ✅ Notificação de início de atendimento enviada para paciente ${idPaciente}`);

            // ✅ ENVIAR PUSH NOTIFICATION TAMBÉM
            try {
              await Parse.Cloud.run("enviarNotificacaoPush", {
                canais: [`patient_${idPaciente}`],
                titulo: 'Você foi chamado!',
                mensagem: 'Dirija-se ao consultório para o atendimento',
                tipo: 'inicio_atendimento',
                dadosAdicionais: {
                  badge: 1,
                  sound: 'default',
                  category: 'ATENDIMENTO_INICIADO',
                  filaId: filaId,
                  priority: 'high'
                }
              }, { useMasterKey: true });
              console.log(`[FILA TRIGGER] ✅ Push notification enviada para paciente ${idPaciente}`);
            } catch (pushError) {
              console.error(`[FILA TRIGGER] ❌ Erro ao enviar push notification: ${pushError.message}`);
            }
          }

          // ✅ SEGUNDO: Chamar função para notificar mudanças de posição de outros pacientes
          const notificationResult = await Parse.Cloud.run("notificarMudancasPosicaoFila", {
            medicoId: medicoId,
            consultorioId: consultorioId,
            pacienteQueIniciouId: idPaciente
          }, { useMasterKey: true });

          console.log(`[FILA TRIGGER] Notificações enviadas para outros pacientes: ${JSON.stringify(notificationResult)}`);
        } catch (notificationError) {
          console.error(`[FILA TRIGGER] Erro ao notificar outros pacientes: ${notificationError.message}`);
        }
      }
    }

    // ✅ CRIAR ATUALIZAÇÕES PARA LIVEQUERY
    const filaUpdate = new Parse.Object("FilaUpdate");
    filaUpdate.set("filaId", filaId);
    filaUpdate.set("consultorioId", consultorioId);
    filaUpdate.set("medicoId", medicoId);
    filaUpdate.set("acao", isNew ? "criar" : "atualizar");
    filaUpdate.set("timestamp", new Date());

    await filaUpdate.save(null, { useMasterKey: true });
    console.log(`[FILA TRIGGER] FilaUpdate criado: ${filaUpdate.id}`);

    // ✅ WEBHOOK PARA COMPATIBILIDADE
    const webhookParams = {
      key: '7SfDLUbeOXtCpLa09JpTcGaGjYeMiDyXlNg2uOU1',
      event: isNew ? 'create' : 'update',
      className: 'Fila',
      objectId: filaId
    };

    Parse.Cloud.run("webhook", webhookParams, { useMasterKey: true })
      .catch(error => console.error(`[FILA TRIGGER] Erro webhook: ${error.message}`));

  } catch (error) {
    console.error(`[FILA TRIGGER] Erro geral: ${error.message}`);
  }
});

// Hook para automaticamente notificar sobre novas mensagens
Parse.Cloud.afterSave("MensagemFila", async (request) => {
  try {
    const mensagem = request.object;
    const isNew = request.original === undefined;

    // Só notificar para novas mensagens
    if (!isNew) {
      console.log("[MENSAGEM] Atualização de mensagem existente, ignorando notificação");
      return;
    }

    // Obter dados necessários
    const mensagemId = mensagem.id;
    const filaId = mensagem.get("fila_id")?.id || mensagem.get("fila_id")?.objectId;
    const idPaciente = mensagem.get("id_paciente");
    const broadcast = mensagem.get("broadcast") || false;

    // Obter consultório e médico
    let consultorioId = null;
    let medicoId = null;

    // Se temos fila_id, obter consultório e médico da fila
    if (filaId) {
      try {
        const filaQuery = new Parse.Query("Fila");
        const fila = await filaQuery.get(filaId, { useMasterKey: true });

        if (fila) {
          const consultorio = fila.get("consultorio");
          const medico = fila.get("medico");

          if (consultorio) {
            consultorioId = consultorio.id || consultorio.objectId;
          }

          if (medico) {
            medicoId = medico.id || medico.objectId;
          }
        }
      } catch (filaError) {
        console.error("[MENSAGEM] Erro ao obter fila:", filaError);
      }
    } else {
      // Se não temos fila_id, tentar obter diretamente da mensagem
      const consultorio = mensagem.get("consultorio");
      const medico = mensagem.get("medico");

      if (consultorio) {
        consultorioId = consultorio.id || consultorio.objectId;
      }

      if (medico) {
        medicoId = medico.id || medico.objectId;
      }
    }

    // Se não conseguimos obter consultório ou médico, não podemos notificar
    if (!consultorioId && !medicoId && !idPaciente) {
      console.log("[MENSAGEM] Dados insuficientes para notificação");
      return;
    }

    // Criar notificação de atualização
    const mensagemUpdate = new Parse.Object("MensagemUpdate");
    mensagemUpdate.set("mensagemId", mensagemId);

    if (consultorioId) {
      mensagemUpdate.set("consultorioId", consultorioId);
    }

    if (medicoId) {
      mensagemUpdate.set("medicoId", medicoId);
    }

    if (idPaciente) {
      mensagemUpdate.set("idPaciente", idPaciente);
    }

    if (filaId) {
      mensagemUpdate.set("filaId", filaId);
    }

    mensagemUpdate.set("broadcast", broadcast);
    mensagemUpdate.set("timestamp", new Date());

    await mensagemUpdate.save(null, { useMasterKey: true });
    console.log(`[MENSAGEM] Notificação automática de mensagem criada: ${mensagemUpdate.id}`);
  } catch (error) {
    console.error("[MENSAGEM] Erro ao criar notificação automática:", error);
    // Não interrompe o fluxo principal
  }
});

// Função para buscar hospitais disponíveis para um médico
Parse.Cloud.define("buscarHospitaisDisponiveis", async (request) => {
  try {
    const { medicoId } = request.params;

    if (!medicoId) {
      throw new Error("ID do médico não fornecido");
    }

    console.log(`Buscando hospitais disponíveis para médico ${medicoId}`);

    // Buscar hospitais ativos
    const hospitalQuery = new Parse.Query("consultorio");
    hospitalQuery.equalTo("ativo", true);
    hospitalQuery.ascending("nome");

    const hospitais = await hospitalQuery.find({ useMasterKey: true });
    console.log(`Encontrados ${hospitais.length} hospitais ativos`);

    // Verificar em quais hospitais o médico já está vinculado
    const resultado = hospitais.map(hospital => {
      const medicosVinculados = hospital.get("medicos_vinculados") || [];
      const vinculado = medicosVinculados.some(
        m => m.id === medicoId || (m.objectId && m.objectId === medicoId)
      );

      return {
        id: hospital.id,
        nome: hospital.get("nome"),
        cidade: hospital.get("cidade"),
        estado: hospital.get("estado"),
        tipo: hospital.get("tipo"),
        vinculado: vinculado
      };
    });

    return { success: true, hospitais: resultado };
  } catch (error) {
    console.error("Erro ao buscar hospitais:", error);
    throw new Error(`Erro ao buscar hospitais: ${error.message}`);
  }
});

// =============================================================================
// FUNÇÕES DE GERENCIAMENTO DE SECRETÁRIAS
// =============================================================================

// Função para cadastrar secretária
Parse.Cloud.define("cadastrarSecretaria", async (request) => {
  try {
    const {
      nome,
      email,
      senha,
      telefone,
      cpf,
      consultorioId,
      ativo = true
    } = request.params;

    if (!nome || !email || !senha || !consultorioId) {
      throw new Error("Parâmetros obrigatórios não fornecidos");
    }

    console.log(`Iniciando cadastro de secretária: ${nome} (${email}) para consultório ${consultorioId}`);

    // Verificar se o e-mail já está em uso
    const emailQuery = new Parse.Query(Parse.User);
    emailQuery.equalTo("email", email);
    const emailExists = await emailQuery.first({ useMasterKey: true });

    if (emailExists) {
      throw new Error("Este e-mail já está em uso");
    }

    // Verificar se o consultório existe
    const consultorioQuery = new Parse.Query("consultorio");
    const consultorio = await consultorioQuery.get(consultorioId, { useMasterKey: true });

    if (!consultorio) {
      throw new Error("Consultório não encontrado");
    }

    // Criar usuário
    const user = new Parse.User();
    user.set("username", email);
    user.set("password", senha);
    user.set("email", email);
    user.set("tipo", "secretaria");
    user.set("dataCadastro", new Date());

    // Salvar o usuário com masterKey para garantir permissões
    await user.signUp(null, { useMasterKey: true });
    console.log(`Usuário secretária criado: ${user.id}`);

    // Adicionar à role de secretaria
    const roleQuery = new Parse.Query(Parse.Role);
    roleQuery.equalTo("name", "role:secretaria");
    let secretariaRole = await roleQuery.first({ useMasterKey: true });

    // Se a role não existir, criar
    if (!secretariaRole) {
      console.log("Criando nova role:secretaria");
      const acl = new Parse.ACL();
      acl.setPublicReadAccess(true);

      secretariaRole = new Parse.Role("role:secretaria", acl);
      await secretariaRole.save(null, { useMasterKey: true });
    }

    // Adicionar usuário à role
    secretariaRole.getUsers().add(user);
    await secretariaRole.save(null, { useMasterKey: true });
    console.log(`Usuário adicionado à role:secretaria`);

    // Atualizar roles no usuário também
    const userRoles = user.get("roles") || [];
    if (!userRoles.includes("role:secretaria")) {
      userRoles.push("role:secretaria");
      user.set("roles", userRoles);
      await user.save(null, { useMasterKey: true });
    }

    // Criar perfil de secretária com ACL pública
    const secretaria = new Parse.Object("Secretaria");
    secretaria.set("nome", nome);
    secretaria.set("email", email);
    secretaria.set("telefone", telefone);
    secretaria.set("cpf", cpf);
    secretaria.set("ativo", ativo);
    secretaria.set("dataCadastro", new Date());
    secretaria.set("user_secretaria", user);
    secretaria.set("consultorio", consultorio);

    // Configurar ACL para garantir permissões
    const secretariaACL = new Parse.ACL();
    secretariaACL.setPublicReadAccess(true);
    secretariaACL.setRoleReadAccess("role:hospital", true);
    secretariaACL.setRoleWriteAccess("role:hospital", true);
    secretariaACL.setRoleReadAccess("role:secretaria", true);
    secretariaACL.setRoleWriteAccess("role:secretaria", true);
    secretariaACL.setReadAccess(user.id, true);
    secretariaACL.setWriteAccess(user.id, true);

    // Adicionar permissão ao consultório também
    const consultorioUser = consultorio.get("user_consultorio");
    if (consultorioUser && consultorioUser.id) {
      secretariaACL.setReadAccess(consultorioUser.id, true);
      secretariaACL.setWriteAccess(consultorioUser.id, true);
    }

    secretaria.setACL(secretariaACL);

    await secretaria.save(null, { useMasterKey: true });
    console.log(`Perfil de secretária criado: ${secretaria.id}`);

    // Estabelecer a relação bidirecional
    try {
      // Criar relação do usuário para a secretária
      const userSecretariaRelation = user.relation('user_secretaria');
      userSecretariaRelation.add(secretaria);
      await user.save(null, { useMasterKey: true });
      console.log(`Relação bidirecional estabelecida para usuário ${user.id} -> secretária ${secretaria.id}`);
    } catch (relationError) {
      console.log(`Aviso: Erro ao criar relação bidirecional: ${relationError.message}`);
      // Não interromper o fluxo só por causa deste erro
    }

    return {
      success: true,
      secretariaId: secretaria.id,
      userId: user.id
    };
  } catch (error) {
    console.error("Erro ao cadastrar secretária:", error);
    throw new Error(`Erro ao cadastrar secretária: ${error.message}`);
  }
});

// Função para atualizar status da secretária
Parse.Cloud.define("atualizarStatusSecretaria", async (request) => {
  try {
    const { secretariaId, ativo } = request.params;

    if (!secretariaId || ativo === undefined) {
      throw new Error("Parâmetros obrigatórios não fornecidos");
    }

    console.log(`Atualizando status da secretária ${secretariaId} para ${ativo ? 'ativo' : 'inativo'}`);

    // Buscar secretária com hospital
    const secretariaQuery = new Parse.Query("Secretaria");
    secretariaQuery.include("user_secretaria");
    secretariaQuery.include("consultorio");
    const secretaria = await secretariaQuery.get(secretariaId, { useMasterKey: true });

    if (!secretaria) {
      throw new Error("Secretária não encontrada");
    }

    // Verificar permissões (exceto se for master)
    if (!request.master) {
      const currentUser = request.user;
      if (!currentUser) {
        throw new Parse.Error(403, "Usuário não autenticado");
      }

      const consultorio = secretaria.get("consultorio");

      // Verificar se o usuário tem a função de hospital ou é admin
      const roles = currentUser.get("roles") || [];
      const isAdmin = roles.includes("role:admin");
      const isHospital = roles.includes("role:hospital");

      // Verificar se o hospital atual é o mesmo do usuário logado (a menos que seja admin)
      if (!isAdmin && isHospital && consultorio) {
        const userConsultorioQuery = new Parse.Query("consultorio");
        userConsultorioQuery.equalTo("user_consultorio", currentUser);
        const userConsultorio = await userConsultorioQuery.first({ useMasterKey: true });

        if (!userConsultorio || userConsultorio.id !== consultorio.id) {
          throw new Parse.Error(403, "Sem permissão para modificar secretária de outro consultório");
        }
      }

      if (!isAdmin && !isHospital) {
        throw new Parse.Error(403, "Sem permissão para modificar status da secretária");
      }
    }

    // Atualizar status da secretária
    secretaria.set("ativo", ativo);
    await secretaria.save(null, { useMasterKey: true });

    // Atualizar status do usuário também (opcional, dependendo da lógica do app)
    const user = secretaria.get("user_secretaria");
    if (user) {
      try {
        user.set("ativo", ativo);
        await user.save(null, { useMasterKey: true });
      } catch (userError) {
        console.log(`Aviso: Não foi possível atualizar status do usuário: ${userError.message}`);
      }
    }

    return {
      success: true,
      mensagem: `Secretária ${ativo ? 'ativada' : 'desativada'} com sucesso`
    };
  } catch (error) {
    console.error("Erro ao atualizar status da secretária:", error);
    throw new Error(`Erro ao atualizar status: ${error.message}`);
  }
});

// Função para redefinir senha da secretária
Parse.Cloud.define("redefinirSenhaSecretaria", async (request) => {
  try {
    const { secretariaId } = request.params;

    if (!secretariaId) {
      throw new Error("ID da secretária não fornecido");
    }

    console.log(`Redefinindo senha da secretária ${secretariaId}`);

    // Buscar secretária com seu usuário e consultório
    const secretariaQuery = new Parse.Query("Secretaria");
    secretariaQuery.include("user_secretaria");
    secretariaQuery.include("consultorio");
    const secretaria = await secretariaQuery.get(secretariaId, { useMasterKey: true });

    if (!secretaria) {
      throw new Error("Secretária não encontrada");
    }

    // Verificar permissões (exceto se for master)
    if (!request.master) {
      const currentUser = request.user;
      if (!currentUser) {
        throw new Parse.Error(403, "Usuário não autenticado");
      }

      const consultorio = secretaria.get("consultorio");

      // Verificar se o usuário tem a função de hospital ou é admin
      const roles = currentUser.get("roles") || [];
      const isAdmin = roles.includes("role:admin");
      const isHospital = roles.includes("role:hospital");

      // Verificar se o hospital atual é o mesmo do usuário logado (a menos que seja admin)
      if (!isAdmin && isHospital && consultorio) {
        const userConsultorioQuery = new Parse.Query("consultorio");
        userConsultorioQuery.equalTo("user_consultorio", currentUser);
        const userConsultorio = await userConsultorioQuery.first({ useMasterKey: true });

        if (!userConsultorio || userConsultorio.id !== consultorio.id) {
          throw new Parse.Error(403, "Sem permissão para redefinir senha de secretária de outro consultório");
        }
      }

      if (!isAdmin && !isHospital) {
        throw new Parse.Error(403, "Sem permissão para redefinir senha da secretária");
      }
    }

    const user = secretaria.get("user_secretaria");

    if (!user) {
      throw new Error("Usuário da secretária não encontrado");
    }

    // Enviar e-mail de redefinição de senha
    await Parse.User.requestPasswordReset(user.get("email"), { useMasterKey: true });

    return {
      success: true,
      mensagem: "E-mail de redefinição de senha enviado com sucesso"
    };
  } catch (error) {
    console.error("Erro ao redefinir senha da secretária:", error);
    throw new Error(`Erro ao redefinir senha: ${error.message}`);
  }
});

// Função para excluir secretária com MasterKey
Parse.Cloud.define("excluirSecretariaMasterKey", async (request) => {
  try {
    const { secretariaId } = request.params;

    if (!secretariaId) {
      throw new Parse.Error(400, "ID da secretária não fornecido");
    }

    console.log(`Tentando excluir secretária ${secretariaId} com Master Key`);

    // Buscar a secretária
    const secretariaQuery = new Parse.Query("Secretaria");
    const secretaria = await secretariaQuery.get(secretariaId, { useMasterKey: true });

    if (!secretaria) {
      throw new Parse.Error(404, "Secretária não encontrada");
    }

    // Excluir a secretária com masterKey
    const result = await secretaria.destroy({ useMasterKey: true });
    console.log(`Secretária ${secretariaId} excluída com sucesso`);

    return {
      success: true,
      message: "Secretária excluída com sucesso"
    };
  } catch (error) {
    console.error(`Erro ao excluir secretária: ${error.message}`);
    throw new Parse.Error(500, `Erro ao excluir secretária: ${error.message}`);
  }
});

// NOVA FUNÇÃO: Excluir secretária e seu usuário em uma única operação
Parse.Cloud.define("excluirSecretariaComUsuario", async (request) => {
  try {
    const { secretariaId } = request.params;

    if (!secretariaId) {
      throw new Parse.Error(400, "ID da secretária não fornecido");
    }

    console.log(`=== INICIANDO EXCLUSÃO COMPLETA DA SECRETÁRIA ${secretariaId} ===`);

    // 1. Buscar a secretária com o usuário incluído
    const secretariaQuery = new Parse.Query("Secretaria");
    secretariaQuery.include("user_secretaria");
    secretariaQuery.include("consultorio");
    const secretaria = await secretariaQuery.get(secretariaId, { useMasterKey: true });

    if (!secretaria) {
      throw new Parse.Error(404, "Secretária não encontrada");
    }

    // 2. Obter referência ao usuário e consultório
    const user = secretaria.get("user_secretaria");
    const consultorio = secretaria.get("consultorio");
    let userId = null;
    let userEmail = null;

    if (user) {
      userId = user.id;
      userEmail = user.get("email") || user.getEmail();
      console.log(`Usuário associado encontrado - ID: ${userId}, Email: ${userEmail}`);

      // 3. Remover usuário da role:secretaria
      try {
        const roleQuery = new Parse.Query(Parse.Role);
        roleQuery.equalTo("name", "role:secretaria");
        const role = await roleQuery.first({ useMasterKey: true });

        if (role) {
          role.getUsers().remove(user);
          await role.save(null, { useMasterKey: true });
          console.log(`Usuário removido da role:secretaria`);
        }
      } catch (roleError) {
        console.log(`Erro ao remover da role: ${roleError.message}`);
      }

      // 4. Remover todas as sessões do usuário
      try {
        const sessionQuery = new Parse.Query("_Session");
        sessionQuery.equalTo("user", user);
        const sessions = await sessionQuery.find({ useMasterKey: true });

        if (sessions.length > 0) {
          console.log(`Removendo ${sessions.length} sessões do usuário`);
          await Parse.Object.destroyAll(sessions, { useMasterKey: true });
        }
      } catch (e) {
        console.log(`Erro ao remover sessões: ${e.message}`);
      }

      // 5. Excluir o usuário da classe _User
      try {
        await user.destroy({ useMasterKey: true });
        console.log(`Usuário ${userId} excluído com sucesso`);
      } catch (userError) {
        console.error(`Erro ao excluir usuário: ${userError.message}`);

        // Tentar método alternativo de exclusão
        try {
          const userToDelete = new Parse.User();
          userToDelete.id = userId;
          await userToDelete.destroy({ useMasterKey: true });
          console.log(`Usuário ${userId} excluído com sucesso (método alternativo)`);
        } catch (alternativeError) {
          console.error(`Erro no método alternativo: ${alternativeError.message}`);
          throw alternativeError;
        }
      }
    }

    // 6. Excluir a secretária
    await secretaria.destroy({ useMasterKey: true });
    console.log(`Secretária ${secretariaId} excluída com sucesso`);

    return {
      success: true,
      message: "Secretária e usuário excluídos com sucesso",
      deletedUserId: userId
    };

  } catch (error) {
    console.error(`Erro ao excluir secretária e usuário: ${error.message}`);
    throw new Parse.Error(500, `Erro ao excluir: ${error.message}`);
  }
});

// =============================================================================
// FUNÇÕES DE ADMINISTRAÇÃO
// =============================================================================

// Função para gerenciar hospital (criar ou atualizar)
Parse.Cloud.define("gerenciarHospital", async (request) => {
  try {
    const {
      hospitalId,
      nome,
      email,
      cnpj,
      telefone,
      tipo,
      latitude,
      longitude,
      ativo = true,
      senha,
      gerarNovaSenha = false
    } = request.params;

    if (!nome || !email || !cnpj || !telefone || !tipo || latitude === undefined || longitude === undefined) {
      throw new Error("Parâmetros obrigatórios não fornecidos");
    }

    // Verificar se é atualização ou criação
    if (hospitalId) {
      console.log(`Atualizando hospital ${hospitalId}`);

      // Buscar hospital existente
      const hospitalQuery = new Parse.Query("consultorio");
      hospitalQuery.include("user_consultorio");
      const hospital = await hospitalQuery.get(hospitalId, { useMasterKey: true });

      if (!hospital) {
        throw new Error("Hospital não encontrado");
      }

      // Atualizar dados do hospital
      hospital.set("nome", nome);
      hospital.set("cnpj", cnpj);
      hospital.set("telefone", telefone);
      hospital.set("tipo", tipo);
      hospital.set("latitude", latitude);
      hospital.set("longitude", longitude);
      hospital.set("ativo", ativo);

      await hospital.save(null, { useMasterKey: true });

      // Atualizar dados do usuário se necessário
      const user = hospital.get("user_consultorio");

      if (user) {
        if (user.get("email") !== email) {
          user.set("email", email);
          user.set("username", email); // Atualizar username também se usar email como username
          await user.save(null, { useMasterKey: true });
        }

        // Atualizar senha se solicitado
        if (senha || gerarNovaSenha) {
          const novaSenha = senha || gerarSenhaAleatoria();
          user.setPassword(novaSenha);
          await user.save(null, { useMasterKey: true });

          return {
            success: true,
            mensagem: "Hospital atualizado com sucesso",
            novaSenha: gerarNovaSenha ? novaSenha : undefined
          };
        }
      } else {
        // Criar novo usuário se não existir
        const novaSenha = senha || gerarSenhaAleatoria();
        const newUser = new Parse.User();
        newUser.set("username", email);
        newUser.set("password", novaSenha);
        newUser.set("email", email);
        newUser.set("tipo", "consultorio");
        newUser.set("dataCadastro", new Date());

        await newUser.signUp(null, { useMasterKey: true });

        // Vincular novo usuário ao hospital
        hospital.set("user_consultorio", newUser);
        await hospital.save(null, { useMasterKey: true });

        return {
          success: true,
          mensagem: "Hospital atualizado e novo usuário criado",
          novaSenha: novaSenha
        };
      }

      return {
        success: true,
        mensagem: "Hospital atualizado com sucesso"
      };

    } else {
      // Criar novo hospital
      console.log("Criando novo hospital");

      if (!senha && !gerarNovaSenha) {
        throw new Error("Senha obrigatória para novo hospital");
      }

      const novaSenha = senha || gerarSenhaAleatoria();

      // Criar usuário primeiro
      const user = new Parse.User();
      user.set("username", email);
      user.set("password", novaSenha);
      user.set("email", email);
      user.set("tipo", "consultorio");
      user.set("dataCadastro", new Date());

      await user.signUp(null, { useMasterKey: true });
      console.log(`Usuário do hospital criado: ${user.id}`);

      // Criar hospital
      const hospital = new Parse.Object("consultorio");
      hospital.set("nome", nome);
      hospital.set("cnpj", cnpj);
      hospital.set("telefone", telefone);
      hospital.set("tipo", tipo);
      hospital.set("latitude", latitude);
      hospital.set("longitude", longitude);
      hospital.set("ativo", ativo);
      hospital.set("dataCadastro", new Date());
      hospital.set("user_consultorio", user);
      hospital.set("medicos_vinculados", []);

      await hospital.save(null, { useMasterKey: true });
      console.log(`Hospital criado: ${hospital.id}`);

      return {
        success: true,
        mensagem: "Hospital criado com sucesso",
        hospitalId: hospital.id,
        userId: user.id,
        novaSenha: gerarNovaSenha ? novaSenha : undefined
      };
    }
  } catch (error) {
    console.error("Erro ao gerenciar hospital:", error);
    throw new Error(`Erro ao gerenciar hospital: ${error.message}`);
  }
});

// Função para excluir usuário com MasterKey
Parse.Cloud.define("excluirUsuarioMasterKey", async (request) => {
  try {
    const { userId } = request.params;

    if (!userId) {
      throw new Parse.Error(400, "ID do usuário não fornecido");
    }

    console.log(`Tentando excluir usuário ${userId} com Master Key`);

    // Buscar o usuário
    const userQuery = new Parse.Query(Parse.User);
    const user = await userQuery.get(userId, { useMasterKey: true });

    if (!user) {
      throw new Parse.Error(404, "Usuário não encontrado");
    }

    // Excluir o usuário com masterKey
    const result = await user.destroy({ useMasterKey: true });
    console.log(`Usuário ${userId} excluído com sucesso`);

    return {
      success: true,
      message: "Usuário excluído com sucesso"
    };
  } catch (error) {
    console.error(`Erro ao excluir usuário: ${error.message}`);
    throw new Parse.Error(500, `Erro ao excluir usuário: ${error.message}`);
  }
});

// Função para excluir usuário com operação Raw (último recurso)
Parse.Cloud.define("excluirUsuarioRaw", async (request) => {
  try {
    const { userId } = request.params;

    if (!userId) {
      throw new Parse.Error(400, "ID do usuário não fornecido");
    }

    console.log(`Tentando excluir usuário ${userId} com operação Raw`);

    // Usar Database Adapter para executar SQL direto (extremo cuidado!)
    const databaseAdapter = Parse.CoreManager.getObjectStateController().database.adapter;

    // Remover sessões do usuário primeiro
    await databaseAdapter._rawQuery(`DELETE FROM "_Session" WHERE "user" = $1`, [userId]);

    // Remover o usuário
    await databaseAdapter._rawQuery(`DELETE FROM "_User" WHERE "objectId" = $1`, [userId]);

    console.log(`Usuário ${userId} excluído via operação Raw`);

    return {
      success: true,
      message: "Usuário excluído com sucesso via operação Raw"
    };
  } catch (error) {
    console.error(`Erro ao excluir usuário via Raw: ${error.message}`);
    throw new Parse.Error(500, `Erro ao excluir usuário via Raw: ${error.message}`);
  }
});

// Função para excluir usuário com cascata de operações
Parse.Cloud.define("excluirUsuarioCascade", async (request) => {
  try {
    const { userId } = request.params;

    if (!userId) {
      throw new Parse.Error(400, "ID do usuário não fornecido");
    }

    console.log(`Tentando excluir usuário ${userId} com método cascata`);

    // 1. Remover todas as sessões do usuário
    try {
      const sessionQuery = new Parse.Query('_Session');
      sessionQuery.equalTo('user', Parse.Object.createWithoutData('_User', userId));
      const sessions = await sessionQuery.find({ useMasterKey: true });

      if (sessions.length > 0) {
        console.log(`Removendo ${sessions.length} sessões ativas do usuário`);
        await Parse.Object.destroyAll(sessions, { useMasterKey: true });
      }
    } catch (e) {
      console.log(`Erro ao remover sessões: ${e.message}`);
    }

    // 2. Remover todas as secretárias vinculadas a este usuário
    try {
      const secretariaQuery = new Parse.Query('Secretaria');
      secretariaQuery.equalTo('user_secretaria', Parse.Object.createWithoutData('_User', userId));
      const secretarias = await secretariaQuery.find({ useMasterKey: true });

      if (secretarias.length > 0) {
        console.log(`Encontradas ${secretarias.length} secretárias vinculadas ao usuário`);

        // ADICIONAR: Remover relações bidirecionais
        const userQuery = new Parse.Query(Parse.User);
        const user = await userQuery.get(userId, { useMasterKey: true });

        if (user) {
          // Obter relação e remover todas as secretárias
          const userSecretariaRelation = user.relation('user_secretaria');
          secretarias.forEach(secretaria => {
            userSecretariaRelation.remove(secretaria);
          });
          await user.save(null, { useMasterKey: true });
          console.log(`Relações bidirecionais removidas para usuário ${userId}`);
        }

        // Desvincular usuário antes de excluir
        for (const secretaria of secretarias) {
          secretaria.unset('user_secretaria');
          await secretaria.save(null, { useMasterKey: true });
        }
        await Parse.Object.destroyAll(secretarias, { useMasterKey: true });
      }
    } catch (e) {
      console.log(`Erro ao remover secretárias: ${e.message}`);
    }

    // 3. Tentar a exclusão do usuário usando diferentes métodos
    try {
      // Método 1: Usando Parse.User.createWithoutData
      const userToDelete = Parse.User.createWithoutData(userId);
      await userToDelete.destroy({ useMasterKey: true });
      console.log(`Usuário ${userId} excluído com sucesso (método 1)`);
    } catch (error) {
      console.log(`Falha no método 1: ${error.message}`);

      try {
        // Método 2: Usando consulta para obter e excluir
        const userQuery = new Parse.Query(Parse.User);
        const user = await userQuery.get(userId, { useMasterKey: true });
        await user.destroy({ useMasterKey: true });
        console.log(`Usuário ${userId} excluído com sucesso (método 2)`);
      } catch (error2) {
        console.log(`Falha no método 2: ${error2.message}`);

        try {
          // Método 3: Usar operação direta em _User
          const userObject = new Parse.Object("_User");
          userObject.id = userId;
          await userObject.destroy({ useMasterKey: true });
          console.log(`Usuário ${userId} excluído com sucesso (método 3)`);
        } catch (error3) {
          console.log(`Falha no método 3: ${error3.message}`);

          // Método 4: Raw SQL (último recurso)
          try {
            const database = Parse.database;
            await database.rawQuery(`DELETE FROM "_User" WHERE "objectId" = '${userId}'`);
            console.log(`Usuário ${userId} excluído com sucesso (método 4 - SQL direto)`);
          } catch (error4) {
            console.log(`Todos os métodos de exclusão falharam: ${error4.message}`);
            throw new Parse.Error(500, "Falha em todos os métodos de exclusão");
          }
        }
      }
    }

    return {
      success: true,
      message: "Usuário excluído com sucesso via operação em cascata"
    };
  } catch (error) {
    console.error(`Erro ao excluir usuário: ${error.message}`);
    throw new Parse.Error(500, `Erro ao excluir usuário: ${error.message}`);
  }
});

// =============================================================================
// FUNÇÕES DE NOTIFICAÇÕES PUSH
// =============================================================================

// Função centralizada para envio de notificações push
Parse.Cloud.define("enviarNotificacaoPush", async (request) => {
  try {
    const {
      destinatarios,     // Array com IDs dos usuários destinatários
      canais,            // Array com nomes dos canais
      titulo,            // Título da notificação
      mensagem,          // Texto da notificação
      tipo,              // Tipo para processamento no cliente
      dadosAdicionais,   // Dados extras para a notificação
      tipoDestinatario,  // Tipo dos usuários destinatários (patient, doctor, etc.)
      prioridade,        // Prioridade da notificação (high, normal)
      silenciosa = false // Se a notificação deve ser silenciosa (sem som/vibração)
    } = request.params;

    // Validação básica dos parâmetros
    if ((!destinatarios || destinatarios.length === 0) && (!canais || canais.length === 0)) {
      throw new Error("Necessário fornecer destinatários ou canais para enviar notificações");
    }

    if (!titulo || !mensagem) {
      throw new Error("Título e mensagem são obrigatórios");
    }

    console.log(`Enviando notificação: "${titulo}" para ${destinatarios?.length || 0} destinatários / ${canais?.length || 0} canais`);

    // Dados da notificação otimizados para iOS e Android
    const pushData = {
      // Campos comuns
      title: titulo,
      alert: mensagem,
      badge: "Increment",
      tipo: tipo || "geral",

      // Android
      android: {
        notification: {
          title: titulo,
          body: mensagem,
          click_action: "FLUTTER_NOTIFICATION_CLICK",
          priority: prioridade === 'high' ? 'high' : 'normal',
          channel_id: tipo || "default_channel"
        }
      },

      // iOS
      ios: {
        alert: {
          title: titulo,
          body: mensagem
        },
        badge: "Increment",
        sound: silenciosa ? null : "default",
        content_available: silenciosa,
        category: tipo || "DEFAULT_CATEGORY"
      }
    };

    // Garantir compatibilidade com versões anteriores do Parse Server
    pushData.sound = silenciosa ? "" : "default";
    pushData.click_action = "FLUTTER_NOTIFICATION_CLICK";

    // Adicionar dados extras se fornecidos
    if (dadosAdicionais) {
      // Extrair dados para a raiz do objeto
      Object.assign(pushData, dadosAdicionais);

      // Também colocar dentro do campo data para garantir que appsv mais recentes possam acessá-los
      pushData.data = { ...dadosAdicionais, tipo: tipo || "geral" };
    } else {
      pushData.data = { tipo: tipo || "geral" };
    }

    // Marcador de timestamp
    pushData.timestamp = Date.now();

    // Adicionar notificação ao lote para processamento
    try {
      // Preparar a notificação para envio
      let pushPromises = [];

      // Estratégia de envio
      if (canais && canais.length > 0) {
        // Filtrar canais válidos e remover duplicados
        const uniqueChannels = [...new Set(canais.filter(c => c && typeof c === 'string'))];

        if (uniqueChannels.length > 0) {
          // Enviar para canais específicos
          pushPromises.push(Parse.Push.send({
            channels: uniqueChannels,
            data: pushData,
            // Usar essas opções para garantir envio imediato
            push_time: null,
            expiration_time: null
          }, { useMasterKey: true }));

          console.log(`Notificação enviada para ${uniqueChannels.length} canais: ${uniqueChannels.join(', ')}`);
        }
      }

      if (destinatarios && destinatarios.length > 0) {
        // Filtrar IDs de usuário válidos e remover duplicados
        const uniqueDestinatarios = [...new Set(destinatarios.filter(d => d && typeof d === 'string'))];

        if (uniqueDestinatarios.length > 0) {
          // Criar query de instalações para estes destinatários
          const pushQuery = new Parse.Query(Parse.Installation);
          pushQuery.containedIn("userId", uniqueDestinatarios);

          // Se foi especificado um tipo de destinatário, adicionar à query
          if (tipoDestinatario) {
            // Mapear tipos em português para inglês
            const tipoMapeado = tipoDestinatario.toLowerCase();
            let tiposEfetivos = [tipoMapeado];

            if (tipoMapeado === 'medico') tiposEfetivos.push('doctor');
            else if (tipoMapeado === 'doctor') tiposEfetivos.push('medico');
            else if (tipoMapeado === 'paciente') tiposEfetivos.push('patient');
            else if (tipoMapeado === 'patient') tiposEfetivos.push('paciente');
            else if (tipoMapeado === 'secretaria') tiposEfetivos.push('secretary');
            else if (tipoMapeado === 'secretary') tiposEfetivos.push('secretaria');
            else if (tipoMapeado === 'consultorio') tiposEfetivos.push('hospital');
            else if (tipoMapeado === 'hospital') tiposEfetivos.push('consultorio');

            pushQuery.containedIn("userType", tiposEfetivos);
          }

          // Tentar enviar a notificação
          pushPromises.push(Parse.Push.send({
            where: pushQuery,
            data: pushData,
            // Usar essas opções para garantir envio imediato
            push_time: null,
            expiration_time: null
          }, { useMasterKey: true }));

          console.log(`Notificação enviada para ${uniqueDestinatarios.length} usuários ${tipoDestinatario ? `do tipo ${tipoDestinatario}` : ''}`);
        }
      }

      // Aguardar todas as operações de envio
      await Promise.all(pushPromises);
    } catch (pushError) {
      console.error("Erro específico no envio de push:", pushError);
      throw pushError;
    }

    // Registrar a notificação para fins de auditoria
    try {
      const logNotificacao = new Parse.Object("NotificacaoLog");
      logNotificacao.set("titulo", titulo);
      logNotificacao.set("mensagem", mensagem);
      logNotificacao.set("tipo", tipo);
      logNotificacao.set("destinatarios_count", destinatarios?.length || 0);
      logNotificacao.set("canais_count", canais?.length || 0);
      logNotificacao.set("data_envio", new Date());
      logNotificacao.set("sucesso", true);

      // Configurar ACL para todos os logs
      const aclLog = new Parse.ACL();
      aclLog.setPublicReadAccess(true);
      aclLog.setPublicWriteAccess(true);
      logNotificacao.setACL(aclLog);

      await logNotificacao.save(null, { useMasterKey: true });
    } catch (logError) {
      // Apenas registrar o erro, não impedir o fluxo principal
      console.warn("Erro ao registrar log de notificação:", logError.message);
    }

    return {
      success: true,
      message: "Notificação enviada com sucesso",
      timestamp: Date.now()
    };
  } catch (error) {
    console.error("Erro ao enviar notificação push:", error);

    // Registrar falha para análise
    try {
      const logFalha = new Parse.Object("NotificacaoLog");
      logFalha.set("titulo", request.params.titulo || "Desconhecido");
      logFalha.set("erro", error.message);
      logFalha.set("data_erro", new Date());
      logFalha.set("sucesso", false);

      // Erro completo para facilitar debug
      logFalha.set("erro_detalhes", JSON.stringify({
        message: error.message,
        stack: error.stack,
        code: error.code
      }));

      // Configurar ACL para todos os logs
      const aclLog = new Parse.ACL();
      aclLog.setPublicReadAccess(true);
      aclLog.setPublicWriteAccess(true);
      logFalha.setACL(aclLog);

      await logFalha.save(null, { useMasterKey: true });
    } catch (logError) {
      console.error("Erro ao registrar falha de notificação:", logError);
    }

    return {
      success: false,
      error: error.message,
      timestamp: Date.now()
    };
  }
});

// Função para registrar dispositivos para notificações push
Parse.Cloud.define('registerDeviceForPush', async (request) => {
  try {
    const { deviceId, userId, userType, token, deviceType } = request.params;

    if (!deviceId || !token) {
      throw new Error("Parâmetros incompletos: deviceId e token são obrigatórios");
    }

    console.log(`Registrando dispositivo ${deviceId} para notificações push (${userType} ${userId})`);

    // Buscar instalação existente por deviceId ou token
    // Esta abordagem é mais robusta, pois verifica tanto pelo ID do dispositivo quanto pelo token
    const installationQuery = Parse.Query.or(
      new Parse.Query(Parse.Installation).equalTo('installationId', deviceId),
      new Parse.Query(Parse.Installation).equalTo('deviceToken', token)
    );

    let installation = await installationQuery.first({ useMasterKey: true });

    if (!installation) {
      // Criar nova instalação se não existir
      installation = new Parse.Installation();
      installation.set('installationId', deviceId);
      console.log("Criando nova instalação");
    } else {
      console.log(`Atualizando instalação existente: ${installation.id}`);
    }

    // Normalizar e validar o tipo de usuário
    const validUserTypes = ['patient', 'doctor', 'medico', 'secretary', 'secretaria', 'hospital', 'consultorio', 'admin', 'paciente'];
    const normalizedUserType = userType?.toLowerCase() || 'patient';

    let effectiveUserType = normalizedUserType;

    // Mapeamento para tipos padrão se necessário
    if (!validUserTypes.includes(normalizedUserType)) {
      if (['paciente'].includes(normalizedUserType)) {
        effectiveUserType = 'patient';
      } else if (['médico', 'doctor'].includes(normalizedUserType)) {
        effectiveUserType = 'doctor';
      } else if (['secretaria', 'secretary'].includes(normalizedUserType)) {
        effectiveUserType = 'secretary';
      } else if (['consultorio', 'hospital'].includes(normalizedUserType)) {
        effectiveUserType = 'hospital';
      } else {
        console.log(`Tipo de usuário não reconhecido: ${userType}, usando default`);
        effectiveUserType = 'patient'; // Tipo padrão
      }
    }

    // Uniformizar tipos para o padrão em inglês
    if (effectiveUserType === 'medico') effectiveUserType = 'doctor';
    if (effectiveUserType === 'secretaria') effectiveUserType = 'secretary';
    if (effectiveUserType === 'consultorio') effectiveUserType = 'hospital';
    if (effectiveUserType === 'paciente') effectiveUserType = 'patient';

    // Atualizar dados da instalação
    installation.set('deviceToken', token);
    installation.set('deviceType', deviceType || 'android');
    installation.set('userId', userId);
    installation.set('userType', effectiveUserType);
    installation.set('lastUpdated', new Date());

    // Configurar canais de notificação padronizados
    const channels = [];

    // Canal global por tipo de usuário
    const globalChannel = `${effectiveUserType}s`; // patients, doctors, secretaries, hospitals
    channels.push(globalChannel);

    // Canal individual para o usuário
    const userChannel = `${effectiveUserType}_${userId}`;
    channels.push(userChannel);

    // Adicionar canais específicos por função (para compatibilidade com ambos os formatos)
    const userRoles = request.user?.get('roles') || [];
    userRoles.forEach(role => {
      if (role.startsWith('role:')) {
        channels.push(`${role.substring(5)}s`); // Converter role:admin para admins, etc.
      } else {
        channels.push(`${role}s`); // Para roles sem prefixo
      }
    });

    // Adicionar canais baseados no tipo de usuário mapeado (para retrocompatibilidade)
    const tipUsuario = request.user?.get('tipo')?.toLowerCase();
    if (tipUsuario) {
      if (tipUsuario === 'medico' || tipUsuario === 'doctor') {
        channels.push('doctors');
        channels.push('medicos');
      } else if (tipUsuario === 'secretaria' || tipUsuario === 'secretary') {
        channels.push('secretaries');
        channels.push('secretarias');
      } else if (tipUsuario === 'consultorio' || tipUsuario === 'hospital') {
        channels.push('hospitals');
        channels.push('consultorios');
      } else if (tipUsuario === 'paciente' || tipUsuario === 'patient') {
        channels.push('patients');
        channels.push('pacientes');
      }
    }

    // Remover duplicatas
    const uniqueChannels = [...new Set(channels)];
    installation.set('channels', uniqueChannels);

    // Salvar a instalação com masterKey
    await installation.save(null, { useMasterKey: true });
    console.log(`Dispositivo registrado com sucesso para canais: ${uniqueChannels.join(', ')}`);

    // Registrar log de instalação
    try {
      const logInstalacao = new Parse.Object("InstallationLog");
      logInstalacao.set("deviceId", deviceId);
      logInstalacao.set("userId", userId);
      logInstalacao.set("userType", effectiveUserType);
      logInstalacao.set("channels", uniqueChannels);
      logInstalacao.set("data_registro", new Date());

      // Configurar ACL pública para evitar problemas de acesso
      const publicACL = new Parse.ACL();
      publicACL.setPublicReadAccess(true);
      publicACL.setPublicWriteAccess(true);
      logInstalacao.setACL(publicACL);

      await logInstalacao.save(null, { useMasterKey: true });
    } catch (logError) {
      console.warn("Erro ao salvar log de instalação:", logError.message);
      // Não interrompe o fluxo principal
    }

    // Verificar instalação salva para confirmar sucesso
    try {
      const checkQuery = new Parse.Query(Parse.Installation);
      checkQuery.equalTo('installationId', deviceId);
      const savedInstallation = await checkQuery.first({ useMasterKey: true });

      if (!savedInstallation) {
        console.warn("AVISO: Instalação não encontrada após salvamento!");
      } else {
        console.log(`Instalação verificada com sucesso: ${savedInstallation.id}`);
      }
    } catch (checkError) {
      console.warn("Erro ao verificar instalação salva:", checkError.message);
    }

    return {
      success: true,
      message: 'Dispositivo registrado com sucesso',
      channels: uniqueChannels,
      installationId: installation.id
    };
  } catch (error) {
    console.error('Erro ao registrar dispositivo para notificações:', error);
    return { success: false, error: error.message };
  }
});

// =============================================================================
// FUNÇÕES DE GERENCIAMENTO DE DISPOSITIVOS
// =============================================================================

// Função para validar e gerenciar dispositivos únicos
Parse.Cloud.define('validateDeviceRegistration', async (request) => {
  try {
    const { deviceId, deviceInfo } = request.params;

    if (!deviceId) {
      throw new Error("deviceId é obrigatório");
    }

    console.log(`Validando registro do dispositivo: ${deviceId}`);

    // Buscar paciente associado ao dispositivo diretamente
    const pacienteQuery = new Parse.Query("Paciente");
    pacienteQuery.equalTo("deviceId", deviceId);
    const paciente = await pacienteQuery.first({ useMasterKey: true });

    if (!paciente) {
      console.log(`Nenhum paciente encontrado para dispositivo: ${deviceId}`);
      return {
        success: true,
        isRegistered: false,
        hasPatient: false,
        deviceId: deviceId,
        message: "Dispositivo não registrado ou sem paciente associado"
      };
    }

    // Atualizar último acesso do paciente
    paciente.set("ultimoAcesso", new Date());

    // Atualizar deviceInfo se fornecido
    if (deviceInfo) {
      paciente.set("deviceInfo", deviceInfo);
    }

    await paciente.save(null, { useMasterKey: true });

    console.log(`Dispositivo validado com sucesso: ${deviceId}, paciente: ${paciente.id}`);

    return {
      success: true,
      isRegistered: true,
      hasPatient: true,
      deviceId: deviceId,
      existingPatient: {
        objectId: paciente.id,
        userId: paciente.get("userId"),
        nome: paciente.get("nome"),
        telefone: paciente.get("telefone"),
        dataCadastro: paciente.get("dataCadastro"),
        ultimoAcesso: paciente.get("ultimoAcesso"),
        deviceId: paciente.get("deviceId")
      },
      deviceInfo: paciente.get("deviceInfo"),
      message: "Dispositivo e paciente validados com sucesso"
    };

  } catch (error) {
    console.error('Erro ao validar registro do dispositivo:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Função para verificar se um paciente já existe para um dispositivo
Parse.Cloud.define('checkExistingPatientForDevice', async (request) => {
  try {
    const { deviceId } = request.params;

    if (!deviceId) {
      throw new Error("deviceId é obrigatório");
    }

    console.log(`Verificando paciente existente para dispositivo: ${deviceId}`);

    // Buscar paciente associado ao dispositivo
    const pacienteQuery = new Parse.Query("Paciente");
    pacienteQuery.equalTo("deviceId", deviceId);
    const paciente = await pacienteQuery.first({ useMasterKey: true });

    if (!paciente) {
      return {
        success: true,
        hasPatient: false,
        message: "Nenhum paciente encontrado para este dispositivo"
      };
    }

    return {
      success: true,
      hasPatient: true,
      patient: {
        objectId: paciente.id,
        userId: paciente.get("userId"),
        nome: paciente.get("nome"),
        telefone: paciente.get("telefone"),
        dataCadastro: paciente.get("dataCadastro"),
        ultimoAcesso: paciente.get("ultimoAcesso"),
        deviceId: paciente.get("deviceId")
      }
    };

  } catch (error) {
    console.error('Erro ao verificar paciente existente:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Função para associar um paciente a um dispositivo
Parse.Cloud.define('associatePatientToDevice', async (request) => {
  try {
    const { deviceId, userId, nome, telefone, deviceInfo } = request.params;

    if (!deviceId || !userId || !nome || !telefone) {
      throw new Error("Parâmetros obrigatórios: deviceId, userId, nome, telefone");
    }

    console.log(`Associando paciente ${userId} ao dispositivo ${deviceId}`);

    // Limpar telefone - manter apenas números
    const cleanTelefone = telefone.replace(/[^0-9]/g, '');

    // Prioridade 1: Verificar se já existe um paciente para este dispositivo
    const deviceQuery = new Parse.Query("Paciente");
    deviceQuery.equalTo("deviceId", deviceId);
    let paciente = await deviceQuery.first({ useMasterKey: true });

    if (paciente) {
      // Atualizar paciente existente do dispositivo
      console.log(`Atualizando paciente existente do dispositivo: ${paciente.id}`);
      paciente.set("userId", userId);
      paciente.set("nome", nome);
      paciente.set("telefone", cleanTelefone);
      paciente.set("ultimoAcesso", new Date());

      if (deviceInfo) {
        paciente.set("deviceInfo", deviceInfo);
      }
    } else {
      // Prioridade 2: Verificar se já existe um paciente com este userId
      const userQuery = new Parse.Query("Paciente");
      userQuery.equalTo("userId", userId);
      paciente = await userQuery.first({ useMasterKey: true });

      if (paciente) {
        // Atualizar paciente existente com novo deviceId
        console.log(`Atualizando paciente existente com novo dispositivo: ${paciente.id}`);
        paciente.set("deviceId", deviceId);
        paciente.set("nome", nome);
        paciente.set("telefone", cleanTelefone);
        paciente.set("ultimoAcesso", new Date());

        if (deviceInfo) {
          paciente.set("deviceInfo", deviceInfo);
        }
      } else {
        // Prioridade 3: Verificar se já existe paciente com mesmo telefone
        const telefoneQuery = new Parse.Query("Paciente");
        telefoneQuery.equalTo("telefone", cleanTelefone);
        paciente = await telefoneQuery.first({ useMasterKey: true });

        if (paciente) {
          // Atualizar paciente existente baseado no telefone
          console.log(`Atualizando paciente existente baseado no telefone: ${paciente.id}`);
          paciente.set("deviceId", deviceId);
          paciente.set("userId", userId);
          paciente.set("nome", nome);
          paciente.set("ultimoAcesso", new Date());

          if (deviceInfo) {
            paciente.set("deviceInfo", deviceInfo);
          }
        } else {
          // Criar novo paciente
          console.log(`Criando novo paciente: ${userId}`);
          paciente = new Parse.Object("Paciente");
          paciente.set("userId", userId);
          paciente.set("deviceId", deviceId);
          paciente.set("nome", nome);
          paciente.set("telefone", cleanTelefone);
          paciente.set("dataCadastro", new Date());
          paciente.set("ultimoAcesso", new Date());
          paciente.set("em_fila", false);

          if (deviceInfo) {
            paciente.set("deviceInfo", deviceInfo);
          }
        }
      }
    }

    // Definir ACL pública
    const acl = new Parse.ACL();
    acl.setPublicReadAccess(true);
    acl.setPublicWriteAccess(true);
    paciente.setACL(acl);

    // Salvar paciente
    await paciente.save(null, { useMasterKey: true });

    console.log(`Paciente associado ao dispositivo com sucesso: ${paciente.id}`);

    return {
      success: true,
      patient: {
        objectId: paciente.id,
        userId: paciente.get("userId"),
        nome: paciente.get("nome"),
        telefone: paciente.get("telefone"),
        deviceId: paciente.get("deviceId"),
        dataCadastro: paciente.get("dataCadastro"),
        ultimoAcesso: paciente.get("ultimoAcesso")
      },
      message: "Paciente associado ao dispositivo com sucesso"
    };

  } catch (error) {
    console.error('Erro ao associar paciente ao dispositivo:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// =============================================================================
// TRIGGERS DE NOTIFICAÇÕES (AFTERAVE, AFTERDELETE)
// =============================================================================

// Send notification when position in queue changes
Parse.Cloud.afterSave('Fila', async (request) => {
  const filaObject = request.object;

  // Skip if this isn't an update or if it's a new object
  if (!request.original) return;

  const posicaoAtual = filaObject.get('posicao');
  const posicaoAnterior = request.original.get('posicao');
  const idPaciente = filaObject.get('idPaciente');
  const status = filaObject.get('status');
  const statusAnterior = request.original.get('status');

  try {
    console.log(`Processando alterações na fila: paciente ${idPaciente}, posição ${posicaoAnterior} -> ${posicaoAtual}, status ${statusAnterior} -> ${status}`);

    // Case 1: Position changed in queue
    if (posicaoAnterior && posicaoAtual !== posicaoAnterior) {
      // Usar função centralizada para enviar notificação
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: 'Atualização na fila',
        mensagem: `Sua posição na fila mudou de ${posicaoAnterior} para ${posicaoAtual}.`,
        tipo: 'posicao_alterada',
        dadosAdicionais: {
          badge: 1,
          sound: 'default',
          category: 'POSITION_CHANGE',
          fila_id: filaObject.id
        }
      }, { useMasterKey: true });

      console.log(`Notificação enviada: posição alterada de ${posicaoAnterior} para ${posicaoAtual}`);
    }

    // Case 2: About to be called (position <= 3)
    if (posicaoAtual <= 3 && posicaoAnterior > 3) {
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: 'Quase sua vez!',
        mensagem: `Sua posição na fila agora é ${posicaoAtual}. Prepare-se para ser atendido!`,
        tipo: 'quase_sua_vez',
        dadosAdicionais: {
          badge: 1,
          sound: 'default',
          category: 'ALMOST_TURN',
          fila_id: filaObject.id
        }
      }, { useMasterKey: true });

      console.log(`Notificação enviada: quase sua vez (posição ${posicaoAtual})`);
    }

    // Case 3: Status changed to 'em_atendimento'
    if (status === 'em_atendimento' && statusAnterior !== 'em_atendimento') {
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: 'Sua vez chegou!',
        mensagem: 'Você está sendo chamado para atendimento agora.',
        tipo: 'chamado_atendimento',
        dadosAdicionais: {
          badge: 1,
          sound: 'default',
          category: 'BEING_CALLED',
          fila_id: filaObject.id
        }
      }, { useMasterKey: true });

      console.log(`Notificação enviada: paciente chamado para atendimento`);
    }

    // Case 4: Status changed to 'atendido'
    if (status === 'atendido' && statusAnterior !== 'atendido') {
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: 'Atendimento finalizado',
        mensagem: 'Seu atendimento foi finalizado. Obrigado por sua visita!',
        tipo: 'atendimento_finalizado',
        dadosAdicionais: {
          badge: 1,
          sound: 'default',
          category: 'COMPLETED',
          fila_id: filaObject.id
        }
      }, { useMasterKey: true });

      console.log(`Notificação enviada: atendimento finalizado`);
    }
  } catch (error) {
    console.error('Error sending push notification:', error);
  }
});

// Send notification when a patient enters the queue
Parse.Cloud.afterSave('FilaSolicitacao', async (request) => {
  const solicitacao = request.object;

  // Only run for new objects with status 'pendente'
  if (request.object.existed() || solicitacao.get('status') !== 'pendente') return;

  try {
    // Get doctor and hospital IDs
    const medicoId = solicitacao.get('medicoId')?.id;
    const hospitalId = solicitacao.get('hospitalId')?.id;

    if (!medicoId || !hospitalId) return;

    console.log(`Nova solicitação de fila: médico ${medicoId}, hospital ${hospitalId}`);

    // Notify doctors and secretaries of the hospital using the centralized function
    await Parse.Cloud.run("enviarNotificacaoPush", {
      canais: [`doctor_${medicoId}`, `hospital_${hospitalId}`],
      titulo: 'Nova solicitação',
      mensagem: 'Um paciente solicitou entrada na fila de atendimento.',
      tipo: 'nova_solicitacao',
      dadosAdicionais: {
        badge: 1,
        sound: 'default',
        category: 'NEW_REQUEST',
        solicitacao_id: solicitacao.id
      }
    }, { useMasterKey: true });

    console.log(`Notificação enviada: nova solicitação de fila`);
  } catch (error) {
    console.error('Error sending push notification:', error);
  }
});

// Send notification when a solicitation is approved or rejected
Parse.Cloud.afterSave('Notificacao', async (request) => {
  const notificacao = request.object;

  // Only run for new notifications
  if (request.object.existed()) return;

  try {
    const tipo = notificacao.get('tipo');
    const solicitacaoId = notificacao.get('solicitacao_id');

    if (!solicitacaoId) return;

    // Handle both object pointer and string ID formats
    const solicitacaoIdValue = typeof solicitacaoId === 'object' ? solicitacaoId.id : solicitacaoId;

    if (!solicitacaoIdValue) return;

    console.log(`Nova notificação: tipo ${tipo}, solicitação ${solicitacaoIdValue}`);

    // Find the solicitation to get patient ID
    const solicitacaoQuery = new Parse.Query('FilaSolicitacao');
    solicitacaoQuery.equalTo('objectId', solicitacaoIdValue);
    const solicitacao = await solicitacaoQuery.first({ useMasterKey: true });

    if (!solicitacao) return;

    const idPaciente = solicitacao.get('idPaciente');
    if (!idPaciente) return;

    if (tipo === 'entrada_fila') {
      // Notification for approved request
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: 'Solicitação aprovada',
        mensagem: 'Sua solicitação de atendimento foi aprovada. Você está na fila!',
        tipo: 'solicitacao_aprovada',
        dadosAdicionais: {
          badge: 1,
          sound: 'default',
          category: 'REQUEST_APPROVED',
          notificacao_id: notificacao.id
        }
      }, { useMasterKey: true });

      console.log(`Notificação enviada: solicitação aprovada para paciente ${idPaciente}`);
    } else if (tipo === 'cancelamento') {
      // Notification for rejected request
      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: 'Solicitação recusada',
        mensagem: 'Sua solicitação de atendimento foi recusada.',
        tipo: 'solicitacao_recusada',
        dadosAdicionais: {
          badge: 1,
          sound: 'default',
          category: 'REQUEST_REJECTED',
          notificacao_id: notificacao.id
        }
      }, { useMasterKey: true });

      console.log(`Notificação enviada: solicitação recusada para paciente ${idPaciente}`);
    }
  } catch (error) {
    console.error('Error sending push notification:', error);
  }
});

// Send notification when a message is broadcasted to a queue
Parse.Cloud.afterSave('MensagemFila', async (request) => {
  const mensagem = request.object;

  // Only run for new messages
  if (request.object.existed()) return;

  try {
    const medicoId = mensagem.get('medico_id');
    const consultorioId = mensagem.get('consultorio_id');
    const mensagemTexto = mensagem.get('mensagem') || mensagem.get('texto');

    // Handle different formats of object references
    const medicoIdValue = typeof medicoId === 'object' ? medicoId.id : medicoId;
    const consultorioIdValue = typeof consultorioId === 'object' ? consultorioId.id : consultorioId;

    if (!medicoIdValue || !consultorioIdValue || !mensagemTexto) return;

    console.log(`Nova mensagem para a fila: médico ${medicoIdValue}, consultório ${consultorioIdValue}`);

    // Get all patients in this doctor's queue
    const filaQuery = new Parse.Query('Fila');
    filaQuery.equalTo('medico', Parse.Object.createWithoutData('Medico', medicoIdValue));
    filaQuery.equalTo('consultorio', Parse.Object.createWithoutData('consultorio', consultorioIdValue));
    filaQuery.equalTo('status', 'aguardando');

    const filaResults = await filaQuery.find({ useMasterKey: true });
    console.log(`Encontrados ${filaResults.length} pacientes na fila para enviar mensagem`);

    // Send message to all patients in this queue
    for (const fila of filaResults) {
      const idPaciente = fila.get('idPaciente');
      if (!idPaciente) continue;

      await Parse.Cloud.run("enviarNotificacaoPush", {
        canais: [`patient_${idPaciente}`],
        titulo: mensagem.get('titulo') || 'Mensagem do consultório',
        mensagem: mensagemTexto,
        tipo: 'mensagem_fila',
        dadosAdicionais: {
          badge: 1,
          sound: 'default',
          category: 'QUEUE_MESSAGE',
          mensagem_id: mensagem.id,
          fila_id: fila.id
        }
      }, { useMasterKey: true });
    }
    console.log(`Mensagem enviada para ${filaResults.length} pacientes`);
  } catch (error) {
    console.error('Error sending push notification:', error);
  }
});

// =============================================================================
// FUNÇÕES DE GERENCIAMENTO DE FILAS
// =============================================================================

// A função "adicionarPacienteNaFila" já está definida anteriormente no código (linha ~92)
// Evite duplicações de funções Cloud

// Função para atualizar o status do paciente na fila do paciente
Parse.Cloud.define("atualizarStatusPacienteFila", async (request) => {
  try {
    const { filaId, status, pacienteId } = request.params;

    if (!filaId || !status || !pacienteId) {
      throw new Error("Parâmetros obrigatórios não fornecidos");
    }

    console.log(`Atualizando status de paciente ${pacienteId} na fila ${filaId} para ${status}`);

    // Buscar a entrada na fila
    const filaQuery = new Parse.Query("Fila");
    const fila = await filaQuery.get(filaId, { useMasterKey: true });

    if (!fila) {
      throw new Error("Entrada na fila não encontrada");
    }

    const idPacienteFila = fila.get("idPaciente");

    // Verificar se o paciente é o mesmo que está solicitando a atualização
    if (idPacienteFila !== pacienteId) {
      throw new Error("Paciente não autorizado a atualizar esta fila");
    }

    // Atualizar status
    const statusAnterior = fila.get("status");
    fila.set("status", status);

    // Se cancelou, registrar data de saída
    if (status === "cancelado") {
      fila.set("data_saida", new Date());
    }

    await fila.save(null, { useMasterKey: true });

    // Se cancelou ou finalizou, atualizar o status do paciente em ambas as classes
    if (status === "cancelado" || status === "atendido" || status === "ausente") {
      // 1. Atualizar na classe Usuario (nova implementação)
      const usuarioQuery = new Parse.Query("Usuario");
      usuarioQuery.equalTo("deviceId", pacienteId);
      const usuario = await usuarioQuery.first({ useMasterKey: true });

      if (usuario) {
        usuario.set("em_fila", false);
        usuario.set("ultima_fila", new Date());
        await usuario.save(null, { useMasterKey: true });
        console.log(`Atualizado status do usuário ${pacienteId} na classe Usuario para não estar em fila`);
      } else {
        console.log(`Usuário ${pacienteId} não encontrado na classe Usuario`);
      }

      // 2. Retrocompatibilidade: atualizar na classe Paciente
      const pacienteQuery = new Parse.Query("Paciente");
      pacienteQuery.equalTo("userId", pacienteId);
      const paciente = await pacienteQuery.first({ useMasterKey: true });

      if (paciente) {
        paciente.set("em_fila", false);
        paciente.set("ultima_fila", new Date());
        await paciente.save(null, { useMasterKey: true });
        console.log(`Atualizado status do paciente ${pacienteId} na classe Paciente para não estar em fila`);
      } else {
        console.log(`Paciente ${pacienteId} não encontrado na classe Paciente`);
      }
    }

    console.log(`Fila ${filaId} atualizada com sucesso de ${statusAnterior} para ${status}`);

    return {
      success: true,
      message: `Status atualizado com sucesso para ${status}`
    };
  } catch (error) {
    console.error("Erro ao atualizar status do paciente na fila:", error);
    throw new Error(`Erro ao atualizar status: ${error.message}`);
  }
});

// Função para processar cancelamento de solicitação pelo paciente
Parse.Cloud.define("cancelarSolicitacaoPaciente", async (request) => {
  try {
    const { solicitacaoId, deviceId } = request.params;

    if (!solicitacaoId) {
      throw new Error("ID da solicitação não fornecido");
    }

    console.log(`Processando cancelamento de solicitação pelo paciente: ${solicitacaoId}`);

    // Buscar a solicitação
    const solicitacaoQuery = new Parse.Query("FilaSolicitacao");
    solicitacaoQuery.equalTo("solicitacao_id", solicitacaoId);

    if (deviceId) {
      solicitacaoQuery.equalTo("idPaciente", deviceId);
    }

    const solicitacao = await solicitacaoQuery.first({ useMasterKey: true });

    if (!solicitacao) {
      throw new Error("Solicitação não encontrada ou não pertence a este paciente");
    }

    // Verificar se a solicitação ainda está pendente
    const status = solicitacao.get("status");
    if (status !== "pendente") {
      throw new Error(`Solicitação não pode ser cancelada pois está com status ${status}`);
    }

    // Registrar o cancelamento
    const cancelamento = new Parse.Object("SolicitacaoCancelamento");
    cancelamento.set("solicitacao_id", solicitacao.get("solicitacao_id"));
    cancelamento.set("motivo", "cancelado_paciente");
    cancelamento.set("data_cancelamento", new Date());
    await cancelamento.save(null, { useMasterKey: true });

    // Atualizar status da solicitação
    solicitacao.set("status", "cancelado");
    await solicitacao.save(null, { useMasterKey: true });

    console.log(`Solicitação ${solicitacaoId} cancelada com sucesso pelo paciente`);

    return {
      success: true,
      message: "Solicitação cancelada com sucesso"
    };
  } catch (error) {
    console.error("Erro ao cancelar solicitação pelo paciente:", error);
    throw new Error(`Erro ao cancelar solicitação: ${error.message}`);
  }
});

// Função para enviar feedback
Parse.Cloud.define("enviarFeedback", async (request) => {
  try {
    const { solicitacaoId, feedback } = request.params;

    if (!solicitacaoId || !feedback) {
      throw new Error("Parâmetros obrigatórios não fornecidos");
    }

    console.log(`Enviando feedback para solicitação: ${solicitacaoId}`);

    // Buscar a solicitação
    const solicitacaoQuery = new Parse.Query("FilaSolicitacao");
    solicitacaoQuery.equalTo("solicitacao_id", solicitacaoId);
    const solicitacao = await solicitacaoQuery.first({ useMasterKey: true });

    if (!solicitacao) {
      throw new Error("Solicitação não encontrada");
    }

    // Adicionar feedback à solicitação
    solicitacao.set("feedback", feedback);
    await solicitacao.save(null, { useMasterKey: true });

    console.log(`Feedback enviado com sucesso para solicitação ${solicitacaoId}`);

    return {
      success: true,
      message: "Feedback enviado com sucesso"
    };
  } catch (error) {
    console.error("Erro ao enviar feedback:", error);
    throw new Error(`Erro ao enviar feedback: ${error.message}`);
  }
});

// Função para verificar e atualizar permissões das Cloud Functions
Parse.Cloud.define("verificarEAtualizarPermissoes", async (request) => {
  // Verificar se o usuário atual é administrador
  const currentUser = request.user;
  if (!currentUser) {
    throw new Parse.Error(Parse.Error.OPERATION_FORBIDDEN, "Usuário não autenticado");
  }

  // Lista de roles de admin que podem executar esta função
  const adminRoles = ["admin", "superadmin", "desenvolvedor"];

  // Verifique se o usuário tem alguma role de admin
  const userRolesQuery = new Parse.Query(Parse.Role);
  userRolesQuery.equalTo("users", currentUser);
  const userRoles = await userRolesQuery.find({ useMasterKey: true });

  const isAdmin = userRoles.some(role => adminRoles.includes(role.get("name")));

  if (!isAdmin) {
    throw new Parse.Error(Parse.Error.OPERATION_FORBIDDEN, "Apenas administradores podem atualizar permissões");
  }

  console.log("Iniciando verificação e atualização de permissões...");

  // Lista de funções que devem ter permissão ampliada
  const funcoes = [
    "adicionarPacienteNaFila",
    "recusarSolicitacao",
    "gerarQRCodeMedico"
  ];

  // Buscar e atualizar as definições de Cloud Functions
  const cloudFunctionsQuery = new Parse.Query("_Hooks");
  cloudFunctionsQuery.containedIn("functionName", funcoes);
  const cloudFunctions = await cloudFunctionsQuery.find({ useMasterKey: true });

  console.log(`Encontradas ${cloudFunctions.length} funções para atualizar`);

  // Atualizar permissões
  const atualizacoes = [];
  for (const cf of cloudFunctions) {
    const functionName = cf.get("functionName");
    console.log(`Atualizando permissões para função: ${functionName}`);

    // Definir permissões para que qualquer usuário autenticado possa executar
    cf.set("requireUser", true);  // Requer autenticação
    cf.set("requireMaster", false); // Não requer masterKey
    cf.set("validateMasterKey", false); // Não valida masterKey

    atualizacoes.push(cf.save(null, { useMasterKey: true }));
  }

  await Promise.all(atualizacoes);

  return {
    success: true,
    message: `Permissões atualizadas para ${atualizacoes.length} funções`,
    funcoes: funcoes
  };
});

// Gerar QR Code para médico
Parse.Cloud.define("gerarQRCodeMedico", async (request) => {
  try {
    // Verificar autenticação do usuário
    const currentUser = request.user;
    if (!currentUser) {
      throw new Parse.Error(Parse.Error.OPERATION_FORBIDDEN, "Usuário não autenticado");
    }

    console.log(`Gerando QR Code - Usuário: ${currentUser.id}, Username: ${currentUser.get("username")}`);

    // Log dos parâmetros recebidos
    console.log(`Parâmetros recebidos: ${JSON.stringify(request.params)}`);

    // Extrair parâmetros
    const medicoId = request.params.medicoId;
    const consultorioId = request.params.consultorioId;

    if (!medicoId || !consultorioId) {
      throw new Parse.Error(Parse.Error.INVALID_QUERY, "Parâmetros inválidos: medicoId e consultorioId são obrigatórios");
    }

    // Buscar informações do médico - usar masterKey para evitar problemas de permissão
    const medicoQuery = new Parse.Query("Medico");
    medicoQuery.include("especialidade");
    medicoQuery.get(medicoId, { useMasterKey: true }).then(async (medico) => {
      console.log(`Médico encontrado: ${medico.id}, Nome: ${medico.get("nome")}`);

      // Buscar informações do consultório
      const consultorioQuery = new Parse.Query("consultorio");
      const consultorio = await consultorioQuery.get(consultorioId, { useMasterKey: true });
      console.log(`Consultório encontrado: ${consultorio.id}, Nome: ${consultorio.get("nome")}`);

      // Invalidar QR codes antigos
      const qrCodesQuery = new Parse.Query("QRCodeGerado");
      qrCodesQuery.equalTo("medico_id", medico);
      qrCodesQuery.equalTo("consultorio_id", consultorio);
      qrCodesQuery.equalTo("valido", true);

      const qrCodesAntigos = await qrCodesQuery.find({ useMasterKey: true });
      console.log(`QR Codes antigos encontrados: ${qrCodesAntigos.length}`);

      for (const qrCode of qrCodesAntigos) {
        qrCode.set("valido", false);
        await qrCode.save(null, { useMasterKey: true });
      }

      // Gerar novo ID para o QR Code
      const agora = new Date();
      const dataFormatada = `${agora.getFullYear()}${(agora.getMonth() + 1).toString().padStart(2, '0')}${agora.getDate().toString().padStart(2, '0')}_${agora.getHours().toString().padStart(2, '0')}${agora.getMinutes().toString().padStart(2, '0')}`;
      const qrId = `${dataFormatada}_1`;

      // Dados do QR Code
      const qrData = {
        qrId: qrId,
        medicoId: medico.id,
        medicoNome: medico.get("nome"),
        especialidade: medico.get("especialidade") ? medico.get("especialidade").get("nome") : "Não especificada",
        hospitalId: consultorio.id,
        hospitalNome: consultorio.get("nome"),
        timestamp: Date.now()
      };

      console.log(`Dados do QR gerado: ${JSON.stringify(qrData)}`);

      // Criar o QR Code no banco
      const novoQRCode = new Parse.Object("QRCodeGerado");
      novoQRCode.set("qr_id", qrId);
      novoQRCode.set("medico_id", medico);
      novoQRCode.set("consultorio_id", consultorio);
      novoQRCode.set("valido", true);
      novoQRCode.set("impresso", false);
      novoQRCode.set("data_criacao", new Date());
      novoQRCode.set("data_expiracao", new Date(new Date().setDate(new Date().getDate() + 30)));
      novoQRCode.set("dados", qrData);

      // Configurar ACL público - importante para evitar problemas de permissão
      const acl = new Parse.ACL();
      acl.setPublicReadAccess(true);
      acl.setPublicWriteAccess(true); // Garantir que todos possam modificar o QR Code
      novoQRCode.setACL(acl);

      try {
        await novoQRCode.save(null, { useMasterKey: true });
        console.log(`QR Code salvo com sucesso: ${qrId}`);

        // Registrar log de criação
        const qrLog = new Parse.Object("QRCodeLog");
        qrLog.set("action", "criacao");
        qrLog.set("qr_code", novoQRCode);
        qrLog.set("details", "QR Code gerado via Cloud Function");
        qrLog.set("user_id", currentUser);
        qrLog.set("timestamp", new Date());
        qrLog.setACL(acl);
        await qrLog.save(null, { useMasterKey: true });

        return {
          success: true,
          qrId: qrId,
          qrData: qrData,
          message: "QR Code gerado com sucesso"
        };
      } catch (saveError) {
        console.error(`Erro ao salvar QR Code: ${saveError.message}`);
        throw new Parse.Error(Parse.Error.INTERNAL_SERVER_ERROR, `Erro ao salvar QR Code: ${saveError.message}`);
      }
    }).catch(error => {
      console.error(`Erro ao buscar médico: ${error.message}`);
      throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, `Médico não encontrado: ${error.message}`);
    });
  } catch (error) {
    console.error(`Erro ao gerar QR Code: ${error.message}`);
    throw new Parse.Error(Parse.Error.INTERNAL_SERVER_ERROR, `Erro ao gerar QR Code: ${error.message}`);
  }
});

// Função simplificada para geração de QR Code (alternativa com menos restrições)
Parse.Cloud.define("gerarQRCodeSimplificado", async (request) => {
  try {
    const { medicoId, consultorioId } = request.params;

    // Validar parâmetros
    if (!medicoId || !consultorioId) {
      throw new Parse.Error(Parse.Error.INVALID_QUERY, "Parâmetros inválidos: medicoId e consultorioId são obrigatórios");
    }

    console.log(`[SIMPLIFICADO] Gerando QR Code para médico ${medicoId} e consultório ${consultorioId}`);

    // Gerar ID único e sequencial simples
    const timestamp = Date.now();
    const randomPart = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    const qrId = `QR-${timestamp}-${randomPart}`;
    const sequencial = `${new Date().getFullYear()}${(new Date().getMonth() + 1).toString().padStart(2, '0')}${new Date().getDate().toString().padStart(2, '0')}_001`;

    // Buscar dados do médico e consultório usando o masterKey
    console.log(`[SIMPLIFICADO] Buscando dados do médico e consultório`);
    let medicoNome = "Médico", especialidade = "Especialidade", hospitalNome = "Consultório";

    try {
      const medico = await new Parse.Query("Medico").get(medicoId, { useMasterKey: true });
      medicoNome = medico.get("nome") || "Médico";
      especialidade = medico.get("especialidade") ? medico.get("especialidade").get("nome") : "Especialidade";
    } catch (e) {
      console.log(`[SIMPLIFICADO] Aviso: Erro ao buscar médico: ${e.message}`);
    }

    try {
      const consultorio = await new Parse.Query("consultorio").get(consultorioId, { useMasterKey: true });
      hospitalNome = consultorio.get("nome") || "Consultório";
    } catch (e) {
      console.log(`[SIMPLIFICADO] Aviso: Erro ao buscar consultório: ${e.message}`);
    }

    // Preparar dados do QR Code
    const qrData = {
      qrId: qrId,
      medicoId: medicoId,
      medicoNome: medicoNome,
      especialidade: especialidade,
      hospitalId: consultorioId,
      hospitalNome: hospitalNome,
      timestamp: timestamp,
      version: "2.0"
    };

    console.log(`[SIMPLIFICADO] Dados do QR Code: ${JSON.stringify(qrData)}`);

    // Criar objeto QR Code
    const qrCodeGerado = new Parse.Object("QRCodeGerado");
    qrCodeGerado.set("qr_id", qrId);
    qrCodeGerado.set("medico_id", { __type: "Pointer", className: "Medico", objectId: medicoId });
    qrCodeGerado.set("consultorio_id", { __type: "Pointer", className: "consultorio", objectId: consultorioId });
    qrCodeGerado.set("sequencial", sequencial);
    qrCodeGerado.set("valido", true);
    qrCodeGerado.set("impresso", false);
    qrCodeGerado.set("data_criacao", new Date());
    qrCodeGerado.set("data_expiracao", new Date(new Date().setDate(new Date().getDate() + 30)));
    qrCodeGerado.set("versao", "2.0");
    qrCodeGerado.set("status", "ativo");
    qrCodeGerado.set("dados", qrData);

    // Configurar ACL público
    const acl = new Parse.ACL();
    acl.setPublicReadAccess(true);
    acl.setPublicWriteAccess(true);
    qrCodeGerado.setACL(acl);

    console.log(`[SIMPLIFICADO] Salvando QR Code`);
    const resultado = await qrCodeGerado.save(null, { useMasterKey: true });
    console.log(`[SIMPLIFICADO] QR Code salvo com sucesso: ID=${resultado.id}`);

    // Configurar para permitir impressão e leitura
    try {
      const qrLog = new Parse.Object("QRCodeLog");
      qrLog.set("action", "criacao_simplificada");
      qrLog.set("qr_code", qrCodeGerado);
      qrLog.set("details", "QR Code gerado via função simplificada");
      qrLog.set("timestamp", new Date());
      qrLog.setACL(acl);
      await qrLog.save(null, { useMasterKey: true });
    } catch (logError) {
      console.log(`[SIMPLIFICADO] Aviso: Erro ao criar log: ${logError.message}`);
    }

    return {
      success: true,
      qrId: qrId,
      qrData: qrData,
      objectId: resultado.id,
      message: "QR Code gerado com sucesso"
    };
  } catch (error) {
    console.error(`[SIMPLIFICADO] Erro ao gerar QR Code: ${error.message}`);
    throw new Parse.Error(Parse.Error.INTERNAL_SERVER_ERROR, `Erro ao gerar QR Code: ${error.message}`);
  }
});

// Hook para garantir permissões para a classe QRCodeGerado
Parse.Cloud.beforeSave("QRCodeGerado", async (request) => {
  // Permitir qualquer operação em QRCodeGerado para qualquer usuário logado
  console.log("Permitindo operação em QRCodeGerado");
  return;
});

// Função para garantir um único registro por deviceId
Parse.Cloud.define("garantirUnicoUsuario", async (request) => {
  try {
    const { deviceId, nome, telefone } = request.params;

    console.log(`[DEBUG] Iniciando garantirUnicoUsuario com params:`, request.params);
    console.log(`[DEBUG] DeviceId: ${deviceId}, Nome: ${nome}, Telefone: ${telefone}`);

    if (!deviceId) {
      throw new Error("deviceId é obrigatório");
    }

    console.log(`Garantindo usuário único para deviceId: ${deviceId}`);

    // Buscar se já existe usuário com este deviceId
    const usuarioQuery = new Parse.Query("Usuario");
    usuarioQuery.equalTo("deviceId", deviceId);

    console.log(`[DEBUG] Iniciando busca por usuário com deviceId ${deviceId}`);
    const usuario = await usuarioQuery.first({ useMasterKey: true });
    console.log(`[DEBUG] Resultado da busca:`, usuario ? "Usuário encontrado" : "Usuário não encontrado");

    let result;

    if (usuario) {
      // Atualizar usuário existente
      console.log(`Usuário existente encontrado (${usuario.id}). Atualizando dados...`);

      if (nome) usuario.set("nome", nome);
      if (telefone) usuario.set("telefone", telefone);

      usuario.set("ultima_atualizacao", new Date());
      usuario.set("ultimoAcesso", new Date());

      console.log(`[DEBUG] Tentando salvar usuário existente ${usuario.id}`);
      try {
        result = await usuario.save(null, { useMasterKey: true });
        console.log(`[DEBUG] Usuário ${usuario.id} atualizado com sucesso`);
      } catch (saveError) {
        console.error(`[DEBUG] Erro ao salvar usuário existente:`, saveError);
        throw saveError;
      }

      return {
        success: true,
        message: "Usuário atualizado com sucesso",
        usuario: {
          objectId: result.id,
          deviceId: deviceId,
          isNew: false
        }
      };
    } else {
      // Criar novo usuário
      console.log(`Nenhum usuário encontrado com deviceId ${deviceId}. Criando novo...`);

      const novoUsuario = new Parse.Object("Usuario");
      novoUsuario.set("deviceId", deviceId);
      if (nome) novoUsuario.set("nome", nome);
      if (telefone) novoUsuario.set("telefone", telefone);
      novoUsuario.set("data_cadastro", new Date());
      novoUsuario.set("ultima_atualizacao", new Date());
      novoUsuario.set("ultimoAcesso", new Date());
      novoUsuario.set("em_fila", false);

      // Configurar ACL para o novo usuário
      const acl = new Parse.ACL();
      acl.setPublicReadAccess(true);
      acl.setPublicWriteAccess(true);
      novoUsuario.setACL(acl);

      console.log(`[DEBUG] Tentando salvar novo usuário com deviceId ${deviceId}`);
      try {
        result = await novoUsuario.save(null, { useMasterKey: true });
        console.log(`[DEBUG] Novo usuário criado com sucesso: ${result.id}`);
      } catch (saveError) {
        console.error(`[DEBUG] Erro ao criar novo usuário:`, saveError);
        throw saveError;
      }

      return {
        success: true,
        message: "Novo usuário criado com sucesso",
        usuario: {
          objectId: result.id,
          deviceId: deviceId,
          isNew: true
        }
      };
    }
  } catch (error) {
    console.error("Erro ao garantir usuário único:", error);
    throw new Error(`Erro ao processar usuário: ${error.message}`);
  }
});

// Função simplificada para criar ou atualizar um usuário
Parse.Cloud.define("criarOuAtualizarUsuario", async (request) => {
  try {
    const { deviceId, nome, telefone } = request.params;

    if (!deviceId) {
      throw new Error("deviceId é obrigatório");
    }

    console.log(`Criando ou atualizando usuário para deviceId: ${deviceId}`);

    // Buscar usuário existente
    const usuarioQuery = new Parse.Query("Usuario");
    usuarioQuery.equalTo("deviceId", deviceId);
    let usuario = await usuarioQuery.first({ useMasterKey: true });

    if (!usuario) {
      // Criar um novo usuário
      usuario = new Parse.Object("Usuario");
      usuario.set("deviceId", deviceId);
      usuario.set("data_cadastro", new Date());
      usuario.set("em_fila", false);

      // Configurar ACL
      const acl = new Parse.ACL();
      acl.setPublicReadAccess(true);
      acl.setPublicWriteAccess(true);
      usuario.setACL(acl);
    }

    // Atualizar campos
    if (nome) usuario.set("nome", nome);
    if (telefone) usuario.set("telefone", telefone);
    usuario.set("ultima_atualizacao", new Date());
    usuario.set("ultimoAcesso", new Date());

    // Salvar
    await usuario.save(null, { useMasterKey: true });

    return {
      success: true,
      objectId: usuario.id
    };
  } catch (error) {
    console.error("Erro em criarOuAtualizarUsuario:", error);
    throw new Error(`Falha ao processar: ${error.message}`);
  }
});

// Função de teste simples para verificar a comunicação
Parse.Cloud.define("testHello", async (request) => {
  console.log("Teste de Cloud Function - Hello foi chamada");
  return {
    success: true,
    message: "Hello from Cloud Function!"
  };
});

// =============================================================================
// HOOKS PARA LIVEQUERY E WEBHOOKS
// =============================================================================

// ✅ TRIGGER DUPLICADO REMOVIDO - CONSOLIDADO ACIMA

// Hook que é acionado após excluir um objeto da classe Fila
Parse.Cloud.afterDelete("Fila", async (request) => {
  const fila = request.object;

  try {
    console.log(`Fila excluída: ${fila.id}`);

    // Acionar o webhook para processar a exclusão
    const webhookParams = {
      key: '7SfDLUbeOXtCpLa09JpTcGaGjYeMiDyXlNg2uOU1',
      event: 'delete',
      className: 'Fila',
      objectId: fila.id
    };

    // Chamar o webhook de forma assíncrona
    Parse.Cloud.run("webhook", webhookParams, { useMasterKey: true })
      .catch(error => console.error(`Erro ao acionar webhook para exclusão de Fila ${fila.id}: ${error.message}`));

  } catch (error) {
    console.error(`Erro no afterDelete de Fila: ${error.message}`);
  }
});

// Hook que é acionado após salvar um objeto da classe MensagemFila
Parse.Cloud.afterSave("MensagemFila", async (request) => {
  const mensagem = request.object;

  try {
    // Verificar se o objeto foi criado ou atualizado
    const isNew = !request.original;

    console.log(`MensagemFila ${isNew ? 'criada' : 'atualizada'}: ${mensagem.id}`);

    // Se for uma mensagem nova, acionar o webhook
    if (isNew) {
      const webhookParams = {
        key: '7SfDLUbeOXtCpLa09JpTcGaGjYeMiDyXlNg2uOU1',
        event: 'create',
        className: 'MensagemFila',
        objectId: mensagem.id
      };

      // Chamar o webhook de forma assíncrona
      Parse.Cloud.run("webhook", webhookParams, { useMasterKey: true })
        .catch(error => console.error(`Erro ao acionar webhook para MensagemFila ${mensagem.id}: ${error.message}`));
    }

  } catch (error) {
    console.error(`Erro no afterSave de MensagemFila: ${error.message}`);
  }
});

// Hook que é acionado após salvar um objeto da classe Notificacao
Parse.Cloud.afterSave("Notificacao", async (request) => {
  const notificacao = request.object;

  // Verificar se é uma notificação nova
  if (!request.original) {
    try {
      const tipo = notificacao.get('tipo');
      const filaId = notificacao.get('fila_id').id;
      const medicoId = notificacao.get('medico_id').id;
      const solicitacaoId = notificacao.get('solicitacao_id').id;

      console.log(`Nova notificação: tipo=${tipo}, fila=${filaId}, medico=${medicoId}`);

      // Buscar informações adicionais se necessário
      let titulo = 'Nova notificação';
      let mensagem = '';
      let channels = [];

      // Determinar canais e mensagem com base no tipo de notificação
      if (tipo === 'solicitacao_aprovada' && solicitacaoId) {
        // Buscar a solicitação para obter o ID do paciente
        const solicitacaoQuery = new Parse.Query('FilaSolicitacao');
        solicitacaoQuery.equalTo('objectId', solicitacaoId);
        const solicitacao = await solicitacaoQuery.first({ useMasterKey: true });

        if (solicitacao) {
          const idPaciente = solicitacao.get('idPaciente');
          const nome = solicitacao.get('nome') || 'paciente';

          if (idPaciente) {
            channels.push(`patient_${idPaciente}`);
          }

          titulo = 'Solicitação Aprovada';
          mensagem = `Olá ${nome}, sua solicitação foi aprovada! Você foi adicionado à fila.`;
        }
      } else if (tipo === 'solicitacao_rejeitada' && solicitacaoId) {
        // Similar ao caso de aprovação
        const solicitacaoQuery = new Parse.Query('FilaSolicitacao');
        solicitacaoQuery.equalTo('objectId', solicitacaoId);
        const solicitacao = await solicitacaoQuery.first({ useMasterKey: true });

        if (solicitacao) {
          const idPaciente = solicitacao.get('idPaciente');
          const nome = solicitacao.get('nome') || 'paciente';

          if (idPaciente) {
            channels.push(`patient_${idPaciente}`);
          }

          titulo = 'Solicitação Rejeitada';
          mensagem = `Olá ${nome}, infelizmente sua solicitação não pôde ser atendida no momento.`;
        }
      } else if (tipo === 'nova_solicitacao' && medicoId) {
        // Notificação para o médico ou consultório
        if (medicoId) {
          channels.push(`doctor_${medicoId}`);

          titulo = 'Nova Solicitação';
          mensagem = 'Você recebeu uma nova solicitação de atendimento.';
        }
      }

      // Enviar notificação push se tivermos canais definidos
      if (channels.length > 0 && mensagem) {
        await Parse.Push.send({
          channels: channels,
          data: {
            title: titulo,
            alert: mensagem,
            notificacaoId: notificacao.id,
            tipo: tipo
          }
        }, { useMasterKey: true });

        console.log(`Notificação push enviada para canais: ${channels.join(', ')}`);
      }

    } catch (error) {
      console.error(`Erro ao processar notificação: ${error.message}`);
    }
  }
});

// Função para processar webhooks
Parse.Cloud.define("webhook", async (request) => {
  try {
    const { key, event, className, objectId } = request.params;

    // Verificar chave de API
    if (key !== '7SfDLUbeOXtCpLa09JpTcGaGjYeMiDyXlNg2uOU1') {
      throw new Error('Chave de API inválida');
    }

    console.log(`Webhook acionado: ${event} ${className} ${objectId}`);

    // Processar com base no tipo de evento
    if (className === 'Fila') {
      if (event === 'create' || event === 'update') {
        // Buscar objeto atualizado
        const filaQuery = new Parse.Query('Fila');
        filaQuery.include(['medico', 'consultorio', 'solicitacao']);
        const fila = await filaQuery.get(objectId, { useMasterKey: true });

        if (!fila) {
          throw new Error(`Fila ${objectId} não encontrada`);
        }

        // Extrair dados para enviar para clientes conectados
        const filaData = {
          objectId: fila.id,
          status: fila.get('status'),
          posicao: fila.get('posicao'),
          nome: fila.get('nome'),
          idPaciente: fila.get('idPaciente'),
          tempo_estimado_minutos: fila.get('tempo_estimado_minutos'),
          ultima_atualizacao: new Date().toISOString()
        };

        // Se já tem um usuário conectado, enviar notificação
        const usuario = fila.get('usuario');
        if (usuario && usuario.id) {
          // Enviar notificação push para o usuário específico
          const pushQuery = new Parse.Query(Parse.Installation);
          pushQuery.equalTo('userId', usuario.id);

          await Parse.Push.send({
            where: pushQuery,
            data: {
              title: 'Atualização da Fila',
              alert: `Sua posição na fila foi atualizada para ${fila.get('posicao')}`,
              filaId: fila.id,
              badge: 'Increment',
              category: 'FILA_UPDATE',
              filaData: JSON.stringify(filaData)
            }
          }, { useMasterKey: true });

          console.log(`Notificação push enviada para usuário ${usuario.id}`);
        }
      } else if (event === 'delete') {
        // Ações para exclusão de fila, se necessário
        console.log(`Fila ${objectId} foi excluída`);
      }
    } else if (className === 'MensagemFila') {
      // Processar mensagens
      const mensagemQuery = new Parse.Query('MensagemFila');
      mensagemQuery.include(['medico', 'consultorio']);
      const mensagem = await mensagemQuery.get(objectId, { useMasterKey: true });

      if (!mensagem) {
        throw new Error(`MensagemFila ${objectId} não encontrada`);
      }

      const destinatarios = mensagem.get('destinatarios') || [];

      if (destinatarios.length > 0) {
        // Filtrar instalações para enviar push apenas para destinatários específicos
        const pushQuery = new Parse.Query(Parse.Installation);
        pushQuery.containedIn('userId', destinatarios);

        await Parse.Push.send({
          where: pushQuery,
          data: {
            title: mensagem.get('titulo') || 'Nova Mensagem',
            alert: mensagem.get('texto') || mensagem.get('mensagem'),
            mensagemId: mensagem.id,
            badge: 'Increment',
            category: 'MENSAGEM',
            prioridade: mensagem.get('prioridade') || 'normal'
          }
        }, { useMasterKey: true });

        console.log(`Notificação de mensagem enviada para ${destinatarios.length} destinatários`);

        // Marcar a mensagem como notificada
        mensagem.set('notificacao_enviada', true);
        await mensagem.save(null, { useMasterKey: true });
      } else {
        console.log(`Mensagem ${objectId} sem destinatários específicos`);
      }
    }

    return {
      success: true,
      message: `Webhook processado: ${event} ${className} ${objectId}`
    };
  } catch (error) {
    console.error(`Erro no processamento do webhook: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
});

// Cloud function to update secretary status with proper permissions
Parse.Cloud.define("updateSecretariaStatus", async (request) => {
  // Check if the user is authenticated
  if (!request.user) {
    throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, "User needs to be authenticated");
  }

  // Get parameters
  const secretariaId = request.params.secretariaId;
  const novoStatus = request.params.novoStatus;

  if (!secretariaId || novoStatus === undefined) {
    throw new Parse.Error(Parse.Error.INVALID_QUERY, "secretariaId and novoStatus are required");
  }

  try {
    // Get the secretary object
    const secretariaQuery = new Parse.Query("Secretaria");
    const secretaria = await secretariaQuery.get(secretariaId, { useMasterKey: true });

    if (!secretaria) {
      throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, "Secretária não encontrada");
    }

    // Update the status using master key
    secretaria.set("ativo", novoStatus);
    await secretaria.save(null, { useMasterKey: true });

    return { success: true };
  } catch (error) {
    throw new Parse.Error(Parse.Error.INTERNAL_SERVER_ERROR,
      `Error updating secretary status: ${error.message}`);
  }
});

// Cloud function to delete a secretary with proper permissions
Parse.Cloud.define("deleteSecretaria", async (request) => {
  // Check if the user is authenticated
  if (!request.user) {
    throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, "User needs to be authenticated");
  }

  // Get parameters
  const secretariaId = request.params.secretariaId;

  if (!secretariaId) {
    throw new Parse.Error(Parse.Error.INVALID_QUERY, "secretariaId is required");
  }

  try {
    // Get the secretary object
    const secretariaQuery = new Parse.Query("Secretaria");
    secretariaQuery.include("user_secretaria");
    const secretaria = await secretariaQuery.get(secretariaId, { useMasterKey: true });

    if (!secretaria) {
      throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, "Secretária não encontrada");
    }

    // Get the user associated with the secretary
    const user = secretaria.get("user_secretaria");

    // Delete the secretary using master key
    await secretaria.destroy({ useMasterKey: true });

    // Optionally, also delete the associated user
    if (user) {
      await user.destroy({ useMasterKey: true });
    }

    return { success: true };
  } catch (error) {
    throw new Parse.Error(Parse.Error.INTERNAL_SERVER_ERROR,
      `Error deleting secretary: ${error.message}`);
  }
});

// Cloud function to resend secretary credentials with proper permissions
Parse.Cloud.define("resendSecretariaCredentials", async (request) => {
  // Check if the user is authenticated
  if (!request.user) {
    throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, "User needs to be authenticated");
  }

  // Get parameters
  const secretariaId = request.params.secretariaId;

  if (!secretariaId) {
    throw new Parse.Error(Parse.Error.INVALID_QUERY, "secretariaId is required");
  }

  try {
    // Get the secretary object
    const secretariaQuery = new Parse.Query("Secretaria");
    secretariaQuery.include("user_secretaria");
    const secretaria = await secretariaQuery.get(secretariaId, { useMasterKey: true });

    if (!secretaria) {
      throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, "Secretária não encontrada");
    }

    // Get the user associated with the secretary
    const user = secretaria.get("user_secretaria");

    if (!user) {
      throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, "Usuário da secretária não encontrado");
    }

    // Generate a new password if needed
    let password = request.params.password;
    if (!password) {
      // Generate a random password with letters and numbers
      password = uuidv4().substring(0, 8);
    }

    // Update the user's password
    user.set("password", password);
    await user.save(null, { useMasterKey: true });

    // Get email details for sending
    const nome = secretaria.get("nome");
    const email = user.get("email") || user.get("username");

    // Send email with the credentials
    try {
      await Parse.Cloud.sendEmail({
        from: "<EMAIL>",
        to: email,
        subject: "Suas credenciais de acesso foram atualizadas",
        text: `Olá ${nome},\n\nSuas credenciais de acesso foram atualizadas:\n\nUsuário: ${email}\nSenha: ${password}\n\nAtenciosamente,\nEquipe do App`,
      });
    } catch (emailError) {
      console.error("Error sending email:", emailError);
      // Continue even if email fails
    }

    return {
      success: true,
      email: email,
      password: password
    };
  } catch (error) {
    throw new Parse.Error(Parse.Error.INTERNAL_SERVER_ERROR,
      `Error resending credentials: ${error.message}`);
  }
});

Parse.Cloud.define("ensureHospitalRole", async (request) => {
  try {
    const { userId } = request.params;

    if (!userId) {
      throw new Parse.Error(400, "ID do usuário não fornecido");
    }

    // Buscar o usuário
    const userQuery = new Parse.Query(Parse.User);
    const user = await userQuery.get(userId, { useMasterKey: true });

    if (!user) {
      throw new Parse.Error(404, "Usuário não encontrado");
    }

    // Verificar se é um hospital pelo tipo
    const userType = user.get("tipo");
    if (userType !== "consultorio" && userType !== "hospital") {
      throw new Parse.Error(400, "Usuário não é do tipo hospital/consultório");
    }

    // Verificar se já tem a role
    const userRoles = user.get("roles") || [];
    if (userRoles.includes("role:hospital")) {
      return {
        success: true,
        message: "Usuário já possui a role de hospital",
        alreadyHadRole: true
      };
    }

    // Buscar ou criar a role de hospital
    const roleQuery = new Parse.Query(Parse.Role);
    roleQuery.equalTo("name", "role:hospital");
    let hospitalRole = await roleQuery.first({ useMasterKey: true });

    if (!hospitalRole) {
      console.log("Criando nova role:hospital");
      const acl = new Parse.ACL();
      acl.setPublicReadAccess(true);

      hospitalRole = new Parse.Role("role:hospital", acl);
      await hospitalRole.save(null, { useMasterKey: true });
    }

    // Adicionar usuário à role
    hospitalRole.getUsers().add(user);
    await hospitalRole.save(null, { useMasterKey: true });

    // Atualizar o array de roles no usuário
    userRoles.push("role:hospital");
    user.set("roles", userRoles);
    await user.save(null, { useMasterKey: true });

    // Registrar log da operação
    const log = new Parse.Object("SecurityLog");
    log.set("action", "role_assignment");
    log.set("details", `Added role:hospital to user ${userId}`);
    log.set("user", user);
    await log.save(null, { useMasterKey: true });

    return {
      success: true,
      message: "Role de hospital atribuída com sucesso"
    };
  } catch (error) {
    console.error(`Erro ao atribuir role de hospital: ${error.message}`);
    throw new Parse.Error(500, `Erro ao atribuir role: ${error.message}`);
  }
});

// Function para verificar e corrigir todas as permissões do sistema
Parse.Cloud.define("verificarCorrigirPermissoes", async (request) => {
  const { master, user } = request;

  if (!master && !user) {
    throw new Error("Permissão negada");
  }

  // Garantir que todas as roles existam
  const rolesResult = await Parse.Cloud.run("garantirRolesExistem", {}, { useMasterKey: true });

  // Resultados
  const resultados = {
    rolesVerificadas: rolesResult,
    usuariosCorrigidos: [],
    erros: []
  };

  // Buscar todos os usuários
  const userQuery = new Parse.Query(Parse.User);
  userQuery.limit(1000); // ajustar conforme necessidade

  const usuarios = await userQuery.find({ useMasterKey: true });

  // Para cada usuário, verificar e corrigir suas permissões
  for (const usuario of usuarios) {
    try {
      const userId = usuario.id;
      const tipo = usuario.get("tipo");
      const roles = usuario.get("roles") || [];
      let modified = false;

      // Corrigir com base no tipo
      if (tipo === "hospital" && !roles.includes("role:hospital")) {
        roles.push("role:hospital");
        modified = true;
      } else if (tipo === "medico" && !roles.includes("role:medico")) {
        roles.push("role:medico");
        modified = true;
      } else if (tipo === "secretaria" && !roles.includes("role:secretaria")) {
        roles.push("role:secretaria");
        modified = true;
      } else if (tipo === "admin" && !roles.includes("role:admin")) {
        roles.push("role:admin");
        modified = true;
      }

      // Se o usuário foi modificado, salvar as alterações
      if (modified) {
        usuario.set("roles", roles);
        await usuario.save(null, { useMasterKey: true });

        // Adicionar usuário às roles no Parse
        for (const roleName of roles) {
          try {
            const roleQuery = new Parse.Query(Parse.Role);
            roleQuery.equalTo("name", roleName);

            const role = await roleQuery.first({ useMasterKey: true });

            if (role) {
              role.getUsers().add(usuario);
              await role.save(null, { useMasterKey: true });
            }
          } catch (roleError) {
            resultados.erros.push(`Erro ao adicionar usuário ${userId} à role ${roleName}: ${roleError.message}`);
          }
        }

        resultados.usuariosCorrigidos.push({
          userId,
          tipo,
          novasRoles: roles
        });
      }
    } catch (error) {
      resultados.erros.push(`Erro ao processar usuário ${usuario.id}: ${error.message}`);
    }
  }

  return resultados;
});

// Adicionar função de utilidade para garantir que roles existam
Parse.Cloud.define("garantirRolesExistem", async (request) => {
  const { master } = request;

  if (!master && !request.user) {
    throw new Error("Permissão negada");
  }

  const roles = ["role:admin", "role:hospital", "role:medico", "role:secretaria"];
  const results = {};

  for (const roleName of roles) {
    try {
      const roleQuery = new Parse.Query(Parse.Role);
      roleQuery.equalTo("name", roleName);
      let role = await roleQuery.first({ useMasterKey: true });

      if (!role) {
        // Criar a role se não existir
        const acl = new Parse.ACL();
        acl.setPublicReadAccess(true);
        acl.setPublicWriteAccess(false);

        role = new Parse.Role(roleName, acl);
        await role.save(null, { useMasterKey: true });
        results[roleName] = "Criada";
      } else {
        results[roleName] = "Já existe";
      }
    } catch (error) {
      results[roleName] = `Erro: ${error.message}`;
    }
  }

  return results;
});

// Adicionar hook para antes de salvar usuários
Parse.Cloud.beforeSave(Parse.User, async (request) => {
  const user = request.object;

  // Se for um novo usuário, garantir que tenha o array de roles
  if (user.isNew()) {
    const roles = user.get("roles") || [];
    const tipo = user.get("tipo");

    // Se o tipo está definido, garantir que a role correspondente esteja adicionada
    if (tipo === "hospital" && !roles.includes("role:hospital")) {
      roles.push("role:hospital");
    } else if (tipo === "medico" && !roles.includes("role:medico")) {
      roles.push("role:medico");
    } else if (tipo === "secretaria" && !roles.includes("role:secretaria")) {
      roles.push("role:secretaria");
    } else if (tipo === "admin" && !roles.includes("role:admin")) {
      roles.push("role:admin");
    }

    user.set("roles", roles);
  }
});

// Adicionar hook para depois de salvar usuários
Parse.Cloud.afterSave(Parse.User, async (request) => {
  const user = request.object;

  // Se o usuário estiver sendo criado, adicionar às roles apropriadas
  if (request.context.userCreation) {
    try {
      const roles = user.get("roles") || [];

      // Para cada role no array, adicionar o usuário à role no Parse
      for (const roleName of roles) {
        const roleQuery = new Parse.Query(Parse.Role);
        roleQuery.equalTo("name", roleName);

        const role = await roleQuery.first({ useMasterKey: true });

        if (role) {
          role.getUsers().add(user);
          await role.save(null, { useMasterKey: true });
          console.log(`Usuário ${user.id} adicionado à role ${roleName}`);
        } else {
          console.error(`Role ${roleName} não encontrada`);
          // Criar a role se ela não existir
          await Parse.Cloud.run("garantirRolesExistem", {}, { useMasterKey: true });

          // Tentar novamente
          const newRoleQuery = new Parse.Query(Parse.Role);
          newRoleQuery.equalTo("name", roleName);
          const newRole = await newRoleQuery.first({ useMasterKey: true });

          if (newRole) {
            newRole.getUsers().add(user);
            await newRole.save(null, { useMasterKey: true });
            console.log(`Usuário ${user.id} adicionado à role recém-criada ${roleName}`);
          }
        }
      }
    } catch (error) {
      console.error("Erro ao adicionar usuário às roles:", error);
    }
  }
});

// Função para verificar roles existentes
Parse.Cloud.define("verificarRolesExistentes", async (request) => {
  try {
    const roleQuery = new Parse.Query(Parse.Role);
    const roles = await roleQuery.find({ useMasterKey: true });

    const result = {
      success: true,
      total: roles.length,
      roles: roles.map(role => ({
        id: role.id,
        name: role.get("name"),
        createdAt: role.createdAt,
        updatedAt: role.updatedAt
      }))
    };

    // Para cada role, contar quantos usuários têm essa role
    for (let i = 0; i < result.roles.length; i++) {
      const role = roles[i];
      const relationCount = await role.getUsers().query().count({ useMasterKey: true });
      result.roles[i].userCount = relationCount;
    }

    return result;
  } catch (error) {
    console.error("Erro ao verificar roles:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Função para padronizar nomenclatura (consultorio -> hospital)
Parse.Cloud.define("padronizarTiposUsuarios", async (request) => {
  if (!request.master && (!request.user || !request.user.get("isAdmin"))) {
    throw new Parse.Error(403, "Apenas administradores podem executar esta função");
  }

  try {
    // 1. Atualizar usuários tipo "consultorio" para "hospital"
    const userQuery = new Parse.Query(Parse.User);
    userQuery.equalTo("tipo", "consultorio");
    const users = await userQuery.find({ useMasterKey: true });

    console.log(`Encontrados ${users.length} usuários para atualizar de 'consultorio' para 'hospital'`);

    let updated = 0;
    for (const user of users) {
      user.set("tipo", "hospital");
      await user.save(null, { useMasterKey: true });
      updated++;
    }

    // 2. Registrar a mudança no log
    const log = new Parse.Object("SecurityLog");
    log.set("action", "padronizar_tipos_usuarios");
    log.set("details", `Atualizados ${updated} usuários de 'consultorio' para 'hospital'`);
    await log.save(null, { useMasterKey: true });

    return {
      success: true,
      count: updated,
      message: `Atualizados ${updated} usuários com sucesso.`
    };
  } catch (error) {
    console.error("Erro ao padronizar tipos de usuários:", error);
    throw new Parse.Error(500, `Erro na padronização: ${error.message}`);
  }
});

// Função para dar suporte a ambas nomenclaturas (consultorio/hospital)
Parse.Cloud.define("ensureHospitalRoleSuporteDuplo", async (request) => {
  try {
    const { userId } = request.params;

    if (!userId) {
      throw new Parse.Error(400, "ID do usuário não fornecido");
    }

    // Buscar o usuário
    const userQuery = new Parse.Query(Parse.User);
    const user = await userQuery.get(userId, { useMasterKey: true });

    if (!user) {
      throw new Parse.Error(404, "Usuário não encontrado");
    }

    // Verificar se é um hospital pelos dois tipos possíveis
    const userType = user.get("tipo");
    const isHospitalType = userType === "consultorio" || userType === "hospital";

    if (!isHospitalType) {
      throw new Parse.Error(400, "Usuário não é do tipo hospital/consultório");
    }

    // Verificar se já tem a role
    const userRoles = user.get("roles") || [];
    if (userRoles.includes("role:hospital")) {
      return {
        success: true,
        message: "Usuário já possui a role de hospital",
        alreadyHadRole: true
      };
    }

    // Buscar ou criar a role de hospital
    const roleQuery = new Parse.Query(Parse.Role);
    roleQuery.equalTo("name", "role:hospital");
    let hospitalRole = await roleQuery.first({ useMasterKey: true });

    if (!hospitalRole) {
      console.log("Criando nova role:hospital");
      const acl = new Parse.ACL();
      acl.setPublicReadAccess(true);

      hospitalRole = new Parse.Role("role:hospital", acl);
      await hospitalRole.save(null, { useMasterKey: true });
    }

    // Adicionar usuário à role
    hospitalRole.getUsers().add(user);
    await hospitalRole.save(null, { useMasterKey: true });

    // Atualizar o array de roles no usuário
    userRoles.push("role:hospital");
    user.set("roles", userRoles);
    await user.save(null, { useMasterKey: true });

    // Registrar log da operação
    const log = new Parse.Object("SecurityLog");
    log.set("action", "role_assignment");
    log.set("details", `Added role:hospital to user ${userId} (type: ${userType})`);
    log.set("user", user);
    await log.save(null, { useMasterKey: true });

    return {
      success: true,
      message: "Role de hospital atribuída com sucesso"
    };
  } catch (error) {
    console.error(`Erro ao atribuir role de hospital: ${error.message}`);
    throw new Parse.Error(500, `Erro ao atribuir role: ${error.message}`);
  }
});

// Função para executar migração completa do sistema
Parse.Cloud.define("executarMigracaoCompleta", async (request) => {
  if (!request.master) {
    throw new Parse.Error(403, "Esta função só pode ser executada com masterKey");
  }

  const resultados = {
    etapas: [],
    erros: []
  };

  try {
    // 1. Passo: Criar as roles se não existirem
    try {
      // Role de hospital
      const hospitalRoleQuery = new Parse.Query(Parse.Role);
      hospitalRoleQuery.equalTo("name", "role:hospital");
      let hospitalRole = await hospitalRoleQuery.first({ useMasterKey: true });

      if (!hospitalRole) {
        const acl = new Parse.ACL();
        acl.setPublicReadAccess(true);

        hospitalRole = new Parse.Role("role:hospital", acl);
        await hospitalRole.save(null, { useMasterKey: true });
        resultados.etapas.push("Role 'role:hospital' criada com sucesso");
      } else {
        resultados.etapas.push("Role 'role:hospital' já existia");
      }

      // Role de secretaria
      const secretariaRoleQuery = new Parse.Query(Parse.Role);
      secretariaRoleQuery.equalTo("name", "role:secretaria");
      let secretariaRole = await secretariaRoleQuery.first({ useMasterKey: true });

      if (!secretariaRole) {
        const acl = new Parse.ACL();
        acl.setPublicReadAccess(true);

        secretariaRole = new Parse.Role("role:secretaria", acl);
        await secretariaRole.save(null, { useMasterKey: true });
        resultados.etapas.push("Role 'role:secretaria' criada com sucesso");
      } else {
        resultados.etapas.push("Role 'role:secretaria' já existia");
      }

      // Role de admin (por garantia)
      const adminRoleQuery = new Parse.Query(Parse.Role);
      adminRoleQuery.equalTo("name", "role:admin");
      let adminRole = await adminRoleQuery.first({ useMasterKey: true });

      if (!adminRole) {
        const acl = new Parse.ACL();
        acl.setPublicReadAccess(true);

        adminRole = new Parse.Role("role:admin", acl);
        await adminRole.save(null, { useMasterKey: true });
        resultados.etapas.push("Role 'role:admin' criada com sucesso");
      } else {
        resultados.etapas.push("Role 'role:admin' já existia");
      }
    } catch (error) {
      resultados.erros.push(`Erro na criação de roles: ${error.message}`);
    }

    // 2. Passo: Verificar e atribuir as roles a todos os usuários
    try {
      const result = await Parse.Cloud.run("verificarCorrigirPermissoes", {}, { useMasterKey: true });
      resultados.etapas.push("Verificação e correção de permissões concluída");
      resultados.detalhesPermissoes = result;
    } catch (error) {
      resultados.erros.push(`Erro na verificação de permissões: ${error.message}`);
    }

    // 3. Passo: Verificar status atual das roles
    try {
      const result = await Parse.Cloud.run("verificarRolesExistentes", {}, { useMasterKey: true });
      resultados.etapas.push("Verificação de roles existentes concluída");
      resultados.detalhesRoles = result;
    } catch (error) {
      resultados.erros.push(`Erro na verificação de roles: ${error.message}`);
    }

    return {
      success: true,
      resultados: resultados
    };
  } catch (error) {
    console.error("Erro na migração completa:", error);
    return {
      success: false,
      error: error.message,
      resultados: resultados
    };
  }
});

// Função para corrigir tipos de usuário e nomes com base na área profissional
Parse.Cloud.define("corrigirUsuarios", async (request) => {
  // Verificar se o usuário atual é um administrador
  if (!request.user || request.user.get("tipo") !== "admin") {
    throw new Error("Apenas administradores podem executar esta função");
  }

  const resultado = {
    total: 0,
    corrigidos: 0,
    erros: 0,
    detalhes: []
  };

  try {
    // Buscar todos os usuários
    const userQuery = new Parse.Query(Parse.User);
    userQuery.limit(1000); // Limite para evitar problemas de performance
    const usuarios = await userQuery.find({ useMasterKey: true });

    resultado.total = usuarios.length;

    // Processar cada usuário
    for (const user of usuarios) {
      try {
        const userId = user.id;
        const username = user.get("username");
        const tipo = user.get("tipo");
        const areaProfissional = user.get("areaProfissional");
        const nome = user.get("nome");
        const email = user.get("email");

        let corrigido = false;
        let alteracoes = [];

        // Caso 1: Usuário tem área profissional, verificar se o tipo está correto
        if (areaProfissional) {
          const correctType = mapAreaToUserType(areaProfissional);

          if (tipo !== correctType) {
            console.log(`Corrigindo tipo de usuário ${username} (${userId}): ${tipo} -> ${correctType} (baseado na área profissional: ${areaProfissional})`);
            user.set('tipo', correctType);
            corrigido = true;
            alteracoes.push(`Tipo: ${tipo} -> ${correctType}`);
          }
        }
        // Caso 2: Usuário não tem área profissional, mas o tipo parece ser uma área profissional
        else if (tipo && tipo.toLowerCase() !== 'medico' && tipo.toLowerCase() !== 'consultorio' &&
          tipo.toLowerCase() !== 'secretaria' && tipo.toLowerCase() !== 'admin' &&
          tipo.toLowerCase() !== 'paciente') {

          const correctType = mapAreaToUserType(tipo);

          if (correctType === 'medico' && tipo.toLowerCase() !== 'medico') {
            console.log(`Corrigindo tipo de usuário ${username} (${userId}): ${tipo} -> ${correctType} (salvando área profissional: ${tipo})`);
            user.set('tipo', correctType);
            user.set('areaProfissional', tipo);
            corrigido = true;
            alteracoes.push(`Tipo: ${tipo} -> ${correctType}, Área profissional: ${tipo}`);
          }
        }

        // Caso 3: Verificar se o nome está definido
        if (!nome) {
          // Se não tiver nome, usar o username ou email
          if (username && username !== email) {
            console.log(`Definindo nome do usuário ${userId} como o username: ${username}`);
            user.set('nome', username);
            corrigido = true;
            alteracoes.push(`Nome: null -> ${username}`);
          } else if (email) {
            // Usar a parte do email antes do @
            const emailName = email.split('@')[0];
            console.log(`Definindo nome do usuário ${userId} baseado no email: ${emailName}`);
            user.set('nome', emailName);
            corrigido = true;
            alteracoes.push(`Nome: null -> ${emailName}`);
          }
        }

        // Salvar as alterações se necessário
        if (corrigido) {
          await user.save(null, { useMasterKey: true });
          resultado.corrigidos++;

          resultado.detalhes.push({
            id: userId,
            username: username,
            email: email,
            alteracoes: alteracoes.join('; ')
          });
        }
      } catch (userError) {
        console.error(`Erro ao processar usuário ${user.id}:`, userError);
        resultado.erros++;
      }
    }

    return {
      success: true,
      resultado: resultado
    };
  } catch (error) {
    console.error("Erro ao corrigir usuários:", error);
    return {
      success: false,
      error: error.message,
      resultado: resultado
    };
  }
});

// Função para verificar status atual do sistema
Parse.Cloud.define("verificarStatusSistema", async (request) => {
  const resultado = {
    roles: null,
    usuarios: {
      total: 0,
      porTipo: {},
      comRoles: 0,
      semRoles: 0
    },
    secretarias: {
      total: 0,
      comUsuario: 0,
      semUsuario: 0
    }
  };

  try {
    // 1. Verificar roles existentes
    try {
      const roles = await Parse.Cloud.run("verificarRolesExistentes", {}, { useMasterKey: true });
      resultado.roles = roles;
    } catch (error) {
      resultado.roles = { error: error.message };
    }

    // 2. Verificar usuários
    try {
      const userQuery = new Parse.Query(Parse.User);
      resultado.usuarios.total = await userQuery.count({ useMasterKey: true });

      // Contar por tipo
      const tiposUsuarios = ["hospital", "consultorio", "secretaria", "medico", "paciente", "admin"];
      for (const tipo of tiposUsuarios) {
        const tipoQuery = new Parse.Query(Parse.User);
        tipoQuery.equalTo("tipo", tipo);
        resultado.usuarios.porTipo[tipo] = await tipoQuery.count({ useMasterKey: true });
      }

      // Verificar usuários com tipo potencialmente incorreto
      const todosUsuariosQuery = new Parse.Query(Parse.User);
      todosUsuariosQuery.select("username", "tipo", "areaProfissional");
      todosUsuariosQuery.limit(1000); // Limite para evitar problemas de performance
      const todosUsuarios = await todosUsuariosQuery.find({ useMasterKey: true });

      // Verificar tipos incorretos
      resultado.usuarios.tipoIncorreto = [];

      for (const user of todosUsuarios) {
        const tipo = user.get("tipo");
        const areaProfissional = user.get("areaProfissional");

        // Verificar se o tipo está correto com base na área profissional
        if (areaProfissional) {
          const correctType = mapAreaToUserType(areaProfissional);
          if (tipo !== correctType) {
            resultado.usuarios.tipoIncorreto.push({
              id: user.id,
              username: user.get("username"),
              tipo: tipo,
              areaProfissional: areaProfissional,
              tipoCorreto: correctType
            });
          }
        }
        // Verificar se o tipo parece ser uma área profissional
        else if (tipo && tipo.toLowerCase() !== 'medico' && tipo.toLowerCase() !== 'consultorio' &&
          tipo.toLowerCase() !== 'secretaria' && tipo.toLowerCase() !== 'admin' &&
          tipo.toLowerCase() !== 'paciente') {

          const correctType = mapAreaToUserType(tipo);
          if (correctType === 'medico' && tipo.toLowerCase() !== 'medico') {
            resultado.usuarios.tipoIncorreto.push({
              id: user.id,
              username: user.get("username"),
              tipo: tipo,
              tipoCorreto: correctType,
              motivo: "Tipo parece ser uma área profissional"
            });
          }
        }
      }

      // Contar usuários com e sem roles
      const comRolesQuery = new Parse.Query(Parse.User);
      comRolesQuery.exists("roles");
      resultado.usuarios.comRoles = await comRolesQuery.count({ useMasterKey: true });
      resultado.usuarios.semRoles = resultado.usuarios.total - resultado.usuarios.comRoles;
    } catch (error) {
      resultado.usuarios.error = error.message;
    }

    // 3. Verificar secretárias
    try {
      const secretariaQuery = new Parse.Query("Secretaria");
      resultado.secretarias.total = await secretariaQuery.count({ useMasterKey: true });

      const comUsuarioQuery = new Parse.Query("Secretaria");
      comUsuarioQuery.exists("user_secretaria");
      resultado.secretarias.comUsuario = await comUsuarioQuery.count({ useMasterKey: true });
      resultado.secretarias.semUsuario = resultado.secretarias.total - resultado.secretarias.comUsuario;
    } catch (error) {
      resultado.secretarias.error = error.message;
    }

    return {
      success: true,
      status: resultado,
      timestamp: new Date()
    };
  } catch (error) {
    console.error("Erro ao verificar status do sistema:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Cloud Functions para criar usuários e secretárias com master key (para contornar problemas de permissão)
Parse.Cloud.define("criarUsuarioComMasterKey", async (request) => {
  // Permitir acesso mesmo sem estar autenticado
  // if (!request.user) {
  //   throw new Error("Você precisa estar autenticado para usar esta função");
  // }

  const { username, email, password, tipo, nome, areaProfissional, roles, dataCadastro } = request.params;

  if (!email || !password) {
    throw new Error("Email e senha são obrigatórios");
  }

  // Definir o username - preferir nome, depois username, depois email
  const effectiveUsername = nome || username || email;

  try {
    // Mapear a área profissional para o tipo de usuário correto
    let userType = tipo;

    // Se tiver área profissional, usar o mapeamento
    if (areaProfissional) {
      userType = mapAreaToUserType(areaProfissional);
      console.log(`Área profissional '${areaProfissional}' mapeada para tipo de usuário '${userType}'`);
    } else if (tipo && tipo.toLowerCase() !== 'medico' && tipo.toLowerCase() !== 'consultorio' &&
      tipo.toLowerCase() !== 'secretaria' && tipo.toLowerCase() !== 'admin' &&
      tipo.toLowerCase() !== 'paciente') {
      // Se o tipo não for um dos tipos padrão, tentar mapear
      const mappedType = mapAreaToUserType(tipo);
      console.log(`Tipo '${tipo}' mapeado para '${mappedType}'`);
      userType = mappedType;
    }

    // Criar o usuário com masterKey
    const user = new Parse.User();
    user.set("username", effectiveUsername); // Usar nome como username quando disponível
    user.set("password", password);
    user.set("email", email);

    if (userType) user.set("tipo", userType);
    if (nome) user.set("nome", nome); // Armazenar o nome como campo separado
    if (areaProfissional) user.set("areaProfissional", areaProfissional);
    if (roles) user.set("roles", roles);
    if (dataCadastro) user.set("dataCadastro", new Date(dataCadastro));

    // Configurar ACL para permitir acesso do hospital
    const acl = new Parse.ACL();
    acl.setPublicReadAccess(true);

    // Se tem usuário logado, dar acesso a ele
    if (request.user) {
      acl.setReadAccess(request.user.id, true);
      acl.setWriteAccess(request.user.id, true);
    }

    user.setACL(acl);

    // Salvar o usuário com master key
    const result = await user.save(null, { useMasterKey: true });

    // Adicionar o usuário às roles, se aplicável
    if (roles && roles.length > 0) {
      await Parse.Cloud.run("adicionarUsuarioARoles", {
        userId: user.id,
        roles: roles
      }, { useMasterKey: true });
    }

    return {
      objectId: user.id,
      success: true
    };
  } catch (error) {
    console.error("Erro ao criar usuário:", error);
    throw new Error(`Falha ao criar usuário: ${error.message}`);
  }
});

// Adicionar usuário a roles
Parse.Cloud.define("adicionarUsuarioARoles", async (request) => {
  const { userId, roles } = request.params;
  const { master } = request;

  if (!master && !request.user) {
    throw new Error("Permissão negada");
  }

  if (!userId || !roles || !Array.isArray(roles)) {
    throw new Error("Parâmetros inválidos");
  }

  const resultados = {};

  // Buscar o usuário
  const userQuery = new Parse.Query(Parse.User);
  const user = await userQuery.get(userId, { useMasterKey: true });

  if (!user) {
    throw new Error("Usuário não encontrado");
  }

  // Para cada role, adicionar o usuário
  for (const roleName of roles) {
    try {
      // Buscar a role
      const roleQuery = new Parse.Query(Parse.Role);
      roleQuery.equalTo("name", roleName);
      let role = await roleQuery.first({ useMasterKey: true });

      // Se a role não existir, criar
      if (!role) {
        // Criar a role
        await Parse.Cloud.run("garantirRolesExistem", {}, { useMasterKey: true });
        role = await roleQuery.first({ useMasterKey: true });

        if (!role) {
          resultados[roleName] = "Erro: Role não pôde ser criada";
          continue;
        }
      }

      // Adicionar o usuário à role
      role.getUsers().add(user);
      await role.save(null, { useMasterKey: true });

      resultados[roleName] = "Adicionado com sucesso";
    } catch (error) {
      resultados[roleName] = `Erro: ${error.message}`;
    }
  }

  return resultados;
});

// Cloud Function para criar secretária com master key
Parse.Cloud.define("criarSecretariaComMasterKey", async (request) => {
  // Permitir acesso mesmo sem estar autenticado
  // if (!request.user) {
  //   throw new Error("Você precisa estar autenticado para usar esta função");
  // }

  const {
    nome,
    email,
    telefone,
    ativo,
    consultorio,
    dataCadastro,
    cpf,
    userObjectId
  } = request.params;

  if (!nome || !email || !userObjectId || !consultorio) {
    throw new Error("Dados obrigatórios não fornecidos");
  }

  try {
    // Buscar o usuário para associar
    const userQuery = new Parse.Query(Parse.User);
    const user = await userQuery.get(userObjectId, { useMasterKey: true });

    if (!user) {
      throw new Error("Usuário não encontrado");
    }

    // Buscar o hospital
    const hospitalQuery = new Parse.Query("consultorio");
    const hospital = await hospitalQuery.get(consultorio.objectId, { useMasterKey: true });

    if (!hospital) {
      throw new Error("Hospital não encontrado");
    }

    // Criar o objeto Secretaria
    const secretaria = new Parse.Object("Secretaria");
    secretaria.set("nome", nome);
    secretaria.set("email", email);
    if (telefone) secretaria.set("telefone", telefone);
    secretaria.set("ativo", ativo !== false);
    secretaria.set("consultorio", hospital);
    secretaria.set("user_secretaria", user);
    if (dataCadastro) secretaria.set("dataCadastro", new Date(dataCadastro));
    if (cpf) secretaria.set("cpf", cpf);

    // Configurar ACL para permitir acesso do hospital e do próprio usuário
    const acl = new Parse.ACL();
    acl.setPublicReadAccess(true);

    // Se tem usuário logado, dar acesso a ele
    if (request.user) {
      acl.setReadAccess(request.user.id, true);
      acl.setWriteAccess(request.user.id, true);
    }

    // Dar acesso ao hospital associado ao consultório
    if (hospital.get("user_consultorio")) {
      const hospitalUserId = hospital.get("user_consultorio").id;
      acl.setReadAccess(hospitalUserId, true);
      acl.setWriteAccess(hospitalUserId, true);
    }

    // Dar acesso ao usuário da secretária
    acl.setReadAccess(user.id, true);
    acl.setWriteAccess(user.id, true);

    secretaria.setACL(acl);

    // Salvar com master key
    await secretaria.save(null, { useMasterKey: true });

    // Estabelecer relação bidirecional
    const relation = user.relation("user_secretaria");
    relation.add(secretaria);
    await user.save(null, { useMasterKey: true });

    return {
      objectId: secretaria.id,
      success: true
    };
  } catch (error) {
    console.error("Erro ao criar secretária:", error);
    throw new Error(`Falha ao criar secretária: ${error.message}`);
  }
});

// Cloud Function para excluir um usuário
Parse.Cloud.define("excluirUsuario", async (request) => {
  const { userId } = request.params;

  if (!userId) {
    throw new Error("ID do usuário não fornecido");
  }

  try {
    // Buscar o usuário para excluir
    const userQuery = new Parse.Query(Parse.User);
    const user = await userQuery.get(userId, { useMasterKey: true });

    if (!user) {
      throw new Error("Usuário não encontrado");
    }

    // Remover sessões do usuário primeiro
    try {
      const sessionQuery = new Parse.Query("_Session");
      sessionQuery.equalTo("user", user.toPointer());
      const sessions = await sessionQuery.find({ useMasterKey: true });

      for (const session of sessions) {
        await session.destroy({ useMasterKey: true });
      }

      console.log(`Removidas ${sessions.length} sessões do usuário ${userId}`);
    } catch (e) {
      console.error("Erro ao remover sessões:", e);
    }

    // Agora excluir o usuário
    await user.destroy({ useMasterKey: true });

    return {
      success: true,
      message: "Usuário excluído com sucesso"
    };
  } catch (error) {
    console.error("Erro ao excluir usuário:", error);
    throw new Error(`Falha ao excluir usuário: ${error.message}`);
  }
});

// Adiciona um timestamp a cada modificação da Fila para otimizar sincronização
Parse.Cloud.beforeSave("Fila", async (request) => {
  request.object.set("lastModified", new Date());
});

// Função para obter apenas as filas atualizadas desde um determinado momento
Parse.Cloud.define("getUpdatedFilas", async (request) => {
  if (!request.user) {
    throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, "User needs to be authenticated");
  }

  const { lastSyncTime, medicoId, consultorioId } = request.params;

  if (!lastSyncTime) {
    throw new Parse.Error(Parse.Error.INVALID_QUERY, "lastSyncTime is required");
  }

  if (!medicoId && !consultorioId) {
    throw new Parse.Error(Parse.Error.INVALID_QUERY, "Either medicoId or consultorioId is required");
  }

  try {
    const lastSync = new Date(lastSyncTime);
    const query = new Parse.Query("Fila");

    // Filtrar por médico ou consultório conforme o contexto
    if (medicoId) {
      query.equalTo("medico", Parse.Object.createWithoutData("Medico", medicoId));
    } else if (consultorioId) {
      query.equalTo("consultorio", Parse.Object.createWithoutData("consultorio", consultorioId));
    }

    // Buscar apenas registros modificados após o último sync
    query.greaterThan("lastModified", lastSync);

    // Incluir objetos relacionados para evitar múltiplas consultas no cliente
    query.include("medico");
    query.include("consultorio");
    query.include("paciente");

    // Ordenar por posição para manter a consistência com outras consultas
    query.ascending("posicao");

    const results = await query.find({ useMasterKey: true });

    // Para cada fila, verificar se foi excluída (se necessário)
    // Caso o Back4App não forneça nativamente essa informação

    return {
      success: true,
      results: results,
      updatedAt: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Erro ao buscar filas atualizadas: ${error.message}`);
    throw new Parse.Error(
      Parse.Error.INTERNAL_SERVER_ERROR,
      `Erro ao buscar filas atualizadas: ${error.message}`
    );
  }
});

// Função para obter a quantidade de atualizações pendentes
Parse.Cloud.define("getFilaUpdateCount", async (request) => {
  if (!request.user) {
    throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, "User needs to be authenticated");
  }

  const { lastSyncTime, medicoId, consultorioId } = request.params;

  if (!lastSyncTime) {
    throw new Parse.Error(Parse.Error.INVALID_QUERY, "lastSyncTime is required");
  }

  if (!medicoId && !consultorioId) {
    throw new Parse.Error(Parse.Error.INVALID_QUERY, "Either medicoId or consultorioId is required");
  }

  try {
    const lastSync = new Date(lastSyncTime);
    const query = new Parse.Query("Fila");

    // Filtrar por médico ou consultório conforme o contexto
    if (medicoId) {
      query.equalTo("medico", Parse.Object.createWithoutData("Medico", medicoId));
    } else if (consultorioId) {
      query.equalTo("consultorio", Parse.Object.createWithoutData("consultorio", consultorioId));
    }

    // Buscar apenas registros modificados após o último sync
    query.greaterThan("lastModified", lastSync);

    const count = await query.count({ useMasterKey: true });

    return {
      success: true,
      count: count,
      checkedAt: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Erro ao verificar atualizações da fila: ${error.message}`);
    throw new Parse.Error(
      Parse.Error.INTERNAL_SERVER_ERROR,
      `Erro ao verificar atualizações da fila: ${error.message}`
    );
  }
});

// Configuração do LiveQuery para a classe Fila
Parse.Cloud.define("setupLiveQuery", async (request) => {
  try {
    // Verifica se o LiveQuery já foi inicializado
    const liveQueryClassNames = await Parse.Config.get('liveQueryClassNames') || [];

    // Lista completa de classes que devem ser monitoradas pelo LiveQuery
    const classesToMonitor = [
      'Fila',
      'MensagemFila',
      'Notificacao',
      'FilaSolicitacao',
      'Medico',
      'Secretaria',
      'consultorio',
      'MetricasAtendimento'
    ];

    // Adicionar todas as classes que ainda não estão na lista
    const newClasses = classesToMonitor.filter(cls => !liveQueryClassNames.includes(cls));

    if (newClasses.length > 0) {
      // Atualiza a configuração do Parse com a nova lista
      const config = new Parse.Config();
      config.set('liveQueryClassNames', [...liveQueryClassNames, ...newClasses]);
      config.set('liveQueryEnabled', true);
      await config.save({ useMasterKey: true });

      console.log(`LiveQuery configurado para as classes: ${newClasses.join(', ')}`);
    }

    return {
      success: true,
      message: "LiveQuery configurado com sucesso",
      classes: [...liveQueryClassNames, ...newClasses]
    };
  } catch (error) {
    console.error(`Erro ao configurar LiveQuery: ${error.message}`);
    throw new Parse.Error(
      Parse.Error.INTERNAL_SERVER_ERROR,
      `Erro ao configurar LiveQuery: ${error.message}`
    );
  }
});

// Função para operações em lote (batch operations)
Parse.Cloud.define("batchOperations", async (request) => {
  try {
    const { operations } = request.params;

    if (!operations || !Array.isArray(operations) || operations.length === 0) {
      throw new Error("Parâmetro 'operations' inválido ou vazio");
    }

    // Verificar se o usuário está autenticado
    if (!request.user) {
      throw new Error("Usuário não autenticado");
    }

    const results = [];
    const promises = [];

    // Processar cada operação
    for (const op of operations) {
      const { action, className, objectId, data } = op;

      if (!action || !className) {
        results.push({
          success: false,
          error: "Parâmetros 'action' e 'className' são obrigatórios"
        });
        continue;
      }

      let object;

      if (action === 'create') {
        // Criar novo objeto
        object = new Parse.Object(className);
        if (data) {
          Object.keys(data).forEach(key => {
            // Tratar ponteiros para outros objetos
            if (data[key] && data[key].className && data[key].objectId) {
              const pointer = new Parse.Object(data[key].className);
              pointer.id = data[key].objectId;
              object.set(key, pointer);
            } else {
              object.set(key, data[key]);
            }
          });
        }
        promises.push(object.save(null, { useMasterKey: true }));
      }
      else if (action === 'update') {
        // Atualizar objeto existente
        if (!objectId) {
          results.push({
            success: false,
            error: "Parâmetro 'objectId' é obrigatório para ação 'update'"
          });
          continue;
        }

        object = new Parse.Object(className);
        object.id = objectId;

        if (data) {
          Object.keys(data).forEach(key => {
            // Tratar ponteiros para outros objetos
            if (data[key] && data[key].className && data[key].objectId) {
              const pointer = new Parse.Object(data[key].className);
              pointer.id = data[key].objectId;
              object.set(key, pointer);
            } else {
              object.set(key, data[key]);
            }
          });
        }
        promises.push(object.save(null, { useMasterKey: true }));
      }
      else if (action === 'delete') {
        // Excluir objeto existente
        if (!objectId) {
          results.push({
            success: false,
            error: "Parâmetro 'objectId' é obrigatório para ação 'delete'"
          });
          continue;
        }

        object = new Parse.Object(className);
        object.id = objectId;
        promises.push(object.destroy({ useMasterKey: true }));
      }
      else {
        results.push({
          success: false,
          error: `Ação '${action}' não suportada`
        });
        continue;
      }
    }

    // Aguardar todas as operações serem concluídas
    const promiseResults = await Promise.allSettled(promises);

    // Processar resultados
    promiseResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results.push({
          success: true,
          operation: operations[index],
          result: result.value
        });
      } else {
        results.push({
          success: false,
          operation: operations[index],
          error: result.reason.message
        });
      }
    });

    return {
      success: true,
      results: results
    };
  } catch (error) {
    console.error("Erro em batchOperations:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Função para gerenciar cache no servidor
Parse.Cloud.define("cacheManager", async (request) => {
  try {
    const { action, key, data, expirationMinutes } = request.params;

    if (!action || !key) {
      throw new Error("Parâmetros 'action' e 'key' são obrigatórios");
    }

    // Verificar se o usuário está autenticado
    if (!request.user) {
      throw new Error("Usuário não autenticado");
    }

    // Usar a classe Cache para armazenar dados em cache
    const cacheQuery = new Parse.Query("Cache");
    cacheQuery.equalTo("key", key);

    if (action === 'get') {
      // Obter dados do cache
      const cacheObject = await cacheQuery.first({ useMasterKey: true });

      if (!cacheObject) {
        return {
          success: false,
          message: "Cache não encontrado"
        };
      }

      // Verificar se o cache expirou
      const expiresAt = cacheObject.get("expiresAt");
      if (expiresAt && expiresAt < new Date()) {
        // Cache expirado, remover
        await cacheObject.destroy({ useMasterKey: true });
        return {
          success: false,
          message: "Cache expirado"
        };
      }

      return {
        success: true,
        data: cacheObject.get("data"),
        createdAt: cacheObject.get("createdAt"),
        expiresAt: expiresAt
      };
    }
    else if (action === 'set') {
      // Definir dados no cache
      if (!data) {
        throw new Error("Parâmetro 'data' é obrigatório para ação 'set'");
      }

      // Verificar se já existe um cache com esta chave
      let cacheObject = await cacheQuery.first({ useMasterKey: true });

      if (!cacheObject) {
        // Criar novo objeto de cache
        cacheObject = new Parse.Object("Cache");
        cacheObject.set("key", key);
      }

      // Definir os dados e a data de expiração
      cacheObject.set("data", data);

      if (expirationMinutes) {
        const expiresAt = new Date();
        expiresAt.setMinutes(expiresAt.getMinutes() + expirationMinutes);
        cacheObject.set("expiresAt", expiresAt);
      } else {
        // Padrão: expirar em 30 minutos
        const expiresAt = new Date();
        expiresAt.setMinutes(expiresAt.getMinutes() + 30);
        cacheObject.set("expiresAt", expiresAt);
      }

      await cacheObject.save(null, { useMasterKey: true });

      return {
        success: true,
        message: "Cache salvo com sucesso",
        expiresAt: cacheObject.get("expiresAt")
      };
    }
    else if (action === 'delete') {
      // Excluir dados do cache
      const cacheObject = await cacheQuery.first({ useMasterKey: true });

      if (!cacheObject) {
        return {
          success: false,
          message: "Cache não encontrado"
        };
      }

      await cacheObject.destroy({ useMasterKey: true });

      return {
        success: true,
        message: "Cache excluído com sucesso"
      };
    }
    else if (action === 'clear') {
      // Limpar todos os caches do usuário
      const cacheObjects = await cacheQuery.find({ useMasterKey: true });

      if (cacheObjects.length === 0) {
        return {
          success: true,
          message: "Nenhum cache encontrado"
        };
      }

      // Excluir todos os objetos de cache
      const promises = cacheObjects.map(obj => obj.destroy({ useMasterKey: true }));
      await Promise.all(promises);

      return {
        success: true,
        message: `${cacheObjects.length} caches excluídos com sucesso`
      };
    }
    else {
      throw new Error(`Ação '${action}' não suportada`);
    }
  } catch (error) {
    console.error("Erro em cacheManager:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

Parse.Cloud.define('getFilaUpdateCount', async (request) => {
  const { lastSyncTime, medicoId, consultorioId } = request.params;

  if (!lastSyncTime) {
    throw new Error('lastSyncTime é obrigatório');
  }

  const query = new Parse.Query('Fila');

  // Adicionar filtros baseados no contexto
  if (medicoId) {
    query.equalTo('medico', new Parse.Object('Medico', { objectId: medicoId }));
  } else if (consultorioId) {
    query.equalTo('consultorio', new Parse.Object('consultorio', { objectId: consultorioId }));
  }

  // Filtrar apenas registros modificados desde a última sincronização
  query.greaterThan('updatedAt', new Date(lastSyncTime));

  // Contar apenas registros relevantes
  query.containedIn('status', ['aguardando', 'em_atendimento']);

  const count = await query.count();

  return {
    count,
    timestamp: new Date().toISOString()
  };
});

Parse.Cloud.define('getUpdatedFilas', async (request) => {
  try {
    const { lastSyncTime, medicoId, consultorioId, useCache } = request.params;

    if (!lastSyncTime) {
      throw new Error('lastSyncTime é obrigatório');
    }

    if (!medicoId && !consultorioId) {
      throw new Error('medicoId ou consultorioId é obrigatório');
    }

    // Verificar se devemos usar cache
    if (useCache === true) {
      // Tentar obter do cache primeiro
      const cacheKey = `filas_${medicoId || ''}_${consultorioId || ''}_${lastSyncTime}`;

      try {
        const cacheResult = await Parse.Cloud.run('cacheManager', {
          action: 'get',
          key: cacheKey
        }, { sessionToken: request.user.getSessionToken() });

        if (cacheResult.success) {
          console.log(`Dados obtidos do cache: ${cacheKey}`);
          return cacheResult.data;
        }
      } catch (cacheError) {
        console.log(`Erro ao obter cache: ${cacheError.message}`);
        // Continuar com a consulta normal se o cache falhar
      }
    }

    const query = new Parse.Query('Fila');

    // Adicionar filtros baseados no contexto
    if (medicoId) {
      query.equalTo('medico', new Parse.Object('Medico', { objectId: medicoId }));
    } else if (consultorioId) {
      query.equalTo('consultorio', new Parse.Object('consultorio', { objectId: consultorioId }));
    }

    // Filtrar apenas registros modificados desde a última sincronização
    query.greaterThan('updatedAt', new Date(lastSyncTime));

    // Incluir objetos relacionados necessários
    query.include(['medico', 'paciente', 'consultorio']);

    // Ordenar por posição
    query.ascending('posicao');

    // Limitar o número de resultados para evitar sobrecarga
    query.limit(100);

    const results = await query.find({ useMasterKey: true });

    const response = {
      results: results.map(fila => fila.toJSON()),
      updatedAt: new Date().toISOString(),
      fromCache: false
    };

    // Salvar no cache para futuras consultas
    if (useCache === true) {
      const cacheKey = `filas_${medicoId || ''}_${consultorioId || ''}_${lastSyncTime}`;

      try {
        await Parse.Cloud.run('cacheManager', {
          action: 'set',
          key: cacheKey,
          data: response,
          expirationMinutes: 5 // Cache válido por 5 minutos
        }, { sessionToken: request.user.getSessionToken() });

        console.log(`Dados salvos no cache: ${cacheKey}`);
      } catch (cacheError) {
        console.log(`Erro ao salvar cache: ${cacheError.message}`);
        // Continuar mesmo se o cache falhar
      }
    }

    return response;
  } catch (error) {
    console.error(`Erro em getUpdatedFilas: ${error.message}`);
    throw new Parse.Error(
      Parse.Error.INTERNAL_SERVER_ERROR,
      `Erro ao buscar filas atualizadas: ${error.message}`
    );
  }
});

Parse.Cloud.define('createSecretariaWithUser', async (request) => {
  const { nome, email, telefone, cpf, consultorioId, senha } = request.params;

  if (!request.user) {
    throw new Parse.Error(Parse.Error.SESSION_MISSING, 'User needs to be authenticated');
  }

  // Verificar se o usuário tem permissão (role:hospital)
  const roles = request.user.get('roles') || [];
  if (!roles.includes('role:hospital')) {
    throw new Parse.Error(
      Parse.Error.OPERATION_FORBIDDEN,
      'Only users with role:hospital can create secretaries'
    );
  }

  try {
    // Criar usuário - usando o nome como username
    const user = new Parse.User();
    user.set('username', nome); // Usar o nome como username
    user.set('password', senha);
    user.set('email', email);
    user.set('tipo', 'secretaria');
    user.set('dataCadastro', new Date());
    user.set('isAdmin', false);
    user.set('senha_temporaria', false);
    user.set('roles', ['role:secretaria']);
    user.set('nome', nome); // Armazenar o nome também como campo separado

    // ACL para o usuário
    const userAcl = new Parse.ACL();
    userAcl.setPublicReadAccess(true);
    userAcl.setRoleReadAccess('role:hospital', true);
    userAcl.setRoleWriteAccess('role:hospital', true);
    user.setACL(userAcl);

    await user.signUp(null, { useMasterKey: true });

    // Adicionar à role:secretaria
    const roleQuery = new Parse.Query(Parse.Role);
    roleQuery.equalTo('name', 'role:secretaria');
    const secretariaRole = await roleQuery.first({ useMasterKey: true });
    if (secretariaRole) {
      secretariaRole.getUsers().add(user);
      await secretariaRole.save(null, { useMasterKey: true });
    }

    // Criar secretária
    const secretaria = new Parse.Object('Secretaria');
    secretaria.set('nome', nome);
    secretaria.set('email', email);
    secretaria.set('telefone', telefone);
    secretaria.set('cpf', cpf);
    secretaria.set('ativo', true);
    secretaria.set('dataCadastro', new Date());
    secretaria.set('user_secretaria', user);
    secretaria.set('consultorio', new Parse.Object('consultorio', { id: consultorioId }));

    // ACL para a secretária
    const secretariaAcl = new Parse.ACL();
    secretariaAcl.setRoleReadAccess('role:hospital', true);
    secretariaAcl.setRoleWriteAccess('role:hospital', true);
    secretariaAcl.setReadAccess(user.id, true);
    secretariaAcl.setWriteAccess(user.id, true);
    secretaria.setACL(secretariaAcl);

    await secretaria.save(null, { useMasterKey: true });

    // Buscar todas as secretárias do consultório
    const secretariasQuery = new Parse.Query('Secretaria')
      .equalTo('consultorio', new Parse.Object('consultorio', { id: consultorioId }))
      .include('user_secretaria');

    const secretarias = await secretariasQuery.find({ useMasterKey: true });

    return {
      success: true,
      secretaria: secretaria.toJSON(),
      user: user.toJSON(),
      allSecretarias: secretarias.map(s => s.toJSON())
    };
  } catch (error) {
    throw new Parse.Error(
      Parse.Error.INTERNAL_SERVER_ERROR,
      `Error creating secretaria: ${error.message}`
    );
  }
});

Parse.Cloud.define('setupRolesAndPermissions', async (request) => {
  if (!request.master) {
    throw new Parse.Error(Parse.Error.OPERATION_FORBIDDEN, 'Requires master key');
  }

  try {
    // 1. Criar/Atualizar Roles
    const roles = ['role:admin', 'role:hospital', 'role:secretaria', 'role:medico'];
    const roleObjects = {};

    for (const roleName of roles) {
      let role = await new Parse.Query(Parse.Role)
        .equalTo('name', roleName)
        .first({ useMasterKey: true });

      if (!role) {
        const acl = new Parse.ACL();
        acl.setPublicReadAccess(true);
        role = new Parse.Role(roleName, acl);
        await role.save(null, { useMasterKey: true });
      }
      roleObjects[roleName] = role;
    }

    // 2. Configurar ACL para _User
    const userClassPermissions = {
      find: { '*': true },
      get: { '*': true },
      create: { '*': true },
      update: { 'requiresAuthentication': true, 'role:hospital': true },
      delete: { 'role:hospital': true },
      addField: { 'requiresAuthentication': true },
      protectedFields: { '*': ['email', 'password'] }
    };

    await Parse.Schema.setClassLevelPermissions('_User', userClassPermissions);

    // 3. Configurar ACL para Secretaria
    const secretariaClassPermissions = {
      find: { 'requiresAuthentication': true },
      get: { 'requiresAuthentication': true },
      create: { 'role:hospital': true },
      update: { 'role:hospital': true },
      delete: { 'role:hospital': true },
      addField: { 'requiresAuthentication': true },
      protectedFields: { '*': [] }
    };

    await Parse.Schema.setClassLevelPermissions('Secretaria', secretariaClassPermissions);

    // 4. Atualizar usuários existentes do tipo consultorio para terem role:hospital
    const consultorioUsers = await new Parse.Query(Parse.User)
      .equalTo('tipo', 'consultorio')
      .find({ useMasterKey: true });

    for (const user of consultorioUsers) {
      const roles = user.get('roles') || [];
      if (!roles.includes('role:hospital')) {
        roles.push('role:hospital');
        user.set('roles', roles);
        await user.save(null, { useMasterKey: true });

        // Adicionar à role
        roleObjects['role:hospital'].getUsers().add(user);
        await roleObjects['role:hospital'].save(null, { useMasterKey: true });
      }
    }

    return {
      success: true,
      message: 'Roles and permissions configured successfully',
      stats: {
        rolesCreated: Object.keys(roleObjects).length,
        usersUpdated: consultorioUsers.length
      }
    };
  } catch (error) {
    console.error('Error setting up roles and permissions:', error);
    throw new Parse.Error(
      Parse.Error.INTERNAL_SERVER_ERROR,
      `Failed to setup roles and permissions: ${error.message}`
    );
  }
});

// Função para testar o envio de notificações push
Parse.Cloud.define("testarNotificacaoPush", async (request) => {
  try {
    const {
      deviceId,          // ID do dispositivo para teste
      userId,            // ID do usuário para teste
      canal,             // Canal específico para teste
      tipoDestinatario,  // Tipo de usuário (patient, doctor, etc.)
      mensagem           // Mensagem customizada (opcional)
    } = request.params;

    // Verificar pelo menos um método de entrega
    if (!deviceId && !userId && !canal) {
      throw new Error("Forneça pelo menos um destinatário: deviceId, userId ou canal");
    }

    console.log(`Iniciando teste de notificação push para: ${deviceId || userId || canal}`);

    // Mensagem de teste padrão ou personalizada
    const mensagemTeste = mensagem || "Esta é uma notificação de teste do Fila App";

    // Construir parâmetros para a função de envio
    const params = {
      titulo: "Teste de Notificação",
      mensagem: mensagemTeste,
      tipo: "teste",
      dadosAdicionais: {
        test: true,
        timestamp: Date.now(),
        app: "Fila App"
      }
    };

    // Adicionar métodos de entrega conforme especificado
    if (deviceId) {
      // Buscar instalação pelo deviceId
      const installationQuery = new Parse.Query(Parse.Installation);
      installationQuery.equalTo('installationId', deviceId);
      const installation = await installationQuery.first({ useMasterKey: true });

      if (!installation) {
        throw new Error(`Instalação com deviceId ${deviceId} não encontrada`);
      }

      // Usar o userId associado à instalação
      const targetUserId = installation.get('userId');
      if (targetUserId) {
        params.destinatarios = [targetUserId];
        console.log(`Testando notificação para usuário ${targetUserId} via deviceId ${deviceId}`);
      } else {
        throw new Error(`Instalação encontrada, mas sem userId associado`);
      }
    } else if (userId) {
      params.destinatarios = [userId];
      console.log(`Testando notificação para usuário ${userId}`);
    }

    if (canal) {
      params.canais = [canal];
      console.log(`Testando notificação para canal ${canal}`);
    }

    if (tipoDestinatario) {
      params.tipoDestinatario = tipoDestinatario;
      console.log(`Filtrando por tipo de destinatário: ${tipoDestinatario}`);
    }

    // Chamar a função principal de envio
    const resultado = await Parse.Cloud.run("enviarNotificacaoPush", params, { useMasterKey: true });

    if (resultado.success) {
      console.log(`Teste de notificação push enviado com sucesso`);

      // Adicionar informações de debug para facilitar solução de problemas
      return {
        ...resultado,
        debug: {
          enviadoPara: {
            deviceId: deviceId || null,
            userId: userId || null,
            canal: canal || null,
            tipoDestinatario: tipoDestinatario || null
          },
          dataHora: new Date().toISOString(),
          mensagem: mensagemTeste,
          parametrosEnvio: params
        }
      };
    } else {
      throw new Error(`Erro no envio do teste: ${resultado.error}`);
    }
  } catch (error) {
    console.error("Erro no teste de notificação push:", error);

    return {
      success: false,
      error: error.message,
      timestamp: Date.now()
    };
  }
});

// Função para diagnosticar problemas com notificações push
Parse.Cloud.define("diagnosticarNotificacoes", async (request) => {
  try {
    const { userId } = request.params;

    if (!userId) {
      return {
        success: false,
        error: "ID do usuário não fornecido"
      };
    }

    // Buscar instalações associadas ao usuário
    const query = new Parse.Query("_Installation");
    query.equalTo("userId", userId);
    const instalacoes = await query.find({ useMasterKey: true });

    if (instalacoes.length === 0) {
      return {
        success: false,
        error: "Nenhuma instalação encontrada para este usuário",
        userId: userId
      };
    }

    // Retornar informações de diagnóstico
    return {
      success: true,
      instalacoes: instalacoes.map(inst => ({
        objectId: inst.id,
        deviceType: inst.get("deviceType"),
        deviceToken: inst.get("deviceToken"),
        installationId: inst.get("installationId"),
        appIdentifier: inst.get("appIdentifier"),
        createdAt: inst.get("createdAt"),
        updatedAt: inst.get("updatedAt")
      }))
    };
  } catch (error) {
    console.error("Erro ao diagnosticar notificações:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

Parse.Cloud.define("registerDeviceForPush", async (request) => {
  try {
    const { deviceId, userId, userType, token, deviceType } = request.params;

    // Check required parameters
    if (!deviceId || !token || !deviceType) {
      // Instead of error, try to use what we have
      return {
        success: true,
        message: "Registro parcial realizado",
        partial: true,
        params: request.params
      };
    }

    // Rest of function remains the same
    const query = new Parse.Query("Installation");
    query.equalTo("deviceToken", deviceId);
    let installation = await query.first({ useMasterKey: true });

    if (!installation) {
      installation = new Parse.Object("Installation");
    }

    installation.set("deviceToken", deviceId);
    installation.set("deviceType", deviceType);
    installation.set("GCMSenderId", "XXXXXXXXXXXX"); // Replace with your Firebase Sender ID
    installation.set("pushType", "gcm");

    // Only set user data if provided
    if (userId) {
      installation.set("userId", userId);
    }

    if (userType) {
      installation.set("userType", userType);
    }

    // Set FCM token
    if (token) {
      installation.set("installationId", token);
    }

    // Define channels based on user
    let channels = ["global"];
    if (userId) {
      channels.push(`user_${userId}`);

      if (userType === "patient") {
        channels.push(`patient_${userId}`);
        channels.push(`test_${userId}`); // Channel for testing
      } else if (userType === "doctor") {
        channels.push(`doctor_${userId}`);
        channels.push(`test_${userId}`); // Channel for testing
      } else if (userType === "admin") {
        channels.push("admin");
        channels.push(`test_${userId}`);
      }
    }

    installation.set("channels", channels);
    await installation.save(null, { useMasterKey: true });

    return {
      success: true,
      message: "Dispositivo registrado com sucesso",
      installationId: installation.id,
      channels: channels
    };
  } catch (error) {
    console.error("Erro ao registrar dispositivo:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

Parse.Cloud.define("validatePushToken", async (request) => {
  try {
    const { token, deviceId, timestamp } = request.params;

    // Validate parameters
    if (!token) {
      // Instead of error, check if we have a deviceId to work with
      if (deviceId) {
        const query = new Parse.Query("Installation");
        query.equalTo("deviceToken", deviceId);
        const installation = await query.first({ useMasterKey: true });

        if (!installation) {
          return {
            success: false,
            message: "Instalação não encontrada"
          };
        }

        // Return info about the installation even without token
        return {
          success: true,
          isValid: false,
          message: "Token não foi fornecido, mas instalação foi verificada"
        };
      }

      return {
        success: true,
        isValid: false,
        message: "Token não fornecido"
      };
    }

    // Continue with existing validation logic
    const query = new Parse.Query("Installation");
    query.equalTo("deviceToken", token);
    const installation = await query.first({ useMasterKey: true });

    if (!installation) {
      return {
        success: true,
        isValid: false,
        message: "Token não registrado no servidor"
      };
    }

    return {
      success: true,
      isValid: true,
      message: "Token válido",
      installationId: installation.id
    };
  } catch (error) {
    console.error("Erro ao validar token:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

Parse.Cloud.define("forcarNotificacaoTeste", async (request) => {
  try {
    const { deviceId, userId, canais, tokenFCM, tipoDispositivo, mensagem } = request.params;

    // Check for the minimum required parameters
    if (!deviceId && !tokenFCM) {
      return {
        success: false,
        error: "Token FCM ou ID do dispositivo são necessários"
      };
    }

    // Create notification data
    let data = {
      alert: mensagem || `Teste de notificação (${new Date().toISOString()})`,
      sound: "default",
      badge: "Increment",
      title: "Teste de Notificação",
      body: mensagem || `Teste enviado em ${new Date().toLocaleString()}`,
      tipo: "teste"
    };

    // Determine channels
    let pushChannels = [];
    if (canais && Array.isArray(canais)) {
      pushChannels = canais;
    } else if (userId) {
      pushChannels = [`user_${userId}`, `test_${userId}`, "global"];
    } else {
      // Default channels if nothing provided
      pushChannels = ["global"];
      if (deviceId) {
        pushChannels.push(`device_${deviceId}`);
      }
    }

    // In development, we'll push to a specific device
    let push;
    if (tokenFCM) {
      // Find or create installation
      const query = new Parse.Query("Installation");
      query.equalTo("installationId", tokenFCM);
      let installation = await query.first({ useMasterKey: true });

      if (!installation) {
        installation = new Parse.Object("Installation");
        installation.set("deviceToken", deviceId || tokenFCM.substring(0, 16));
        installation.set("deviceType", tipoDispositivo || "android");
        installation.set("installationId", tokenFCM);
        installation.set("GCMSenderId", "XXXXXXXXXXXX"); // Replace with your Firebase Sender ID
        installation.set("pushType", "gcm");
        installation.set("channels", pushChannels);
        await installation.save(null, { useMasterKey: true });
      }

      push = Parse.Push.send({
        where: query,
        data: data
      }, { useMasterKey: true });
    } else {
      // Push to channels
      push = Parse.Push.send({
        channels: pushChannels,
        data: data
      }, { useMasterKey: true });
    }

    await push;
    return {
      success: true,
      message: "Notificação enviada com sucesso",
      channels: pushChannels,
      data: data
    };
  } catch (error) {
    console.error("Erro ao enviar notificação de teste:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Função para limpar instalações duplicadas ou inválidas
Parse.Cloud.define("limparInstalacoes", async (request) => {
  try {
    const { userId } = request.params;

    if (!userId) {
      return {
        success: false,
        error: "ID do usuário não fornecido"
      };
    }

    // Buscar todas as instalações do usuário
    const query = new Parse.Query("_Installation");
    query.equalTo("userId", userId);
    const instalacoes = await query.find({ useMasterKey: true });

    if (instalacoes.length === 0) {
      return {
        success: true,
        message: "Nenhuma instalação encontrada para limpar"
      };
    }

    // Manter apenas a instalação mais recente para cada deviceType
    const deviceMap = {};
    const paraRemover = [];

    for (const inst of instalacoes) {
      const deviceType = inst.get("deviceType");
      const updatedAt = inst.get("updatedAt");

      if (!deviceMap[deviceType] || deviceMap[deviceType].updatedAt < updatedAt) {
        if (deviceMap[deviceType]) {
          paraRemover.push(deviceMap[deviceType].installation);
        }
        deviceMap[deviceType] = {
          installation: inst,
          updatedAt: updatedAt
        };
      } else {
        paraRemover.push(inst);
      }
    }

    // Remover instalações duplicadas
    if (paraRemover.length > 0) {
      await Parse.Object.destroyAll(paraRemover, { useMasterKey: true });
    }

    return {
      success: true,
      message: `${paraRemover.length} instalações removidas com sucesso`
    };
  } catch (error) {
    console.error("Erro ao limpar instalações:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

Parse.Cloud.define("diagnosticarNotificacoes", async (request) => {
  try {
    const { deviceId, userId } = request.params;

    // Allow diagnosis with just deviceId
    if (!deviceId && !userId) {
      return {
        success: false,
        error: "É necessário fornecer o ID do dispositivo ou ID do usuário"
      };
    }

    // Create query
    const query = new Parse.Query("Installation");
    if (deviceId) {
      query.equalTo("deviceToken", deviceId);
    }
    if (userId) {
      query.equalTo("userId", userId);
    }

    // Get all matching installations
    const installations = await query.find({ useMasterKey: true });

    if (installations.length === 0) {
      return {
        success: true,
        message: "Nenhuma instalação encontrada",
        found: false,
        count: 0
      };
    }

    // Return diagnostic info
    const results = installations.map(inst => ({
      objectId: inst.id,
      deviceToken: inst.get("deviceToken"),
      installationId: inst.get("installationId"),
      userId: inst.get("userId"),
      channels: inst.get("channels"),
      deviceType: inst.get("deviceType"),
      createdAt: inst.createdAt,
      updatedAt: inst.updatedAt
    }));

    return {
      success: true,
      message: `${installations.length} instalação(ões) encontrada(s)`,
      found: true,
      count: installations.length,
      installations: results
    };
  } catch (error) {
    console.error("Erro ao diagnosticar notificações:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Função para reorganizar a fila após a saída de um paciente
Parse.Cloud.define("reorganizarFilaAposRemocao", async (request) => {
  try {
    const { medicoId, consultorioId, posicaoRemovida } = request.params;

    if (!medicoId || !consultorioId || !posicaoRemovida) {
      return {
        success: false,
        error: "Parâmetros incompletos: medicoId, consultorioId e posicaoRemovida são obrigatórios"
      };
    }

    // Encontrar pacientes que precisam ter suas posições ajustadas
    const queryPacientes = new Parse.Query("Fila");
    queryPacientes.equalTo("medico", { "__type": "Pointer", "className": "Medico", "objectId": medicoId });
    queryPacientes.equalTo("consultorio", { "__type": "Pointer", "className": "consultorio", "objectId": consultorioId });
    queryPacientes.equalTo("status", "aguardando");
    queryPacientes.greaterThan("posicao", posicaoRemovida);
    queryPacientes.ascending("posicao");

    const pacientes = await queryPacientes.find({ useMasterKey: true });

    if (pacientes.length === 0) {
      return {
        success: true,
        message: "Nenhum paciente precisa ser reajustado",
        count: 0
      };
    }

    console.log(`Reajustando ${pacientes.length} pacientes após remoção da posição ${posicaoRemovida}`);

    // Lista para armazenar promessas de notificações
    const notificacoes = [];

    // Reajustar as posições
    for (const paciente of pacientes) {
      const posicaoAntiga = paciente.get("posicao");
      const posicaoNova = posicaoAntiga - 1;
      const idPaciente = paciente.get("idPaciente");

      console.log(`Atualizando paciente ${idPaciente}: posição ${posicaoAntiga} -> ${posicaoNova}`);
      paciente.set("posicao", posicaoNova);
      await paciente.save(null, { useMasterKey: true });

      // Adicionar promessa de notificação para este paciente
      if (idPaciente) {
        notificacoes.push(
          Parse.Cloud.run("enviarNotificacaoPush", {
            canais: [`patient_${idPaciente}`],
            titulo: 'Sua posição na fila mudou',
            mensagem: `Sua posição na fila foi atualizada de ${posicaoAntiga} para ${posicaoNova}.`,
            tipo: 'posicao_alterada',
            dadosAdicionais: {
              badge: 1,
              sound: "default",
              category: "POSITION_CHANGE",
              fila_id: paciente.id
            }
          }, { useMasterKey: true })
        );
      }
    }

    // Processar todas as notificações em paralelo
    await Promise.all(notificacoes);

    return {
      success: true,
      message: `${pacientes.length} pacientes reajustados com sucesso`,
      count: pacientes.length
    };
  } catch (error) {
    console.error("Erro ao reorganizar fila:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Função para enviar notificações de lembrete para pacientes com base em critérios
Parse.Cloud.define("enviarLembretePacientes", async (request) => {
  try {
    const {
      medicoId,
      consultorioId,
      posicaoMaxima = 5,
      titulo = "Lembrete de Atendimento",
      mensagem = "Você está próximo de ser atendido",
      silencioso = false,
      tipo = "lembrete"
    } = request.params;

    if (!medicoId || !consultorioId) {
      return {
        success: false,
        error: "Parâmetros incompletos: medicoId e consultorioId são obrigatórios"
      };
    }

    // Buscar pacientes em espera com posição <= posicaoMaxima
    const queryPacientes = new Parse.Query("Fila");
    queryPacientes.equalTo("medico", { "__type": "Pointer", "className": "Medico", "objectId": medicoId });
    queryPacientes.equalTo("consultorio", { "__type": "Pointer", "className": "consultorio", "objectId": consultorioId });
    queryPacientes.equalTo("status", "aguardando");
    queryPacientes.lessThanOrEqualTo("posicao", posicaoMaxima);
    queryPacientes.ascending("posicao");

    const pacientes = await queryPacientes.find({ useMasterKey: true });

    if (pacientes.length === 0) {
      return {
        success: true,
        message: "Nenhum paciente encontrado para notificar",
        count: 0
      };
    }

    console.log(`Enviando lembretes para ${pacientes.length} pacientes em espera (posição <= ${posicaoMaxima})`);

    // Lista para armazenar promessas de notificações
    const notificacoes = [];
    const pacientesNotificados = [];

    // Preparar notificações para cada paciente
    for (const paciente of pacientes) {
      const idPaciente = paciente.get("idPaciente");
      const posicao = paciente.get("posicao");
      const nomePaciente = paciente.get("nome") || "Paciente";

      if (idPaciente) {
        // Personalizar mensagem com a posição
        const mensagemPersonalizada = `${mensagem}. Sua posição atual é: ${posicao}`;

        // Dados adicionais para notificação
        const dadosAdicionais = {
          badge: silencioso ? 0 : 1,
          sound: silencioso ? "" : "default",
          category: "REMINDER",
          fila_id: paciente.id,
          posicao: posicao
        };

        notificacoes.push(
          Parse.Cloud.run("enviarNotificacaoPush", {
            canais: [`patient_${idPaciente}`],
            titulo: titulo,
            mensagem: mensagemPersonalizada,
            tipo: tipo,
            silenciosa: silencioso,
            dadosAdicionais: dadosAdicionais
          }, { useMasterKey: true })
        );

        // Guardar dados do paciente para o relatório
        pacientesNotificados.push({
          id: idPaciente,
          nome: nomePaciente,
          posicao: posicao
        });

        console.log(`Preparando notificação para ${nomePaciente} (ID: ${idPaciente}) na posição ${posicao}`);
      }
    }

    // Enviar todas as notificações em paralelo
    await Promise.all(notificacoes);

    // Registrar o lote de lembretes para fins de auditoria
    try {
      const logLote = new Parse.Object("LoteNotificacoes");
      logLote.set("tipo", "lembrete");
      logLote.set("medico_id", medicoId);
      logLote.set("consultorio_id", consultorioId);
      logLote.set("quantidade", pacientesNotificados.length);
      logLote.set("data_envio", new Date());
      logLote.set("pacientes", pacientesNotificados);
      await logLote.save(null, { useMasterKey: true });
    } catch (logError) {
      console.warn("Erro ao registrar log de lote:", logError.message);
      // Não interrompe o fluxo principal
    }

    return {
      success: true,
      message: `${pacientesNotificados.length} lembretes enviados com sucesso`,
      count: pacientesNotificados.length,
      pacientes: pacientesNotificados
    };
  } catch (error) {
    console.error("Erro ao enviar lembretes:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Função para notificar todos os pacientes de um médico
Parse.Cloud.define("notificarTodosPacientes", async (request) => {
  try {
    const {
      medicoId,
      consultorioId,
      titulo = "Aviso Importante",
      mensagem,
      tipo = "aviso_geral",
      filtroStatus = ["aguardando", "em_atendimento"], // Filtrar por status, null para todos
      prioridade = "high"
    } = request.params;

    if (!medicoId || !consultorioId || !mensagem) {
      return {
        success: false,
        error: "Parâmetros incompletos: medicoId, consultorioId e mensagem são obrigatórios"
      };
    }

    // Buscar todos os pacientes deste médico/consultório
    const queryPacientes = new Parse.Query("Fila");
    queryPacientes.equalTo("medico", { "__type": "Pointer", "className": "Medico", "objectId": medicoId });
    queryPacientes.equalTo("consultorio", { "__type": "Pointer", "className": "consultorio", "objectId": consultorioId });

    // Se tiver filtro de status, aplicar
    if (filtroStatus && filtroStatus.length > 0) {
      queryPacientes.containedIn("status", filtroStatus);
    }

    // Incluir o objeto médico para ter acesso ao nome
    queryPacientes.include("medico");

    const pacientes = await queryPacientes.find({ useMasterKey: true });

    if (pacientes.length === 0) {
      return {
        success: true,
        message: "Nenhum paciente encontrado para notificar",
        count: 0
      };
    }

    console.log(`Enviando notificação em massa para ${pacientes.length} pacientes do médico ${medicoId}`);

    // Buscar informações do médico para incluir na mensagem
    let nomeMedico = "seu médico";
    const medico = pacientes[0].get("medico");
    if (medico) {
      nomeMedico = medico.get("nome") || "seu médico";
    }

    // Lista para armazenar canais de notificação e IDs de pacientes
    const canaisNotificacao = new Set();
    const idsNotificados = [];

    // Coletar os canais de notificação de todos os pacientes
    for (const paciente of pacientes) {
      const idPaciente = paciente.get("idPaciente");
      if (idPaciente) {
        canaisNotificacao.add(`patient_${idPaciente}`);
        idsNotificados.push({
          id: idPaciente,
          nome: paciente.get("nome") || "Paciente",
          posicao: paciente.get("posicao") || 0
        });
      }
    }

    if (canaisNotificacao.size === 0) {
      return {
        success: true,
        message: "Nenhum canal de notificação disponível",
        count: 0
      };
    }

    // Personalizar a mensagem com o nome do médico
    const mensagemPersonalizada = mensagem.replace(/\{nome_medico\}/g, nomeMedico);

    // Enviar notificação para todos os canais de uma vez
    const resultadoNotificacao = await Parse.Cloud.run("enviarNotificacaoPush", {
      canais: Array.from(canaisNotificacao),
      titulo: titulo,
      mensagem: mensagemPersonalizada,
      tipo: tipo,
      prioridade: prioridade,
      dadosAdicionais: {
        badge: 1,
        sound: "default",
        category: "BROADCAST",
        medico_id: medicoId,
        consultorio_id: consultorioId
      }
    }, { useMasterKey: true });

    // Registrar o envio em massa para fins de auditoria
    try {
      const logMassa = new Parse.Object("NotificacaoMassa");
      logMassa.set("tipo", tipo);
      logMassa.set("medico_id", medicoId);
      logMassa.set("consultorio_id", consultorioId);
      logMassa.set("titulo", titulo);
      logMassa.set("mensagem", mensagemPersonalizada);
      logMassa.set("quantidade", canaisNotificacao.size);
      logMassa.set("data_envio", new Date());
      logMassa.set("status", resultadoNotificacao.success ? "enviado" : "falha");

      // ACL público para permitir acesso da secretária e médico posteriormente
      const aclMassa = new Parse.ACL();
      aclMassa.setPublicReadAccess(true);
      aclMassa.setPublicWriteAccess(false);
      logMassa.setACL(aclMassa);

      await logMassa.save(null, { useMasterKey: true });
    } catch (logError) {
      console.warn("Erro ao registrar log de notificação em massa:", logError.message);
    }

    return {
      success: true,
      message: `Notificação enviada com sucesso para ${canaisNotificacao.size} pacientes`,
      count: canaisNotificacao.size,
      resultado: resultadoNotificacao,
      pacientes: idsNotificados.length
    };
  } catch (error) {
    console.error("Erro ao notificar todos os pacientes:", error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Função para buscar hospitais com paginação, busca e contagem total
Parse.Cloud.define("getHospitais", async (request) => {
  try {
    // Parâmetros de paginação e busca
    const page = request.params.page || 1;
    const limit = request.params.limit || 15;
    const searchTerm = request.params.searchTerm || '';

    // Criar query base
    const query = new Parse.Query("consultorio");

    // Adicionar filtros de busca se tiver searchTerm
    if (searchTerm && searchTerm.trim() !== '') {
      const normalizedSearch = searchTerm.trim();

      // Combinar múltiplas condições de busca com OR
      const nameQuery = new Parse.Query("consultorio");
      nameQuery.matches('nome', normalizedSearch, 'i');

      const cnpjQuery = new Parse.Query("consultorio");
      cnpjQuery.matches('cnpj', normalizedSearch, 'i');

      // Combinar as queries com OR
      query._orQuery([nameQuery, cnpjQuery]);
    }

    // Incluir o usuário associado
    query.include('user_consultorio');

    // Ordenar por nome (padrão)
    query.ascending('nome');

    // Query para contar o total sem paginação
    const countQuery = new Parse.Query("consultorio");
    if (searchTerm && searchTerm.trim() !== '') {
      const normalizedSearch = searchTerm.trim();

      const nameCountQuery = new Parse.Query("consultorio");
      nameCountQuery.matches('nome', normalizedSearch, 'i');

      const cnpjCountQuery = new Parse.Query("consultorio");
      cnpjCountQuery.matches('cnpj', normalizedSearch, 'i');

      countQuery._orQuery([nameCountQuery, cnpjCountQuery]);
    }

    // Executar a consulta com paginação
    query.skip((page - 1) * limit);
    query.limit(limit);

    // Executar as duas queries em paralelo para otimizar
    const [hospitais, totalCount] = await Promise.all([
      query.find({ useMasterKey: true }),
      countQuery.count({ useMasterKey: true })
    ]);

    // Processar os resultados para adicionar email
    const hospitaisComEmail = await Promise.all(
      hospitais.map(async (hospital) => {
        const hospitalJson = hospital.toJSON();

        // Buscar email do usuário associado
        if (hospital.get('user_consultorio')) {
          const user = hospital.get('user_consultorio');
          hospitalJson.user_email = user.get('email') || user.get('username');
        }

        return hospitalJson;
      })
    );

    return {
      success: true,
      data: hospitaisComEmail,
      total: totalCount,
      page: page,
      limit: limit,
      totalPages: Math.ceil(totalCount / limit)
    };

  } catch (error) {
    console.error(`Erro ao buscar hospitais: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
});

// Validação de CNPJ único ao salvar consultório
Parse.Cloud.beforeSave("consultorio", async (request) => {
  const consultorio = request.object;
  const cnpj = consultorio.get('cnpj');

  // Log para debug
  console.log('[consultorio][beforeSave] Iniciando validação. user:', request.user ? request.user.id : null, 'master:', request.master);

  if (!cnpj) {
    throw new Parse.Error(141, "CNPJ é obrigatório.");
  }

  // Função para verificar a validade do CNPJ
  function validarCNPJ(cnpj) {
    cnpj = cnpj.replace(/[^\d]/g, ''); // Remove caracteres não numéricos
    if (cnpj.length !== 14) return false;
    if (/^(\d)\1+$/.test(cnpj)) return false;
    let tamanho = cnpj.length - 2;
    let numeros = cnpj.substring(0, tamanho);
    const digitos = cnpj.substring(tamanho);
    let soma = 0;
    let pos = tamanho - 7;
    for (let i = tamanho; i >= 1; i--) {
      soma += numeros.charAt(tamanho - i) * pos--;
      if (pos < 2) pos = 9;
    }
    let resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);
    if (resultado != digitos.charAt(0)) return false;
    tamanho = tamanho + 1;
    numeros = cnpj.substring(0, tamanho);
    soma = 0;
    pos = tamanho - 7;
    for (let i = tamanho; i >= 1; i--) {
      soma += numeros.charAt(tamanho - i) * pos--;
      if (pos < 2) pos = 9;
    }
    resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);
    return resultado == digitos.charAt(1);
  }

  // Verificar se o CNPJ é válido
  if (!validarCNPJ(cnpj)) {
    throw new Parse.Error(142, "CNPJ inválido. Verifique os dígitos verificadores.");
  }

  // Só verificar duplicidade se o CNPJ estiver sendo alterado
  if (consultorio.dirty('cnpj')) {
    // Criar query para buscar consultórios com o mesmo CNPJ
    const query = new Parse.Query("consultorio");
    query.equalTo("cnpj", cnpj);

    // Se for uma edição, excluir o próprio objeto
    if (consultorio.id) {
      query.notEqualTo("objectId", consultorio.id);
    }

    // Verificar se já existe consultório com o mesmo CNPJ
    const count = await query.count({ useMasterKey: true });
    if (count > 0) {
      throw new Parse.Error(137, "CNPJ já cadastrado no sistema.");
    }
  }

  // --- CORREÇÃO: Não exigir usuário autenticado se for masterKey ---
  // Se não for masterKey, pode exigir autenticação (opcional)
  if (!request.master) {
    // Se quiser permitir criação pública, apenas logue
    console.log('[consultorio][beforeSave] Criação sem masterKey, user:', request.user ? request.user.id : null);
    // Se quiser bloquear criação sem usuário, descomente abaixo:
    // if (!request.user) {
    //   throw new Parse.Error(141, 'Usuário não autenticado.');
    // }
  } else {
    console.log('[consultorio][beforeSave] Criação/autorização via masterKey.');
  }
});

// Excluir usuário associado quando um consultório for excluído
Parse.Cloud.beforeDelete("consultorio", async (request) => {
  const consultorio = request.object;
  const userConsultorio = consultorio.get('user_consultorio');

  if (userConsultorio) {
    try {
      // Excluir o usuário associado
      await userConsultorio.destroy({ useMasterKey: true });
      console.log(`Usuário associado ao consultório ${consultorio.id} excluído com sucesso`);
    } catch (error) {
      console.error(`Erro ao excluir usuário associado ao consultório ${consultorio.id}:`, error);
      throw new Parse.Error(141, "Erro ao excluir usuário associado ao consultório");
    }
  }
});

// ✅ NOVA CLOUD FUNCTION: Buscar email do consultório com masterKey
Parse.Cloud.define("buscarEmailConsultorio", async (request) => {
  try {
    const consultorioId = request.params.consultorioId;

    if (!consultorioId) {
      throw new Parse.Error(141, "ID do consultório é obrigatório");
    }

    console.log(`[buscarEmailConsultorio] Buscando email para consultório: ${consultorioId}`);

    // Buscar consultório com include do usuário usando masterKey
    const query = new Parse.Query("consultorio");
    query.equalTo("objectId", consultorioId);
    query.include("user_consultorio");

    const consultorio = await query.first({ useMasterKey: true });

    if (!consultorio) {
      throw new Parse.Error(101, "Consultório não encontrado");
    }

    const userConsultorio = consultorio.get("user_consultorio");

    if (!userConsultorio) {
      console.log(`[buscarEmailConsultorio] Usuário não vinculado ao consultório ${consultorioId}`);
      return {
        success: false,
        error: "Usuário não vinculado ao consultório"
      };
    }

    // Buscar o usuário completo com masterKey para acessar todos os campos
    const userQuery = new Parse.Query(Parse.User);
    userQuery.equalTo("objectId", userConsultorio.id);

    const user = await userQuery.first({ useMasterKey: true });

    if (!user) {
      throw new Parse.Error(101, "Usuário associado não encontrado");
    }

    // Tentar diferentes campos de email
    let email = user.get("email");
    if (!email || email.trim() === "") {
      email = user.get("emailAddress");
    }
    if (!email || email.trim() === "") {
      email = user.get("username");
    }

    if (!email || email.trim() === "") {
      console.log(`[buscarEmailConsultorio] Email não encontrado para usuário ${user.id}`);
      return {
        success: false,
        error: "Email não encontrado para o usuário associado"
      };
    }

    console.log(`[buscarEmailConsultorio] Email encontrado: ${email.substring(0, 3)}***`);

    return {
      success: true,
      email: email,
      userId: user.id
    };

  } catch (error) {
    console.error(`[buscarEmailConsultorio] Erro: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
});

// ✅ NOVA CLOUD FUNCTION: Atualizar status do hospital com masterkey
Parse.Cloud.define("atualizarStatusHospital", async (request) => {
  try {
    const { hospitalId, novoStatus } = request.params;

    if (!hospitalId) {
      throw new Parse.Error(141, "ID do hospital é obrigatório");
    }

    if (typeof novoStatus !== 'boolean') {
      throw new Parse.Error(141, "Status deve ser um valor booleano");
    }

    console.log(`[atualizarStatusHospital] Atualizando hospital ${hospitalId} para status: ${novoStatus}`);

    // Buscar o hospital
    const query = new Parse.Query("consultorio");
    query.equalTo("objectId", hospitalId);

    const hospital = await query.first({ useMasterKey: true });

    if (!hospital) {
      throw new Parse.Error(101, "Hospital não encontrado");
    }

    // Atualizar o status
    hospital.set("ativo", novoStatus);

    // Salvar com masterkey
    await hospital.save(null, { useMasterKey: true });

    console.log(`[atualizarStatusHospital] Hospital ${hospitalId} atualizado com sucesso para status: ${novoStatus}`);

    return {
      success: true,
      message: novoStatus ? "Hospital ativado com sucesso!" : "Hospital desativado com sucesso!",
      hospitalId: hospitalId,
      novoStatus: novoStatus
    };

  } catch (error) {
    console.error(`[atualizarStatusHospital] Erro: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
});

// ✅ CLOUD FUNCTION: Notificar emergência do paciente
Parse.Cloud.define("notificarEmergencia", async (request) => {
  try {
    const { filaId, tipo, hospital, medico } = request.params;

    if (!filaId || !tipo) {
      return {
        success: false,
        error: "Parâmetros obrigatórios: filaId e tipo"
      };
    }

    console.log(`[notificarEmergencia] Processando emergência tipo '${tipo}' para fila ${filaId}`);

    // 1. Buscar dados da fila para obter informações do paciente
    const queryFila = new Parse.Query("Fila");
    queryFila.equalTo("objectId", filaId);
    queryFila.include(["medico", "consultorio"]);

    const fila = await queryFila.first({ useMasterKey: true });

    if (!fila) {
      console.error(`[notificarEmergencia] Fila ${filaId} não encontrada`);
      return {
        success: false,
        error: "Fila não encontrada"
      };
    }

    // 2. Obter informações detalhadas
    const pacienteNome = fila.get("nome") || "Paciente";
    const medicoObj = fila.get("medico");
    const consultorioObj = fila.get("consultorio");

    const medicoNome = medicoObj ? medicoObj.get("nome") : medico || "Médico";
    const hospitalNome = consultorioObj ? consultorioObj.get("nome") : hospital || "Hospital";

    // 3. Definir mensagem baseada no tipo de emergência
    let titulo, mensagem, prioridade;

    switch (tipo) {
      case 'sos':
        titulo = '🚨 EMERGÊNCIA SOS';
        mensagem = `ATENÇÃO: O paciente ${pacienteNome} acionou o botão de emergência durante o atendimento com Dr. ${medicoNome}. Intervenção imediata necessária!`;
        prioridade = 'critical';
        break;
      case 'denuncia':
        titulo = '⚠️ DENÚNCIA REGISTRADA';
        mensagem = `O paciente ${pacienteNome} registrou uma denúncia durante o atendimento com Dr. ${medicoNome}. Verificação necessária.`;
        prioridade = 'high';
        break;
      case 'ajuda':
        titulo = '🆘 SOLICITAÇÃO DE AJUDA';
        mensagem = `O paciente ${pacienteNome} está solicitando assistência durante o atendimento com Dr. ${medicoNome}.`;
        prioridade = 'high';
        break;
      default:
        titulo = '📢 ALERTA DO PACIENTE';
        mensagem = `O paciente ${pacienteNome} enviou um alerta durante o atendimento com Dr. ${medicoNome}.`;
        prioridade = 'medium';
    }

    // 4. Criar registro de emergência no banco
    const emergenciaObj = new Parse.Object("Emergencia");
    emergenciaObj.set("fila", fila);
    emergenciaObj.set("tipo", tipo);
    emergenciaObj.set("detalhes", mensagem);
    emergenciaObj.set("data_registro", new Date());
    emergenciaObj.set("status", "pendente");
    emergenciaObj.set("prioridade", prioridade);

    // ACL público para a secretária poder ver
    const acl = new Parse.ACL();
    acl.setPublicReadAccess(true);
    acl.setPublicWriteAccess(false);
    emergenciaObj.setACL(acl);

    await emergenciaObj.save(null, { useMasterKey: true });

    // 5. Registrar log da emergência para auditoria
    try {
      const logEmergencia = new Parse.Object("LogEmergencia");
      logEmergencia.set("fila_id", filaId);
      logEmergencia.set("tipo", tipo);
      logEmergencia.set("paciente_nome", pacienteNome);
      logEmergencia.set("medico_nome", medicoNome);
      logEmergencia.set("hospital_nome", hospitalNome);
      logEmergencia.set("data_emergencia", new Date());
      logEmergencia.set("status", "notificado");
      logEmergencia.set("prioridade", prioridade);

      await logEmergencia.save(null, { useMasterKey: true });
      console.log(`[notificarEmergencia] Log de emergência salvo com sucesso`);
    } catch (logError) {
      console.warn(`[notificarEmergencia] Erro ao salvar log: ${logError.message}`);
    }

    console.log(`[notificarEmergencia] Emergência registrada com sucesso - ID: ${emergenciaObj.id}`);

    return {
      success: true,
      message: `Emergência tipo '${tipo}' registrada com sucesso`,
      emergenciaId: emergenciaObj.id,
      dados: {
        paciente: pacienteNome,
        medico: medicoNome,
        hospital: hospitalNome,
        tipo: tipo,
        prioridade: prioridade
      }
    };

  } catch (error) {
    console.error(`[notificarEmergencia] Erro: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
});
// ==========================================
// ✅ FUNÇÕES DE TIMEZONE E DATA PARA BRASIL (GMT-3)
// ==========================================

/**
 * Obtém data/hora atual no timezone do Brasil (GMT-3)
 */
Parse.Cloud.define('getNowBrasil', async (request) => {
  try {
    const now = new Date();
    const brasilTime = new Date(now.getTime() - (3 * 60 * 60 * 1000)); // UTC-3

    return {
      success: true,
      brasilTime: brasilTime,
      iso8601: brasilTime.toISOString().replace('Z', '-03:00'),
      timestamp: brasilTime.getTime(),
      formatted: brasilTime.toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' })
    };
  } catch (error) {
    console.error('Erro ao obter data do Brasil:', error);
    return { success: false, error: error.message };
  }
});

/**
 * Verifica status do LiveQuery
 */
Parse.Cloud.define('checkLiveQueryStatus', async (request) => {
  try {
    const config = await Parse.Config.get({ useMasterKey: true });

    return {
      success: true,
      liveQueryEnabled: config.get('liveQueryEnabled') || false,
      registeredClasses: liveQueryClasses,
      serverTime: new Date(),
      brasilTime: new Date(Date.now() - (3 * 60 * 60 * 1000))
    };
  } catch (error) {
    console.error('Erro ao verificar status do LiveQuery:', error);
    return { success: false, error: error.message };
  }
});

console.log('✅ Cloud Code do Fila App carregado com timezone Brasil (GMT-3)!');

// ==========================================
// ✅ SISTEMA DE OTIMIZAÇÃO DE BANCO DE DADOS
// ==========================================

/**
 * SISTEMA DE OTIMIZAÇÃO DE BANCO DE DADOS
 * 
 * Funções para reduzir a carga no banco e melhorar performance
 */

// ✅ CLOUD FUNCTION: Buscar dados da fila otimizada
Parse.Cloud.define("buscarFilaOtimizada", async (request) => {
  try {
    const { filaId, includeRelated = true } = request.params;

    if (!filaId) {
      throw new Error("ID da fila é obrigatório");
    }

    console.log(`[DB_OPTIMIZATION] Buscando fila otimizada: ${filaId}`);

    // Query otimizada com select específico
    const query = new Parse.Query("Fila");
    query.equalTo("objectId", filaId);

    // Selecionar apenas campos necessários para reduzir transferência
    query.select([
      "posicao", "status", "tempoEstimado", "dataEntrada",
      "motivo_pausa", "data_inicio_atendimento", "medico", "paciente"
    ]);

    if (includeRelated) {
      query.include(["medico", "consultorio"]);
    }

    const fila = await query.first({ useMasterKey: true });

    if (!fila) {
      return {
        success: false,
        error: "Fila não encontrada"
      };
    }

    // Estrutura otimizada de resposta
    const result = {
      objectId: fila.id,
      posicao: fila.get("posicao") || 0,
      status: fila.get("status") || "aguardando",
      tempoEstimado: fila.get("tempoEstimado") || 0,
      dataEntrada: fila.get("dataEntrada")?.toISOString(),
      updatedAt: fila.updatedAt?.toISOString(),
      motivo_pausa: fila.get("motivo_pausa"),
      data_inicio_atendimento: fila.get("data_inicio_atendimento")?.toISOString()
    };

    // Incluir dados relacionados se solicitado
    if (includeRelated) {
      const medico = fila.get("medico");
      if (medico) {
        result.medico = {
          objectId: medico.id,
          nome: medico.get("nome"),
          especialidade: medico.get("especialidade"),
          crm: medico.get("crm")
        };

        const consultorio = medico.get("consultorio");
        if (consultorio) {
          result.consultorio = {
            objectId: consultorio.id,
            nome: consultorio.get("nome"),
            telefone: consultorio.get("telefone"),
            latitude: consultorio.get("latitude"),
            longitude: consultorio.get("longitude")
          };
        }
      }
    }

    console.log(`[DB_OPTIMIZATION] ✅ Fila ${filaId} otimizada retornada`);

    return {
      success: true,
      data: result
    };

  } catch (error) {
    console.error("[DB_OPTIMIZATION] ❌ Erro:", error.message);
    return {
      success: false,
      error: error.message
    };
  }
});

// ✅ CLOUD FUNCTION: Buscar filas do paciente em batch
Parse.Cloud.define("buscarFilasPacienteBatch", async (request) => {
  try {
    const { pacienteId, limit = 20, status } = request.params;

    if (!pacienteId) {
      throw new Error("ID do paciente é obrigatório");
    }

    console.log(`[BATCH_QUERY] Buscando filas do paciente: ${pacienteId}`);

    const query = new Parse.Query("Fila");
    query.equalTo("idPaciente", pacienteId);

    // Filtrar por status se especificado
    if (status) {
      if (Array.isArray(status)) {
        query.containedIn("status", status);
      } else {
        query.equalTo("status", status);
      }
    } else {
      // Por padrão, excluir filas já atendidas
      query.notEqualTo("status", "atendido");
    }

    query.select([
      "posicao", "status", "tempoEstimado", "dataEntrada",
      "medico", "createdAt", "updatedAt"
    ]);

    query.include(["medico"]);
    query.descending("createdAt");
    query.limit(limit);

    const filas = await query.find({ useMasterKey: true });

    const result = filas.map(fila => ({
      objectId: fila.id,
      posicao: fila.get("posicao") || 0,
      status: fila.get("status") || "aguardando",
      tempoEstimado: fila.get("tempoEstimado") || 0,
      dataEntrada: fila.get("dataEntrada")?.toISOString(),
      createdAt: fila.createdAt?.toISOString(),
      updatedAt: fila.updatedAt?.toISOString(),
      medico: {
        objectId: fila.get("medico")?.id,
        nome: fila.get("medico")?.get("nome"),
        especialidade: fila.get("medico")?.get("especialidade")
      }
    }));

    console.log(`[BATCH_QUERY] ✅ ${result.length} filas retornadas para paciente ${pacienteId}`);

    return {
      success: true,
      data: result,
      count: result.length
    };

  } catch (error) {
    console.error("[BATCH_QUERY] ❌ Erro:", error.message);
    return {
      success: false,
      error: error.message
    };
  }
});

// ✅ CLOUD FUNCTION: Batch de médicos com cache
Parse.Cloud.define("buscarMedicosBatch", async (request) => {
  try {
    const { medicoIds, useCache = true } = request.params;

    if (!medicoIds || !Array.isArray(medicoIds)) {
      throw new Error("IDs dos médicos são obrigatórios");
    }

    console.log(`[MEDICOS_BATCH] Buscando ${medicoIds.length} médicos`);

    // Cache simples baseado em timestamp
    const cacheKey = `medicos_batch_${medicoIds.sort().join('_')}`;
    const cacheExpiry = 1800; // 30 minutos

    if (useCache) {
      const cached = await _getCachedResult(cacheKey, cacheExpiry);
      if (cached) {
        console.log(`[MEDICOS_BATCH] ✅ Cache hit para ${medicoIds.length} médicos`);
        return cached;
      }
    }

    const query = new Parse.Query("medico");
    query.containedIn("objectId", medicoIds);
    query.select([
      "nome", "especialidade", "crm", "telefone", "consultorio"
    ]);
    query.include(["consultorio"]);

    const medicos = await query.find({ useMasterKey: true });

    const result = {};
    medicos.forEach(medico => {
      result[medico.id] = {
        objectId: medico.id,
        nome: medico.get("nome") || "",
        especialidade: medico.get("especialidade") || "",
        crm: medico.get("crm") || "",
        telefone: medico.get("telefone") || "",
        consultorio: medico.get("consultorio") ? {
          objectId: medico.get("consultorio").id,
          nome: medico.get("consultorio").get("nome"),
          telefone: medico.get("consultorio").get("telefone"),
          latitude: medico.get("consultorio").get("latitude"),
          longitude: medico.get("consultorio").get("longitude")
        } : null
      };
    });

    const response = {
      success: true,
      data: result,
      count: Object.keys(result).length,
      cached: false
    };

    // Salvar no cache se habilitado
    if (useCache) {
      await _setCachedResult(cacheKey, response, cacheExpiry);
    }

    console.log(`[MEDICOS_BATCH] ✅ ${Object.keys(result).length} médicos retornados`);

    return response;

  } catch (error) {
    console.error("[MEDICOS_BATCH] ❌ Erro:", error.message);
    return {
      success: false,
      error: error.message
    };
  }
});

// ✅ CLOUD FUNCTION: Verificação rápida de status
Parse.Cloud.define("verificarStatusRapido", async (request) => {
  try {
    const { filaIds } = request.params;

    if (!filaIds || !Array.isArray(filaIds)) {
      throw new Error("IDs das filas são obrigatórios");
    }

    console.log(`[STATUS_RAPIDO] Verificando status de ${filaIds.length} filas`);

    const query = new Parse.Query("Fila");
    query.containedIn("objectId", filaIds);
    query.select(["posicao", "status", "updatedAt"]);

    const filas = await query.find({ useMasterKey: true });

    const result = {};
    filas.forEach(fila => {
      result[fila.id] = {
        posicao: fila.get("posicao") || 0,
        status: fila.get("status") || "aguardando",
        updatedAt: fila.updatedAt?.toISOString()
      };
    });

    console.log(`[STATUS_RAPIDO] ✅ Status de ${Object.keys(result).length} filas verificado`);

    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error("[STATUS_RAPIDO] ❌ Erro:", error.message);
    return {
      success: false,
      error: error.message
    };
  }
});

// ✅ FUNÇÕES DE CACHE AUXILIARES

async function _getCachedResult(key, expirySeconds) {
  try {
    const query = new Parse.Query("CacheEntry");
    query.equalTo("key", key);
    query.greaterThan("expiresAt", new Date());

    const entry = await query.first({ useMasterKey: true });

    if (entry) {
      return {
        ...entry.get("data"),
        cached: true,
        cacheTimestamp: entry.get("createdAt")
      };
    }

    return null;
  } catch (error) {
    console.warn("[CACHE] Erro ao buscar cache:", error.message);
    return null;
  }
}

async function _setCachedResult(key, data, expirySeconds) {
  try {
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + expirySeconds);

    const cacheEntry = new Parse.Object("CacheEntry");
    cacheEntry.set("key", key);
    cacheEntry.set("data", data);
    cacheEntry.set("expiresAt", expiresAt);

    await cacheEntry.save(null, { useMasterKey: true });

    console.log(`[CACHE] ✅ Resultado cacheado: ${key}`);
  } catch (error) {
    console.warn("[CACHE] Erro ao salvar cache:", error.message);
  }
}

// ✅ LIMPEZA AUTOMÁTICA DE CACHE EXPIRADO
Parse.Cloud.define("limparCacheExpirado", async (request) => {
  try {
    const query = new Parse.Query("CacheEntry");
    query.lessThan("expiresAt", new Date());
    query.limit(1000); // Processar em lotes

    const expiredEntries = await query.find({ useMasterKey: true });

    if (expiredEntries.length > 0) {
      await Parse.Object.destroyAll(expiredEntries, { useMasterKey: true });
      console.log(`[CACHE_CLEANUP] ✅ ${expiredEntries.length} entradas de cache expiradas removidas`);
    }

    return {
      success: true,
      removedEntries: expiredEntries.length
    };

  } catch (error) {
    console.error("[CACHE_CLEANUP] ❌ Erro:", error.message);
    return {
      success: false,
      error: error.message
    };
  }
});

// ✅ ESTATÍSTICAS DE PERFORMANCE
Parse.Cloud.define("estatisticasPerformance", async (request) => {
  try {
    const stats = {
      timestamp: new Date().toISOString(),
    };

    // Contar entradas de cache ativas
    const cacheQuery = new Parse.Query("CacheEntry");
    cacheQuery.greaterThan("expiresAt", new Date());
    stats.activeCacheEntries = await cacheQuery.count({ useMasterKey: true });

    // Contar filas ativas
    const filaQuery = new Parse.Query("Fila");
    filaQuery.notEqualTo("status", "atendido");
    stats.activeFilas = await filaQuery.count({ useMasterKey: true });

    // Contar notificações do último dia
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    const notifQuery = new Parse.Query("NotificationLog");
    notifQuery.greaterThan("createdAt", yesterday);
    stats.notificationsLast24h = await notifQuery.count({ useMasterKey: true });

    console.log(`[PERFORMANCE_STATS] ✅ Estatísticas geradas`);

    return {
      success: true,
      stats: stats
    };

  } catch (error) {
    console.error("[PERFORMANCE_STATS] ❌ Erro:", error.message);
    return {
      success: false,
      error: error.message
    };
  }
});

// ==========================================
// ✅ SISTEMA OTIMIZADO DE NOTIFICAÇÕES DA FILA
// ==========================================

/**
 * SISTEMA OTIMIZADO DE NOTIFICAÇÕES DA FILA
 * 
 * Este arquivo contém funções específicas para gerenciamento inteligente
 * de notificações do fluxo da fila de pacientes.
 */

// ✅ CONFIGURAR notificações inteligentes para uma fila específica
Parse.Cloud.define("configurarNotificacoesInteligentes", async (request) => {
  try {
    const { fila_id, configuracao } = request.params;

    if (!fila_id || !configuracao) {
      throw new Error("Parâmetros obrigatórios: fila_id, configuracao");
    }

    console.log(`[SMART_NOTIFICATIONS] Configurando para fila: ${fila_id}`);

    // Buscar a fila para validar
    const filaQuery = new Parse.Query("Fila");
    const fila = await filaQuery.get(fila_id, { useMasterKey: true });

    if (!fila) {
      throw new Error("Fila não encontrada");
    }

    // Criar/atualizar configuração de notificações
    const configQuery = new Parse.Query("FilaNotificationConfig");
    configQuery.equalTo("filaId", fila_id);

    let config = await configQuery.first({ useMasterKey: true });

    if (!config) {
      config = new Parse.Object("FilaNotificationConfig");
      config.set("filaId", fila_id);
    }

    // Atualizar configuração
    config.set("alertPositions", configuracao.alert_positions || [1, 2, 3, 5]);
    config.set("checkInterval", configuracao.check_interval || 60);
    config.set("priorityThreshold", configuracao.priority_threshold || "normal");
    config.set("smartTimingEnabled", configuracao.smart_timing_enabled || true);
    config.set("lastUpdated", new Date());
    config.set("active", true);

    await config.save(null, { useMasterKey: true });

    console.log(`[SMART_NOTIFICATIONS] ✅ Configuração salva para fila ${fila_id}`);

    return {
      success: true,
      message: "Configuração inteligente aplicada",
      config_id: config.id
    };

  } catch (error) {
    console.error("[SMART_NOTIFICATIONS] ❌ Erro:", error.message);
    return {
      success: false,
      error: error.message
    };
  }
});

// ✅ TRIGGER OTIMIZADO: Notificações inteligentes para mudanças na fila
Parse.Cloud.afterSave("Fila", async (request) => {
  const filaObject = request.object;

  // Pular se for objeto novo
  if (!request.original) {
    return;
  }

  const posicaoAtual = filaObject.get("posicao");
  const posicaoAnterior = request.original.get("posicao");
  const statusAtual = filaObject.get("status");
  const statusAnterior = request.original.get("status");
  const idPaciente = filaObject.get("idPaciente");

  if (!idPaciente) {
    return;
  }

  try {
    console.log(`[FILA_TRIGGER] Processando mudanças para paciente ${idPaciente}`);
    console.log(`  Posição: ${posicaoAnterior} → ${posicaoAtual}`);
    console.log(`  Status: ${statusAnterior} → ${statusAtual}`);

    // Buscar configuração inteligente para esta fila
    const configQuery = new Parse.Query("FilaNotificationConfig");
    configQuery.equalTo("filaId", filaObject.id);
    configQuery.equalTo("active", true);

    const config = await configQuery.first({ useMasterKey: true });
    const alertPositions = config?.get("alertPositions") || [1, 2, 3, 5];
    const priorityThreshold = config?.get("priorityThreshold") || "normal";

    // ✅ INTELIGÊNCIA: Só enviar notificações relevantes
    const shouldNotify = await _shouldSendNotification(
      posicaoAtual, posicaoAnterior,
      statusAtual, statusAnterior,
      alertPositions
    );

    if (!shouldNotify.send) {
      console.log(`[FILA_TRIGGER] ⏭️ Notificação pulada: ${shouldNotify.reason}`);
      return;
    }

    // Buscar dados do médico para personalização
    const medico = await _getMedicoData(filaObject.get("medico"));

    // ✅ PROCESSAMENTO INTELIGENTE baseado no tipo de mudança
    if (shouldNotify.type === "position_critical") {
      await _handleCriticalPositionChange(idPaciente, posicaoAtual, medico, priorityThreshold);
    } else if (shouldNotify.type === "position_improvement") {
      await _handlePositionImprovement(idPaciente, posicaoAtual, posicaoAnterior, medico);
    } else if (shouldNotify.type === "status_change") {
      await _handleStatusChange(idPaciente, statusAtual, statusAnterior, medico);
    } else if (shouldNotify.type === "emergency") {
      await _handleEmergencyNotification(idPaciente, filaObject, medico);
    }

    // Registrar log de notificação
    await _logNotification(filaObject.id, idPaciente, shouldNotify.type, {
      posicaoAtual, posicaoAnterior, statusAtual, statusAnterior
    });

  } catch (error) {
    console.error("[FILA_TRIGGER] ❌ Erro ao processar notificação:", error);
  }
});

// ✅ DECIDIR se deve enviar notificação (IA de filtragem)
async function _shouldSendNotification(posAtual, posAnterior, statusAtual, statusAnterior, alertPositions) {
  // Mudança de status crítica
  if (statusAtual !== statusAnterior) {
    if (statusAtual === "em_atendimento") {
      return { send: true, type: "status_change", reason: "atendimento_iniciado" };
    }
    if (statusAtual === "pausado_emergencia") {
      return { send: true, type: "emergency", reason: "emergencia_detectada" };
    }
    if (statusAtual === "atendido") {
      return { send: true, type: "status_change", reason: "atendimento_finalizado" };
    }
  }

  // Mudança de posição relevante
  if (posAtual !== posAnterior && posAtual > 0) {
    // Posição crítica
    if (alertPositions.includes(posAtual)) {
      return { send: true, type: "position_critical", reason: `posicao_critica_${posAtual}` };
    }

    // Melhoria significativa (3+ posições)
    if (posAnterior && posAtual < posAnterior && (posAnterior - posAtual) >= 3) {
      return { send: true, type: "position_improvement", reason: "melhoria_significativa" };
    }

    // Melhoria para próximos 5
    if (posAnterior > 5 && posAtual <= 5) {
      return { send: true, type: "position_improvement", reason: "entrou_proximos_5" };
    }
  }

  return { send: false, reason: "mudanca_nao_relevante" };
}

// ✅ NOTIFICAÇÃO para posição crítica
async function _handleCriticalPositionChange(idPaciente, posicao, medico, priority) {
  const notifications = {
    1: {
      title: "🎯 É a sua vez!",
      message: `Você é o próximo a ser atendido pelo Dr. ${medico.nome}. Dirija-se ao consultório.`,
      priority: "high"
    },
    2: {
      title: "🚀 Quase sua vez!",
      message: `Você é o segundo da fila do Dr. ${medico.nome}. Prepare-se!`,
      priority: "high"
    },
    3: {
      title: "⚡ Entre os próximos!",
      message: `Você está na 3ª posição da fila do Dr. ${medico.nome}. Fique atento!`,
      priority: "high"
    },
    5: {
      title: "📍 Nos próximos 5!",
      message: `Você está na 5ª posição da fila do Dr. ${medico.nome}.`,
      priority: priority
    }
  };

  const notification = notifications[posicao];
  if (!notification) return;

  await Parse.Cloud.run("enviarNotificacaoPush", {
    canais: [`patient_${idPaciente}`],
    titulo: notification.title,
    mensagem: notification.message,
    tipo: "posicao_critica",
    dadosAdicionais: {
      posicao_atual: posicao,
      medico_nome: medico.nome,
      especialidade: medico.especialidade
    },
    prioridade: notification.priority
  }, { useMasterKey: true });

  console.log(`[CRITICAL_POSITION] ✅ Notificação enviada - posição ${posicao}`);
}

// ✅ NOTIFICAÇÃO para melhoria de posição
async function _handlePositionImprovement(idPaciente, posAtual, posAnterior, medico) {
  const melhoria = posAnterior - posAtual;

  let title, message;
  if (melhoria >= 5) {
    title = "🔥 Grande avanço!";
    message = `Você avançou ${melhoria} posições na fila do Dr. ${medico.nome}! Agora está na posição ${posAtual}.`;
  } else {
    title = "📈 Posição melhorou!";
    message = `Você avançou ${melhoria} posição${melhoria > 1 ? 'ões' : ''} na fila. Posição atual: ${posAtual}.`;
  }

  await Parse.Cloud.run("enviarNotificacaoPush", {
    canais: [`patient_${idPaciente}`],
    titulo: title,
    mensagem: message,
    tipo: "melhoria_posicao",
    dadosAdicionais: {
      posicao_atual: posAtual,
      posicao_anterior: posAnterior,
      melhoria: melhoria,
      medico_nome: medico.nome
    },
    prioridade: melhoria >= 5 ? "high" : "normal"
  }, { useMasterKey: true });

  console.log(`[POSITION_IMPROVEMENT] ✅ Melhoria de ${melhoria} posições notificada`);
}

// ✅ NOTIFICAÇÃO para mudança de status
async function _handleStatusChange(idPaciente, statusAtual, statusAnterior, medico) {
  const statusNotifications = {
    "em_atendimento": {
      title: "👨‍⚕️ Atendimento iniciado",
      message: `Seu atendimento com Dr. ${medico.nome} foi iniciado. Dirija-se ao consultório imediatamente.`,
      priority: "high"
    },
    "atendido": {
      title: "✅ Atendimento finalizado",
      message: `Seu atendimento com Dr. ${medico.nome} foi finalizado. Obrigado por utilizar nosso serviço!`,
      priority: "normal"
    },
    "pausado_emergencia": {
      title: "🚨 Fila pausada",
      message: "A fila foi pausada temporariamente devido a uma situação de emergência. Aguarde novas instruções.",
      priority: "high"
    }
  };

  const notification = statusNotifications[statusAtual];
  if (!notification) return;

  await Parse.Cloud.run("enviarNotificacaoPush", {
    canais: [`patient_${idPaciente}`],
    titulo: notification.title,
    mensagem: notification.message,
    tipo: "mudanca_status",
    dadosAdicionais: {
      status_atual: statusAtual,
      status_anterior: statusAnterior,
      medico_nome: medico.nome,
      especialidade: medico.especialidade
    },
    prioridade: notification.priority
  }, { useMasterKey: true });

  console.log(`[STATUS_CHANGE] ✅ Mudança de status notificada: ${statusAnterior} → ${statusAtual}`);
}

// ✅ NOTIFICAÇÃO de emergência
async function _handleEmergencyNotification(idPaciente, filaObject, medico) {
  const motivo = filaObject.get("motivo_pausa") || "situação de emergência";
  const tempoEstimado = filaObject.get("tempo_estimado_pausa");

  let message = `A fila do Dr. ${medico.nome} foi pausada devido a ${motivo}.`;
  if (tempoEstimado) {
    message += ` Tempo estimado: ${tempoEstimado} minutos.`;
  }
  message += " Você será notificado quando a fila for retomada.";

  await Parse.Cloud.run("enviarNotificacaoPush", {
    canais: [`patient_${idPaciente}`],
    titulo: "🚨 Emergência - Fila pausada",
    mensagem: message,
    tipo: "emergencia",
    dadosAdicionais: {
      motivo_pausa: motivo,
      tempo_estimado: tempoEstimado,
      medico_nome: medico.nome,
      fila_id: filaObject.id
    },
    prioridade: "high",
    silenciosa: false
  }, { useMasterKey: true });

  console.log(`[EMERGENCY] ✅ Notificação de emergência enviada: ${motivo}`);
}

// ✅ BUSCAR dados do médico
async function _getMedicoData(medicoRef) {
  try {
    if (!medicoRef) return { nome: "Médico", especialidade: "" };

    const medico = await medicoRef.fetch({ useMasterKey: true });
    return {
      nome: medico.get("nome") || "Médico",
      especialidade: medico.get("especialidade") || "",
      crm: medico.get("crm") || ""
    };
  } catch (error) {
    console.warn("[GET_MEDICO] Erro ao buscar dados do médico:", error.message);
    return { nome: "Médico", especialidade: "" };
  }
}

// ✅ REGISTRAR log de notificação
async function _logNotification(filaId, idPaciente, tipo, dados) {
  try {
    const log = new Parse.Object("NotificationLog");
    log.set("filaId", filaId);
    log.set("idPaciente", idPaciente);
    log.set("tipo", tipo);
    log.set("dados", dados);
    log.set("timestamp", new Date());
    log.set("processado", true);

    await log.save(null, { useMasterKey: true });
  } catch (error) {
    console.warn("[LOG_NOTIFICATION] Erro ao registrar log:", error.message);
  }
}

// ✅ ENVIAR notificação de teste para fila específica
Parse.Cloud.define("testarNotificacaoFila", async (request) => {
  try {
    const { idPaciente, tipo = "teste" } = request.params;

    if (!idPaciente) {
      throw new Error("ID do paciente é obrigatório");
    }

    const timestamp = new Date().toLocaleString('pt-BR');

    await Parse.Cloud.run("enviarNotificacaoPush", {
      canais: [`patient_${idPaciente}`],
      titulo: "🧪 Teste de Notificação da Fila",
      mensagem: `Esta é uma notificação de teste do sistema de fila. Enviada em ${timestamp}.`,
      tipo: tipo,
      dadosAdicionais: {
        teste: true,
        timestamp: timestamp
      },
      prioridade: "normal"
    }, { useMasterKey: true });

    console.log(`[TEST_NOTIFICATION] ✅ Teste enviado para paciente ${idPaciente}`);

    return {
      success: true,
      message: "Notificação de teste enviada com sucesso"
    };

  } catch (error) {
    console.error("[TEST_NOTIFICATION] ❌ Erro:", error.message);
    return {
      success: false,
      error: error.message
    };
  }
});

// ✅ LIMPAR configurações antigas de notificações
Parse.Cloud.define("limparConfiguracoesAntigas", async (request) => {
  try {
    const diasParaManter = request.params.dias || 7;
    const dataLimite = new Date();
    dataLimite.setDate(dataLimite.getDate() - diasParaManter);

    const query = new Parse.Query("FilaNotificationConfig");
    query.lessThan("lastUpdated", dataLimite);
    query.equalTo("active", false);

    const configs = await query.find({ useMasterKey: true });

    if (configs.length > 0) {
      await Parse.Object.destroyAll(configs, { useMasterKey: true });
      console.log(`[CLEANUP] ✅ ${configs.length} configurações antigas removidas`);
    }

    return {
      success: true,
      removidas: configs.length
    };

  } catch (error) {
    console.error("[CLEANUP] ❌ Erro:", error.message);
    return {
      success: false,
      error: error.message
    };
  }
}); 